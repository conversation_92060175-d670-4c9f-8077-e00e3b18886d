'use client';
import {
  Suspense,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { getHotelDetails } from '@/api/hotel/hotelDetails';
import { HotelDatePicker } from '@/components/datePicker';
import { useApp, useHotel } from '@/context';
// ... existing code ...

import BaseInfo from './components/BaseInfo';
import Carousel from './components/Carousel';
import Filter from './components/Filter';
import HotelDetailsNavBar from './components/HotelDetailsNavBar';
import HotelDetailsSkeleton from './components/HotelDetailsSkeleton';
import HotelInfo from './components/HotelInfo';
import RoomCard from './components/RoomCard';
import RoomCardSkeleton from './components/RoomCardSkeleton';

const HotelDetails = () => {
  const { geoInfo } = useApp();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [hotelData, setHotelData] =
    useState<API.HotelDetails.IHotelDetailsFetchResponseData>();

  // 添加滚动状态管理
  const [isScrolled, setIsScrolled] = useState(false);

  // 添加房间加载状态管理
  const [isRoomLoading, setIsRoomLoading] = useState(false);

  // 从URL读取筛选条件
  const urlFilters = useMemo(() => {
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        const parsed = JSON.parse(decodeURIComponent(filtersParam));
        return Array.isArray(parsed) ? parsed : [];
      } catch (error) {
        console.warn('URL筛选参数解析失败:', error);
        return [];
      }
    }
    return [];
  }, [searchParams]);

  // 当前选中的筛选条件 - 使用URL作为初始值
  const [selectedFilters, setSelectedFilters] = useState<string[]>(urlFilters);

  // 同步URL筛选条件到本地状态
  useEffect(() => {
    setSelectedFilters(urlFilters);
  }, [urlFilters]);

  // 日历相关状态管理
  const [showDatePicker, setShowPicker] = useState(false);
  const { hotelDates: dates, updateHotelDates } = useHotel();

  // 使用useMemo稳定hotelId值，避免不必要的重新渲染
  const hotelId = useMemo(() => searchParams.get('hotelId'), [searchParams]);

  // 使用ref记录上次的筛选条件，避免重复调用
  const lastFiltersRef = useRef<string>('');
  const isLoadingRef = useRef(false);

  // 滚动监听效果
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const threshold = 120; // 滚动阈值
      setIsScrolled(scrollTop > threshold);
    };

    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 初始检查
    handleScroll();

    // 清理事件监听器
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const fetchHotelDetails = useCallback(
    async (filters: string[] = [], isFilterChange: boolean = false) => {
      if (!hotelId) {
        console.warn('hotelId 不存在，跳过API调用');
        return;
      }

      // 生成筛选条件的唯一标识
      const filtersKey = JSON.stringify(filters.sort());

      // 如果正在加载中或筛选条件没有变化，跳过API调用
      if (isLoadingRef.current || lastFiltersRef.current === filtersKey) {
        console.log('API调用被跳过：', {
          isLoading: isLoadingRef.current,
          sameFilters: lastFiltersRef.current === filtersKey,
          lastFilters: lastFiltersRef.current,
          currentFilters: filtersKey,
        });
        return;
      }

      isLoadingRef.current = true;
      lastFiltersRef.current = filtersKey;

      // 如果是筛选或日期变化，只显示房间骨架屏
      if (isFilterChange) {
        setIsRoomLoading(true);
      }

      try {
        const requestParams = {
          arrivalDate: dates[0],
          coordinateInfo: {
            coordinateType: 'GCJ-02',
            latitude: Number(geoInfo.lat),
            longitude: Number(geoInfo.lng),
          },
          departureDate: dates[1],
          hotelId,
          filter: filters,
        };

        const { code, data } = await getHotelDetails(requestParams);
        if (code === 200) {
          setHotelData(data);
        }
      } catch (error) {
        console.error('获取酒店详情失败:', error);
      } finally {
        isLoadingRef.current = false;
        setIsRoomLoading(false);
      }
    },
    [hotelId, dates, geoInfo]
  );

  // 仅在hotelId变化时调用API（初始加载）
  useEffect(() => {
    if (hotelId) {
      // 清除之前的状态
      lastFiltersRef.current = '';
      void fetchHotelDetails([], false); // 初始加载，不是筛选变化
    }
  }, [hotelId, fetchHotelDetails]);

  // 监听日期变化，重新加载酒店详情
  useEffect(() => {
    if (hotelId) {
      // 重置筛选条件缓存并重新获取数据
      lastFiltersRef.current = '';
      setIsRoomLoading(true); // 立即设置房间加载状态
      void fetchHotelDetails([], true); // 日期变化，设置为筛选变化
    }
  }, [dates, fetchHotelDetails, hotelId]);

  const handleFilterChange = useCallback(
    (conditions: string[]) => {
      setSelectedFilters(conditions); // 保存当前选中的筛选条件

      // 更新URL参数
      const newSearchParams = new URLSearchParams(searchParams.toString());
      if (conditions.length > 0) {
        newSearchParams.set(
          'filters',
          encodeURIComponent(JSON.stringify(conditions))
        );
      } else {
        newSearchParams.delete('filters');
      }

      // 使用router.replace替换URL，不影响浏览器返回
      const newUrl = `${pathname}?${newSearchParams.toString()}`;
      router.replace(newUrl, { scroll: false });

      setIsRoomLoading(true); // 立即设置房间加载状态
      void fetchHotelDetails(conditions, true); // 筛选变化
    },
    [fetchHotelDetails, searchParams, pathname, router]
  );

  // 日历相关函数
  const handleShowPicker = useCallback(() => {
    setShowPicker(true);
  }, []);

  const handleClosePicker = useCallback(() => {
    setShowPicker(false);
  }, []);

  const handleDateChange = useCallback((dateList: string[]) => {
    updateHotelDates(dateList as [string, string]);
  }, []);

  return (
    <div>
      {hotelData ? (
        <>
          <HotelDetailsNavBar isScrolled={isScrolled} hotelData={hotelData} />
          <Carousel
            hotelImages={hotelData.hotelStaticInfo?.hotelImages ?? []}
          />
          <HotelInfo hotelData={hotelData} />
          <Filter
            filterList={hotelData.filterList ?? []}
            onFilterChange={handleFilterChange}
            arrivalDate={dates[0]}
            departureDate={dates[1]}
            onDateSelect={handleShowPicker}
            initialFilters={urlFilters}
          />
          {/* 根据房间加载状态显示骨架屏或真实内容 */}
          {isRoomLoading ? (
            <RoomCardSkeleton count={3} />
          ) : (
            <RoomCard
              baseMRoomList={hotelData.baseMRoomList ?? []}
              roomInfoList={hotelData.roomInfoList ?? []}
              unMatchRoomInfoList={hotelData.unMatchRoomInfoList ?? []}
              selectedFilters={selectedFilters}
              filterList={hotelData.filterList ?? []}
              roomKey={hotelData.roomKey ?? ''}
            />
          )}
          <BaseInfo hotelData={hotelData} />
        </>
      ) : (
        <>
          <HotelDetailsSkeleton />
        </>
      )}

      <HotelDatePicker
        visible={showDatePicker}
        value={dates}
        onSelect={handleDateChange}
        onClose={handleClosePicker}
      />
    </div>
  );
};

export default function HotelDetailsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HotelDetails />
    </Suspense>
  );
}
