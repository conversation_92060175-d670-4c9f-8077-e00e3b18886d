'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

// 定义酒店卡片组件的属性接口
interface HotelCardProps {
  /** 酒店数据 */
  hotel: API.HotelList.IHotelListItem;
  /** 点击事件回调 */
  onHotelClick?: (_hotelData: API.HotelList.IHotelListItem) => void;
}

// 评分格式化工具函数
const formatScore = (score: number): string => {
  const scoreStr = score.toString();
  if (scoreStr.indexOf('.') === -1) {
    return `${scoreStr}.0`;
  }
  return scoreStr;
};

const HotelCard = ({ hotel, onHotelClick: _onHotelClick }: HotelCardProps) => {
  const [imageLoadState, setImageLoadState] = useState<
    'hidden' | 'loaded' | 'error'
  >('hidden');
  const [showImage, setShowImage] = useState(false);

  // 处理图片加载成功
  const onImageLoad = () => {
    setImageLoadState('loaded');
    setShowImage(true);
  };

  // 处理图片加载失败
  const onImageError = () => {
    setImageLoadState('error');
    setShowImage(false);
  };

  const router = useRouter();

  const handleHotelClick = (hotelData: API.HotelList.IHotelListItem) => {
    // 跳转到酒店详情页
    router.push(`/hotel/hotelDetails?hotelId=${hotelData.hotelId}`);
  };
  // 渲染星级图标
  const renderStars = (starRate: number) =>
    Array.from({ length: starRate }, (_, index) => (
      <img
        key={index}
        src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotelList/goldStar.png"
        alt="star"
        className="w-[12px] h-[12px]"
      />
    ));

  return (
    <div
      className="flex bg-bg-card rounded-[20px] overflow-hidden my-2 p-1 cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => handleHotelClick(hotel)}
      role="button"
      tabIndex={0}
    >
      {/* 左侧酒店图片 */}
      <div className="w-[116px] flex-shrink-0 rounded-[16px] overflow-hidden">
        {imageLoadState === 'error' && (
          <div className="w-full h-full flex flex-col items-center justify-center bg-bg-light text-text-muted">
            <div className="text-2xl mb-1">📷</div>
            <div className="text-xs text-center">图片暂时无法显示</div>
          </div>
        )}

        {hotel.hotel.thumbNailUrl && (
          <div className="relative w-full h-full ">
            <img
              src={hotel.hotel.thumbNailUrl}
              alt={hotel.hotel.hotelName}
              className={`object-cover transition-opacity duration-300 ${showImage ? 'opacity-100' : 'opacity-0'} w-full h-full`}
              onLoad={onImageLoad}
              onError={onImageError}
            />
          </div>
        )}
      </div>

      {/* 右侧酒店信息 */}
      <div className="flex-1 px-3 py-1 flex flex-col justify-between min-h-[100px] min-w-0">
        {/* 酒店名称 */}
        {hotel.hotel.hotelName && (
          <div className="font-medium text-base text-text-primary leading-[1.4] mb-2 line-clamp-2">
            {hotel.hotel.hotelName}
          </div>
        )}

        {/* 评分和星级 */}
        {(hotel.hotel.review.score > 0 || hotel.hotel.starRate > 0) && (
          <div className="flex items-center mb-1.5">
            {hotel.hotel.review.score > 0 && (
              <div className="bg-primary-light rounded-md text-primary text-xs px-1 mr-1.5">
                {formatScore(hotel.hotel.review.score)}
              </div>
            )}
            {hotel.hotel.starRate > 0 && (
              <div className="flex items-center p-0.5 bg-primary-bg rounded-[4px]">
                {renderStars(hotel.hotel.starRate)}
              </div>
            )}
          </div>
        )}

        {/* 地区信息 */}
        {(hotel.hotel.districtName ?? hotel.hotel.businessZoneName) && (
          <div className="text-xs text-text-primary mb-2 flex items-center">
            {hotel.hotel.districtName && (
              <span>{hotel.hotel.districtName}</span>
            )}
            {hotel.hotel.businessZoneName && (
              <span className="flex-1 ml-4 overflow-hidden text-ellipsis whitespace-nowrap">
                靠近{hotel.hotel.businessZoneName}
              </span>
            )}
          </div>
        )}

        {/* 酒店标签 */}
        {hotel.hotel.features && (
          <div className="flex flex-wrap gap-1 h-[22px] overflow-hidden mb-2.5">
            {hotel.hotel.features.map(feature => (
              <div
                key={feature}
                className="bg-bg-light rounded-lg text-[10px] h-[22px] text-text-primary px-1.5 flex items-center justify-center"
              >
                {feature}
              </div>
            ))}
          </div>
        )}

        {/* 价格信息 */}

        <div className="flex items-baseline justify-end">
          <span className="text-[10px]">¥</span>
          <span className="text-[22px] font-medium mx-0.5">
            {hotel.lowRate}
          </span>
          <span className="text-[10px]">起</span>
        </div>
      </div>
    </div>
  );
};

export default HotelCard;
