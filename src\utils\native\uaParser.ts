/** UserAgent 解析工具 用于解析浏览器 UserAgent 中的设备和用户信息 */

/**
 * 解析UserAgent中的设备和用户信息 格式: _DreameHome_平台_版本号_uid_uuid_安全区域高度_是否在app内 例如:
 * _DreameHome_a_2.2.0.0_ZG82433_934876A101DDD5EE8710C1E59656C10235DB48FC_InAppWebView
 *
 * @param userAgent 可选的UserAgent字符串，如果不提供则从window.navigator获取（客户端）
 */
export const parseUserAgent = (userAgent?: string): UserDeviceInfo => {
  const defaultInfo: UserDeviceInfo = {
    platform: 'unknown',
    version: '',
    uid: '',
    uuid: '',
    safeAreaHeight: '',
    isInAppWebView: false,
    originalUA: '',
  };

  // 获取UserAgent字符串
  let ua: string;
  if (userAgent) {
    // 如果传入了userAgent参数，直接使用（SSR场景）
    ua = userAgent;
  } else if (typeof window !== 'undefined' && window.navigator) {
    // 如果没有传入userAgent，从window.navigator获取（客户端场景）
    ua = window.navigator.userAgent;
  } else {
    // 服务端且没有传入userAgent，返回默认值
    return defaultInfo;
  }

  defaultInfo.originalUA = ua;

  // 1. 检测追觅App
  const dreameHomeIndex = ua.indexOf('_DreameHome_');
  if (dreameHomeIndex !== -1) {
    // 提取 _DreameHome_ 后面的部分
    const dreameHomePart = ua.substring(
      dreameHomeIndex + '_DreameHome_'.length
    );
    const parts = dreameHomePart.split('_');

    if (parts.length >= 6) {
      // 解析平台 (a=android, i=ios)
      const platformCode = parts[0];
      if (platformCode === 'a') {
        defaultInfo.platform = 'android';
      } else if (platformCode === 'i') {
        defaultInfo.platform = 'ios';
      } else {
        defaultInfo.platform = 'unknown';
      }

      // 解析版本号
      defaultInfo.version = parts[1] || '';

      // 解析用户ID
      defaultInfo.uid = parts[2] || '';

      // 解析设备唯一标识
      defaultInfo.uuid = parts[3] || '';

      // 解析安全区域高度
      defaultInfo.safeAreaHeight = parts[4] || '';

      // 解析是否在App内 (InAppWebView表示在App内)
      defaultInfo.isInAppWebView = parts[5] === 'InAppWebView';
    }

    return defaultInfo;
  }

  // 2. 检测自有App (hongtu)
  if (ua.includes('hongtu')) {
    // 自有App的UA格式可能不同，这里使用基础检测
    defaultInfo.isInAppWebView = true;

    // 检测平台
    if (ua.includes('Android')) {
      defaultInfo.platform = 'android';
    } else if (
      ua.includes('iPhone') ||
      ua.includes('iPad') ||
      ua.includes('iPod')
    ) {
      defaultInfo.platform = 'ios';
    } else {
      defaultInfo.platform = 'unknown';
    }

    return defaultInfo;
  }

  // 3. 检测常规移动端
  if (
    ua.includes('Mobile') ||
    ua.includes('Android') ||
    ua.includes('iPhone') ||
    ua.includes('iPad')
  ) {
    // 检测平台
    if (ua.includes('Android')) {
      defaultInfo.platform = 'android';
    } else if (
      ua.includes('iPhone') ||
      ua.includes('iPad') ||
      ua.includes('iPod')
    ) {
      defaultInfo.platform = 'ios';
    } else {
      defaultInfo.platform = 'unknown';
    }

    // 常规移动端不在App内
    defaultInfo.isInAppWebView = false;

    return defaultInfo;
  }

  // 4. 检测桌面端
  if (
    ua.includes('Windows') ||
    ua.includes('Macintosh') ||
    ua.includes('Linux')
  ) {
    defaultInfo.platform = 'unknown';
    defaultInfo.isInAppWebView = false;
    return defaultInfo;
  }

  return defaultInfo;
};

/** 获取设备信息 这是一个便捷方法，直接返回解析后的设备信息 */
export const getDeviceInfo = (): UserDeviceInfo => parseUserAgent();

/** 检查是否在App内 */
export const isInApp = (): boolean => {
  const deviceInfo = parseUserAgent();
  return deviceInfo.isInAppWebView;
};

/** 获取平台信息 */
export const getPlatform = (): string => {
  const deviceInfo = parseUserAgent();
  return deviceInfo.platform;
};

/** 获取应用版本 */
export const getAppVersion = (): string => {
  const deviceInfo = parseUserAgent();
  return deviceInfo.version;
};

/** 获取用户ID */
export const getUserId = (): string => {
  const deviceInfo = parseUserAgent();
  return deviceInfo.uid;
};

/** 获取设备UUID */
export const getDeviceUUID = (): string => {
  const deviceInfo = parseUserAgent();
  return deviceInfo.uuid;
};

/**
 * 获取安全区域高度 - 只有在追觅App中才返回高度值，其他情况返回0
 *
 * @param userAgent 可选的UserAgent字符串，如果不提供则从window.navigator获取（客户端）
 */
export const getSafeAreaHeight = (userAgent?: string): number => {
  // 获取UserAgent字符串
  let ua: string;
  if (userAgent) {
    // 如果传入了userAgent参数，直接使用（SSR场景）
    ua = userAgent;
  } else if (typeof window !== 'undefined' && window.navigator) {
    // 如果没有传入userAgent，从window.navigator获取（客户端场景）
    ua = window.navigator.userAgent;
  } else {
    // 服务端且没有传入userAgent，返回0
    return 0;
  }

  // 检查是否是追觅App
  if (!ua.includes('_DreameHome_')) {
    return 0;
  }

  const deviceInfo = parseUserAgent(ua);
  const height = parseFloat(deviceInfo.safeAreaHeight);
  return isNaN(height) ? 0 : height;
};

/**
 * 检测是否为移动设备
 *
 * @param userAgent 可选的UserAgent字符串，如果不提供则从window.navigator获取（客户端）
 * @returns 是否为移动设备
 */
export const isMobileDevice = (userAgent?: string): boolean => {
  // 获取UserAgent字符串
  let ua: string;
  if (userAgent) {
    ua = userAgent;
  } else if (typeof window !== 'undefined' && window.navigator) {
    ua = window.navigator.userAgent;
  } else {
    return false;
  }

  // 检测移动设备
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
};

/**
 * 检测是否为Android设备
 *
 * @param userAgent 可选的UserAgent字符串，如果不提供则从window.navigator获取（客户端）
 * @returns 是否为Android设备
 */
export const isAndroidDevice = (userAgent?: string): boolean => {
  // 获取UserAgent字符串
  let ua: string;
  if (userAgent) {
    ua = userAgent;
  } else if (typeof window !== 'undefined' && window.navigator) {
    ua = window.navigator.userAgent;
  } else {
    return false;
  }

  return ua.includes('Android');
};

/**
 * 检测是否为iOS设备
 *
 * @param userAgent 可选的UserAgent字符串，如果不提供则从window.navigator获取（客户端）
 * @returns 是否为iOS设备
 */
export const isIOSDevice = (userAgent?: string): boolean => {
  // 获取UserAgent字符串
  let ua: string;
  if (userAgent) {
    ua = userAgent;
  } else if (typeof window !== 'undefined' && window.navigator) {
    ua = window.navigator.userAgent;
  } else {
    return false;
  }

  return /iPhone|iPad|iPod/i.test(ua);
};
