import { useCallback } from 'react';

import dayjs from 'dayjs';

import DatePicker from './datePicker';

import type { CalendarDayType, CalendarProps } from 'react-vant';
import './hotelDatePicker.css';

/** 日期顶部别名映射 */
const DATE_TOP_INFO_MAP: Record<string, string> = {
  [dayjs().format('YYYY-MM-DD')]: '今天',
  [dayjs().add(1, 'day').format('YYYY-MM-DD')]: '明天',
};

/** 日期显示别名白名单 */
const SHOW_DATE_TOP_INFO_DAY_TYPE: CalendarDayType[] = [
  'start',
  'middle',
  'end',
];

/** 日期底部别名映射 */
const DATE_BOTTOM_INFO_MAP: Partial<Record<CalendarDayType, string>> = {
  start: '入住',
  end: '离店',
};

const WEEKDAYS = [
  <span key="0" className="text-primary text-[16px]">
    日
  </span>,
  <span key="1" className="text-[16px]">
    一
  </span>,
  <span key="2" className="text-[16px]">
    二
  </span>,
  <span key="3" className="text-[16px]">
    三
  </span>,
  <span key="4" className="text-[16px]">
    四
  </span>,
  <span key="5" className="text-[16px]">
    五
  </span>,
  <span key="6" className="text-primary text-[16px]">
    六
  </span>,
];

const MAX_DATE = dayjs().add(360, 'day').toDate();

const HotelDatePicker = ({
  value,
  onSelect,
  ...data
}: Omit<CalendarProps, 'value' | 'onSelect'> & {
  value: string[] | null;
  onSelect?: (_value: string[]) => void;
}) => {
  const handleSelect = useCallback(
    (values: string | string[]) => {
      const dateList = values as string[];
      if (dateList.length === 2) {
        onSelect?.(dateList);
      }
    },
    [onSelect]
  );

  return (
    <DatePicker
      className="hotel-date-picker-contianer"
      value={value}
      {...data}
      onSelect={handleSelect}
      round={false}
      color="var(--btn-primary-bg)"
      type="range"
      title="请选择入住离店日期"
      maxDate={MAX_DATE}
      topInfoRender={day =>
        day.type && SHOW_DATE_TOP_INFO_DAY_TYPE.includes(day.type)
          ? DATE_TOP_INFO_MAP[dayjs(day.date).format('YYYY-MM-DD')]
          : null
      }
      bottomInfoRender={day => day.type && DATE_BOTTOM_INFO_MAP[day.type]}
      showMark={false}
      showSubtitle={false}
      showConfirm={false}
      weekdays={WEEKDAYS}
    />
  );
};

export default HotelDatePicker;
