'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import { getCityInfo, searchKeywords } from '@/api/search';
import { ActionSheet } from '@/components/actionSheet';
import { tryCatchTracker } from '@/utils/track';

import { CitySection } from './components/citySection';
import HistorySection from './components/historySection';
import {
  HotelSection,
  clearHistory,
  getHistory,
} from './components/hotelSection';
import { SearchHeader } from './components/searchHeader';

import type { SearchComponentProps } from './types';

/** 搜索组件 - 重新设计，简化逻辑 */
export const SearchComponent: React.FC<SearchComponentProps> = ({
  visible,
  onClose,
  initialCityData = null,
  searchParams = {},
  onCitySelect,
  onSearchResultSelect,
  showCitySection = true,
  initialKeyword = '',
  directSearch = false,
  placeholder = '位置/品牌/酒店名',
  currentCity,
  LocationComp,
}) => {
  // 状态管理
  const [keyword, setKeyword] = useState(initialKeyword);
  const [cityData, setCityData] = useState<Search.IGetCityInfoRes | null>(
    initialCityData
  );
  const [searchResults, setSearchResults] = useState<
    Search.ISearchResultItem[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [cityLoading, setCityLoading] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(
    directSearch || !!initialKeyword
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [history, setHistory] = useState<Search.ISearchResultItem[]>([]);

  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 获取城市数据
  const fetchCityData = useCallback(async () => {
    if (cityData) return;

    setCityLoading(true);
    try {
      const response = await getCityInfo();
      if (response.isSuccess && response.data) {
        setCityData(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch city data:', error);
    } finally {
      setCityLoading(false);
    }
  }, [cityData]);

  // 核心搜索函数 - 唯一的搜索入口
  const performSearch = useCallback(
    async (searchKeyword: string, page = 1) => {
      console.warn('🔍 Search triggered:', { keyword: searchKeyword, page });

      if (!searchKeyword.trim()) {
        setShowSearchResults(!showCitySection);
        setSearchResults([]);
        setCurrentPage(1);
        setHasMore(false);
        return;
      }

      if (page === 1) {
        console.warn('📝 Setting loading state for initial search');
        setLoading(true);
        setShowSearchResults(true);
        setSearchResults([]);
      } else {
        console.warn('📝 Setting loadingMore state for page', page);
        setLoadingMore(true);
      }

      try {
        const searchRequest: Search.ISearchKeywordsReq = {
          keyword: searchKeyword,
          cityId: searchParams.cityId ?? '',
          cityName: searchParams.cityName ?? '',
          arrivalDate: searchParams.arrivalDate ?? '',
          departureDate: searchParams.departureDate ?? '',
          types: [5],
          pageIndex: page,
          pageSize: 10,
        };

        console.warn('📤 API Request:', searchRequest);
        const response = await searchKeywords(searchRequest);
        console.warn('📥 API Response:', {
          isSuccess: response.isSuccess,
          dataLength: response.data?.records?.length || 0,
          page,
          timestamp: new Date().toISOString(),
        });

        if (response.isSuccess && response.data) {
          const newResults = response.data.records || [];

          if (page === 1) {
            console.warn(
              '📝 Setting search results for page 1, count:',
              newResults.length
            );
            setSearchResults(newResults);
          } else {
            console.warn(
              '📝 Appending search results for page',
              page,
              'count:',
              newResults.length
            );
            setSearchResults(prev => [...prev, ...newResults]);
          }

          setCurrentPage(page);
          const newHasMore = newResults.length === 10;
          console.warn('📝 Setting hasMore:', newHasMore, 'for page', page);
          setHasMore(newHasMore);
        } else {
          console.warn('⚠️ Search API failed or no data');
          if (page === 1) {
            setSearchResults([]);
          }
          setHasMore(false);
        }
      } catch (error) {
        tryCatchTracker(error, { scene: 'search', searchKeyword, page });
        if (page === 1) {
          setSearchResults([]);
        }
        setHasMore(false);
      } finally {
        if (page === 1) {
          console.warn('📝 Clearing loading state for initial search');
          setLoading(false);
        } else {
          console.warn('📝 Clearing loadingMore state for page', page);
          setLoadingMore(false);
        }
      }
    },
    [searchParams, showCitySection]
  );

  // 加载更多搜索结果
  const handleLoadMore = useCallback(async () => {
    if (
      loading ||
      loadingMore ||
      !hasMore ||
      !keyword.trim() ||
      searchResults.length === 0
    ) {
      console.warn('⏸️ Load more blocked:', {
        loading,
        loadingMore,
        hasMore,
        keyword: keyword.trim(),
        resultsLength: searchResults.length,
      });
      return;
    }
    console.warn('📄 Loading more results:', { keyword, currentPage });
    await performSearch(keyword, currentPage + 1);
  }, [
    loading,
    loadingMore,
    hasMore,
    keyword,
    currentPage,
    performSearch,
    searchResults.length,
  ]);

  // 处理关键词变化 - 简化逻辑
  const handleKeywordChange = useCallback(
    (newKeyword: string) => {
      console.warn('🔤 Keyword changed:', newKeyword);
      setKeyword(newKeyword);

      // 清除之前的防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // 如果关键词为空，立即清空结果
      if (!newKeyword.trim()) {
        setShowSearchResults(!showCitySection);
        setSearchResults([]);
        setCurrentPage(1);
        setHasMore(false);
        return;
      }

      // 设置防抖搜索
      debounceTimerRef.current = setTimeout(() => {
        console.warn('⏰ Debounced search executing for:', newKeyword);
        void performSearch(newKeyword, 1);
      }, 300);
    },
    [showCitySection, performSearch]
  );

  // 处理城市选择
  const handleCitySelect = useCallback(
    (city: Search.ICityInfo) => {
      console.warn('City selected in SearchComponent:', city);
      onCitySelect?.(city);
      onClose(); // 选择城市后关闭搜索组件
    },
    [onCitySelect, onClose]
  );

  // 处理搜索结果选择
  const handleSearchResultSelect = useCallback(
    (result: Search.ISearchResultItem) => {
      console.warn('Search result selected in SearchComponent:', result);
      onSearchResultSelect?.(result);
      onClose(); // 选择搜索结果后关闭搜索组件
    },
    [onSearchResultSelect, onClose]
  );

  // 组件显示时的初始化 - 简化逻辑
  useEffect(() => {
    if (visible) {
      console.warn('🚀 Search component opened');

      // 重置状态
      setKeyword(initialKeyword);
      setShowSearchResults(directSearch || !!initialKeyword);
      setSearchResults([]);
      setCurrentPage(1);
      setHasMore(false);
      setLoading(false);
      setLoadingMore(false); // 重置加载更多状态

      // 获取城市数据
      if (showCitySection && !cityData) {
        void fetchCityData();
      }

      // 如果有初始关键词，执行搜索
      if (initialKeyword.trim()) {
        console.warn('🔍 Initial search for:', initialKeyword);
        void performSearch(initialKeyword, 1);
      }

      // 初始化历史记录
      setHistory(getHistory());
    }
  }, [
    visible,
    showCitySection,
    cityData,
    fetchCityData,
    initialKeyword,
    directSearch,
    performSearch,
  ]);

  // 清理防抖定时器
  useEffect(
    () => () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    },
    []
  );

  // 历史记录点击
  const handleHistoryClick = (item: Search.ISearchResultItem) => {
    if (item.type === 1) {
      // 城市
      onCitySelect?.({ cityId: item.cityId, cityName: item.cityName });
      onClose();
    } else if (item.type === 5) {
      // 酒店
      onSearchResultSelect?.(item);
      onClose();
    }
  };

  return (
    <ActionSheet
      visible={visible}
      onClose={onClose}
      showCancel={false}
      className="search-component"
    >
      <div className="flex flex-col h-[85vh]">
        <SearchHeader
          keyword={keyword}
          onKeywordChange={handleKeywordChange}
          onSearch={k => performSearch(k, 1)}
          placeholder={placeholder}
          onClose={onClose}
        />
        {/* 历史记录展示 */}
        {!showCitySection && (
          <HistorySection
            history={history.filter(item => item.type === 5)}
            onClear={() => {
              clearHistory();
              setHistory([]);
            }}
            onItemClick={handleHistoryClick}
          />
        )}
        <div className="flex-1 overflow-hidden">
          {showSearchResults || directSearch ? (
            /* 搜索结果页面 */
            <HotelSection
              searchResults={searchResults}
              searchParams={searchParams}
              keyword={keyword}
              loading={loading}
              loadingMore={loadingMore}
              hasMore={hasMore}
              onLoadMore={handleLoadMore}
              onResultSelect={handleSearchResultSelect}
            />
          ) : (
            /* 城市选择页面或占位内容 */
            <>
              {showCitySection ? (
                /* 显示城市选择 */
                <>
                  {cityData ? (
                    <CitySection
                      cityData={cityData}
                      onCitySelect={handleCitySelect}
                      currentCity={currentCity}
                      LocationComp={LocationComp}
                      onHotelHistoryClick={handleHistoryClick}
                    />
                  ) : cityLoading ? (
                    <div className="flex-1 flex items-center justify-center bg-white">
                      <div className="text-sm text-gray-500">
                        加载城市数据中...
                      </div>
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-center bg-white">
                      <div className="text-sm text-gray-500">
                        加载失败，请重试
                      </div>
                    </div>
                  )}
                </>
              ) : (
                /* 不显示城市选择时的占位内容 */
                <div className="flex-1 flex items-center justify-center bg-white">
                  <div className="text-sm text-gray-500">
                    请在搜索框中输入关键词进行搜索
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </ActionSheet>
  );
};

export default SearchComponent;
