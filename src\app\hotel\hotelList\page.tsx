'use client';
import { Suspense, useCallback } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { Loading, PullRefresh } from 'react-vant';

import { HotelDatePicker } from '@/components/datePicker';
import { SearchComponent } from '@/components/search';
import { useApp, useHotel } from '@/context';
import { useLocation } from '@/hooks/useLocation';

import { getImageUrl } from '../../../utils';

import HotelCard from './components/HotelCard';
import { HotelCardSkeletonList } from './components/HotelCardSkeleton';
import NavBar from './components/NavBar';
import QuickFilter from './components/QuickFilter';
import { useHotelListData } from './hooks/useHotelListData';
import { useHotelListUrlParams } from './hooks/useHotelListUrlParams';
import { useInfiniteScroll } from './hooks/useInfiniteScroll';
import { useModalStates } from './hooks/useModalStates';

/** 空状态组件 */
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center py-16">
    <img src={getImageUrl('order/empty.png')} alt="" className="w-[196px]" />
    <h3 className="text-[#000] font-bold text-[16px] mt-[10px]">
      暂无酒店数据
    </h3>
  </div>
);
const PlaneIcon: React.FC = () => (
  <div className="flex flex-col items-center justify-center">
    <span className="w-[72px] h-[72px] block">
      <img
        src={getImageUrl('loading.gif')}
        alt="loading"
        className="w-full h-full object-contain"
      />
    </span>
  </div>
);
const HotelListPage = () => {
  // Next.js hooks
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Context hooks
  const {
    hotelDates: dates,
    updateHotelDates,
    cityInfo: hotelCityInfo,
    updateKeyword,
    updateHotelCityInfo,
  } = useHotel();
  const { geoInfo } = useApp();
  const { LocationComponent } = useLocation({});

  // Custom hooks for state and logic
  const { filters, queryText, updateUrl } = useHotelListUrlParams({
    searchParams,
    router,
    pathname,
  });

  const {
    showDatePicker,
    showSearchModal,
    handleShowDatePicker,
    handleCloseDatePicker,
    handleShowSearchModal,
    handleCloseSearchModal,
  } = useModalStates();

  const {
    hotelList,
    isLoading,
    isLoadingMore,
    isRefreshing,
    hasMore,
    error,
    onRefresh,
    loadMore,
  } = useHotelListData({
    filters,
    queryText,
    dates,
    cityInfo: hotelCityInfo,
    geoInfo,
  });

  const { loadMoreRef } = useInfiniteScroll(
    () => {
      void loadMore();
    },
    {
      hasMore,
      isLoading,
      isLoadingMore,
      isRefreshing,
    }
  );

  // Event Handlers
  const handleDateChange = useCallback(
    (dateList: string[]) => {
      updateHotelDates(dateList as [string, string]);
      handleCloseDatePicker();
    },
    [updateHotelDates, handleCloseDatePicker]
  );

  const handleSearchResultSelect = useCallback(
    (result: Search.ISearchResultItem) => {
      const newQueryText = result.itemName;
      updateUrl(filters, newQueryText);
      updateKeyword(newQueryText);
      handleCloseSearchModal();

      if (result.type === 5) {
        // TODO: Hotel - navigate to details page
      }
    },
    [filters, updateUrl, handleCloseSearchModal, updateKeyword]
  );

  const handleCitySelect = useCallback(
    (city: Search.ICityInfo) => {
      // 更新酒店城市信息
      updateHotelCityInfo({
        cityId: city.cityId,
        cityName: city.cityName,
      });
      // 清空搜索关键词
      updateKeyword('');
      // 关闭搜索弹框
      handleCloseSearchModal();
    },
    [updateHotelCityInfo, updateKeyword, handleCloseSearchModal]
  );

  const handleClearSearch = useCallback(() => {
    updateUrl(filters, '');
    updateKeyword('');
  }, [filters, updateUrl, updateKeyword]);

  const handleFilterChange = useCallback(
    (newFilters: API.HotelList.IFilter[]) => {
      updateUrl(newFilters, queryText);
    },
    [queryText, updateUrl]
  );

  // Render Logic
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar
        cityId={hotelCityInfo.cityId}
        cityName={hotelCityInfo.cityName}
        arrivalDate={dates[0]}
        departureDate={dates[1]}
        queryText={queryText}
        onDateSelect={handleShowDatePicker}
        onSearch={handleShowSearchModal}
        onClearSearch={handleClearSearch}
        onCitySelect={handleShowSearchModal}
      />

      <QuickFilter
        cityId={hotelCityInfo.cityId}
        selectedFilters={filters}
        onFilterChange={handleFilterChange}
      />

      <div className="px-2 pt-[54px]">
        <HotelDatePicker
          visible={showDatePicker}
          value={dates}
          onSelect={handleDateChange}
          onClose={handleCloseDatePicker}
        />
        <PullRefresh
          onRefresh={onRefresh}
          headHeight={64}
          pullingText={<PlaneIcon />}
          loosingText={<PlaneIcon />}
          loadingText={<PlaneIcon />}
        >
          {isLoading ? (
            <HotelCardSkeletonList count={8} />
          ) : hotelList.length > 0 ? (
            <>
              {hotelList.map(hotel => (
                <HotelCard key={hotel.hotelId} hotel={hotel} />
              ))}
              <div
                ref={loadMoreRef}
                className="flex items-center justify-center py-6"
              >
                {isLoadingMore ? (
                  <div className="flex items-center gap-2">
                    <Loading color="#8c6533" size="16px">
                      加载中...
                    </Loading>
                  </div>
                ) : (
                  !hasMore && (
                    <span className="text-[#66666e] text-sm">没有更多了</span>
                  )
                )}
              </div>
            </>
          ) : (
            <EmptyState />
          )}
        </PullRefresh>
      </div>

      <SearchComponent
        visible={showSearchModal}
        onClose={handleCloseSearchModal}
        searchParams={{
          cityId: hotelCityInfo.cityId,
          cityName: hotelCityInfo.cityName,
          arrivalDate: dates[0],
          departureDate: dates[1],
        }}
        currentCity={{
          cityId: geoInfo.cityId,
          cityName: geoInfo.cityName,
          address: geoInfo.address,
          districtName: geoInfo.districtName,
          isUserLocation: Boolean(geoInfo.lastUpdatedTime),
        }}
        LocationComp={LocationComponent}
        onSearchResultSelect={handleSearchResultSelect}
        onCitySelect={handleCitySelect}
        showCitySection
        enableSearch
      />
    </div>
  );
};

export default function HotelPageWithSuspense(props: any) {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <HotelListPage {...props} />
    </Suspense>
  );
}
