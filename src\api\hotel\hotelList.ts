import { request } from '@/utils/request';

export const getHotelList = (params: API.HotelList.IHotelListFetchRequest) =>
  request<API.HotelList.IHotelListFetchResponseData>(`/hotel/search/list/v1`, {
    method: 'POST',
    body: params,
  });

export const getHotelListFilter = (cityId: string) =>
  request<API.HotelList.IQuickFilterFetchResponseData>(
    `/hotel/search/filter/getFasterFilters/v1?cityId=${cityId}`
  );
