'use client';
import React, { useState } from 'react';

import ActionSheet from '@/components/actionSheet';

interface PriceInfoProps {
  [key: string]: any;
}

const PriceInfo: React.FC<PriceInfoProps> = data => {
  const {
    paymentTag,
    totalAmount,
    selfPayAmountDesc,
    selfPayRefundDesc,
    detailInfos,
    nightCount,
    roomCount,
  } = data;

  const [visible, setVisible] = useState(false);
  const handlePriceDeatilPop = () => {
    setVisible(true);
  };

  return (
    <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[12px]">
      <div className="flex items-center justify-between">
        <p className="text-[16px] font-medium">
          <span className="text-text-primary">{paymentTag}</span>
          <span className="text-primary ml-[12px]">￥{totalAmount}</span>
        </p>
        <div className="flex items-center" onClick={handlePriceDeatilPop}>
          <span className="text-[12px] text-primary leading-none">
            费用明细
          </span>
          <img
            className="w-[10px] h-[10px] ml-[2px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/right_g_icon.png"
            alt=""
          />
        </div>
      </div>
      {selfPayAmountDesc || selfPayRefundDesc ? (
        <div className="mt-8px">
          {!!selfPayAmountDesc && (
            <p className="text-[12px] text-[#11111E]">{selfPayAmountDesc}</p>
          )}
          {!!selfPayRefundDesc && (
            <p className="mt-[4px] text-[12px] text-[#99999E]">
              {selfPayRefundDesc}
            </p>
          )}
        </div>
      ) : null}
      <ActionSheet
        visible={visible}
        title="费用明细"
        showCancel={false}
        onClose={() => setVisible(false)}
      >
        <div className="px-[16px] pb-[30px] max-h-[60vh] overflow-y-auto [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
          <div className="flex justify-between">
            <p className="text-[18px] text[#000000] font-medium">订单金额</p>
            <p>
              <span className="text-[14px] text-[#33333E] mr-[8px] font-medium">
                {nightCount}晚、{roomCount}间共
              </span>
              <span className="text-[10px] text-[#000000] mr-[2px]">￥</span>
              <span className="text-[24px] text-[#000000] font-blod">
                {totalAmount}
              </span>
            </p>
          </div>
          <div className="mt-[12px]">
            {detailInfos?.map((item: any, index: number) => (
              <div className="flex justify-between mb-[8px]" key={index}>
                <p className="text-[12px] text-[#33333E]">
                  {item.checkInDate} {item.mealDesc}
                </p>
                <p className="text-[12px] text-[#000000]">
                  {item.price && <span>￥{item.price}</span>}
                  {item.numberOfRooms > 0 && <span>x{item.numberOfRooms}</span>}
                </p>
              </div>
            ))}
          </div>
        </div>
      </ActionSheet>
    </div>
  );
};

export default PriceInfo;
