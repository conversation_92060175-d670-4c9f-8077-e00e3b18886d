'use client';
import { useRouter } from 'next/navigation';

const Agreement = () => {
  const router = useRouter();
  const jumptoAgreement = (key: string) => {
    // router.push(`/hotel/baseInfo?hotelId=${hotelId}`);
    switch (key) {
      case 'user':
        router.push(`/agreements/user`);
        break;
      default:
        router.push(`/agreements/bookNoteInfo`);
    }
  };
  return (
    <div className="mx-[20px] my-[7px]">
      <p className="text-[12px] text-text-primary">
        请您在提交订单前仔细阅读
        <span
          className="text-primary cursor-pointer hover:opacity-80 transition-opacity duration-200"
          onClick={() => {
            jumptoAgreement('user');
          }}
        >
          《个人信息授权声明》
        </span>
        和
        <span
          className="text-primary cursor-pointer hover:opacity-80 transition-opacity duration-200"
          onClick={() => {
            jumptoAgreement('hotel');
          }}
        >
          《酒店服务条款》
        </span>
      </p>
    </div>
  );
};

export default Agreement;
