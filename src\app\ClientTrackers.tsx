'use client';
import { useEffect } from 'react';

import { usePathname } from 'next/navigation';

import { tracker } from '@/utils/track';

export default function ClientTrackers() {
  // 1. PV/UV埋点
  useEffect(() => {
    tracker({ eventType: 'pv', path: window.location.pathname });
    // 可扩展UV逻辑（如localStorage/sessionStorage标记）
  }, []);

  // 2. 路由跳转埋点
  const pathname = usePathname();
  useEffect(() => {
    tracker({ eventType: 'route', path: pathname });
  }, [pathname]);

  // 3. 全局点击事件埋点
  useEffect(() => {
    // const handleClick = (e: MouseEvent) => {
    //   tracker({
    //     eventType: 'click',
    //     path: window.location.pathname,
    //     extra: {
    //       tag: (e.target as HTMLElement)?.tagName,
    //       id: (e.target as HTMLElement)?.id,
    //       className: (e.target as HTMLElement)?.className,
    //       x: e.clientX,
    //       y: e.clientY,
    //     },
    //   });
    // };
    // document.addEventListener('click', handleClick, true);
    // return () => document.removeEventListener('click', handleClick, true);
  }, []);

  // 4. 全局JS错误埋点
  useEffect(() => {
    const onError = (event: ErrorEvent) => {
      tracker({
        eventType: 'js_error',
        message: event.message,
        stack: event.error?.stack,
        path: window.location.pathname,
      });
    };
    const onUnhandledRejection = (event: PromiseRejectionEvent) => {
      tracker({
        eventType: 'js_error',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        path: window.location.pathname,
      });
    };
    window.addEventListener('error', onError);
    window.addEventListener('unhandledrejection', onUnhandledRejection);
    return () => {
      window.removeEventListener('error', onError);
      window.removeEventListener('unhandledrejection', onUnhandledRejection);
    };
  }, []);

  return null;
}
