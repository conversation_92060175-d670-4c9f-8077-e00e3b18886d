/** 自有壳 App 接口工具 - 用于与自有原生应用进行交互 */

/** 检查自有App是否可用 */
const isOwnAppAvailable = (): boolean =>
  typeof window !== 'undefined' && !!window.App && !!window.App.isInApp();

/** 检查是否在自有壳内 */
const isInOwnApp = (_userAgent?: string): boolean => {
  if (typeof window === 'undefined') {
    return _userAgent?.includes('hongtu') ?? false;
  }
  const { userAgent } = window.navigator;
  return userAgent.includes('hongtu');
};

/** 自有壳 App 工具对象 */
export const OwnApp = {
  /**
   * 检查 App 是否可用
   *
   * @returns {boolean} App 是否可用
   */
  isAvailable: isOwnAppAvailable,

  /**
   * 检查是否在壳内
   *
   * @returns {boolean} 是否在壳内
   */
  isInApp: isInOwnApp,

  /**
   * 关闭当前页面
   *
   * @param {boolean} isClose - 是否关闭页面
   * @throws {Error} 当 App 不可用时抛出错误
   */
  closePage: (isClose: boolean): void => {
    if (!isOwnAppAvailable()) {
      throw new Error('Own App is not available');
    }
    if (typeof isClose !== 'boolean') {
      throw new Error('isClose parameter must be a boolean');
    }
    window.App?.closePage(isClose);
  },

  /**
   * 跳转到指定页面
   *
   * @param {string} url - 目标页面URL
   * @param {((url: string) => void) | undefined} customJump -
   *   自定义跳转方法，在非native环境下使用
   * @throws {Error} 当 App 不可用或 URL 无效时抛出错误
   */
  jumpPage: (url: string, customJump?: (_url: string) => void): void => {
    if (!url || typeof url !== 'string') {
      throw new Error('url parameter must be a non-empty string');
    }

    if (isOwnAppAvailable()) {
      window.App?.jumpPage(url, customJump);
      return;
    }

    if (typeof customJump === 'function') {
      customJump(url);
      return;
    }

    // 如果不在App内且没有自定义跳转方法，使用默认的页面跳转
    if (typeof window !== 'undefined') {
      window.location.href = url;
    }
  },

  /**
   * 设置页面标题
   *
   * @param {string} title - 页面标题
   * @throws {Error} 当 App 不可用或标题无效时抛出错误
   */
  setPageTitle: (title: string): void => {
    if (!isOwnAppAvailable()) {
      throw new Error('Own App is not available');
    }
    if (!title || typeof title !== 'string') {
      throw new Error('title parameter must be a non-empty string');
    }
    window.App?.setPageTitle(title);
  },

  /**
   * 获取用户 token
   *
   * @returns {string} 用户 token
   * @throws {Error} 当 App 不可用时抛出错误
   */
  getUserToken: (): string => {
    if (!isOwnAppAvailable()) {
      throw new Error('Own App is not available');
    }
    return window.App?.getUserToken() || '';
  },

  /**
   * 获取 APP 版本号
   *
   * @returns {string} APP 版本号
   * @throws {Error} 当 App 不可用时抛出错误
   */
  getAppVersion: (): string => {
    if (!isOwnAppAvailable()) {
      throw new Error('Own App is not available');
    }
    return window.App?.getAppVersion() || '';
  },

  /**
   * 返回上一页
   *
   * @throws {Error} 当 App 不可用时抛出错误
   */
  goBack: (): void => {
    if (!isOwnAppAvailable()) {
      throw new Error('Own App is not available');
    }
    window.App?.goBack();
  },
};
