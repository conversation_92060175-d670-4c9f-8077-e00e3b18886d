import { Suspense } from 'react';

import { staticBaseUrl } from '@/config';
import { isInDreameApp } from '@/server/utils';

import AuthWindow from './authWindow';
import ClientGoMMyOrder from './clientGoMyOrder';
import HomeGoBack from './goback';
import { BusinessGridClient } from './modules/businessGrid/index';
import HotelQuerier from './modules/hotel/querier';
import RecommendHotel from './modules/hotel/recommendHotels';

interface BusinessItemType {
  inactiveIcon: string;
  activeIcon: string;
  name: string;
  type: string;
}

const BUSINESS_LIST: BusinessItemType[] = [
  {
    inactiveIcon: `${staticBaseUrl}/home/<USER>
    activeIcon: `${staticBaseUrl}/home/<USER>
    name: '酒店',
    type: 'hotel',
  },
  {
    inactiveIcon: `${staticBaseUrl}/home/<USER>
    activeIcon: `${staticBaseUrl}/home/<USER>
    name: '定制游',
    type: 'travel',
  },
  {
    inactiveIcon: `${staticBaseUrl}/home/<USER>
    activeIcon: `${staticBaseUrl}/home/<USER>
    name: '机票',
    type: 'flight',
  },
  {
    inactiveIcon: `${staticBaseUrl}/home/<USER>
    activeIcon: `${staticBaseUrl}/home/<USER>
    name: '火车票',
    type: 'train',
  },
];

const bgImageUrl = `url(${staticBaseUrl}/home/<USER>
const HomePage = async () => {
  const inDreameApp = await isInDreameApp();
  return (
    <div className="px-[20px] min-h-[100vh] bg-bg-gray">
      <div
        className="absolute left-0 top-0 w-full h-[325px] bg-[length:100%_100%] bg-no-repeat bg-center"
        style={{ backgroundImage: bgImageUrl }}
      />
      <div className="pt-[57px] pb-[41px] relative">
        <div className="flex items-center justify-between h-[58px] mb-[22px]">
          <HomeGoBack />
          <div className="app-sign">
            {!inDreameApp && (
              <img
                className="w-[84px]"
                src={`${staticBaseUrl}/home/<USER>
                alt="加载失败"
              />
            )}
          </div>
          <ClientGoMMyOrder />
        </div>
        <BusinessGridClient
          businessList={BUSINESS_LIST}
          HotelQuerier={HotelQuerier}
          RecommendHotel={RecommendHotel}
        />
      </div>

      {inDreameApp && (
        <Suspense>
          <AuthWindow />
        </Suspense>
      )}
    </div>
  );
};
export default HomePage;
