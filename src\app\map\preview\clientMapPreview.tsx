'use client';
import { Suspense, useMemo } from 'react';

import { useSearchParams } from 'next/navigation';

import AMap from '@/components/amap';
import { useApp } from '@/context';

const MapPreviewContent = () => {
  const searchParams = useSearchParams();
  const params = useMemo(
    () => ({
      lat: searchParams.get('lat') ?? '',
      lng: searchParams.get('lng') ?? '',
      name: searchParams.get('name') ?? '',
    }),
    [searchParams]
  );

  const points = useMemo(
    () => [
      {
        lat: params.lat,
        lng: params.lng,
        label: params.name,
      },
    ],
    [params.lat, params.lng, params.name]
  );

  const { geoInfo } = useApp();

  const location = useMemo(
    () => ({
      longitude: geoInfo.lng,
      latitude: geoInfo.lat,
    }),
    [geoInfo]
  );

  return <AMap showLocation location={location} points={points} />;
};

const ClientMapPreview = () => (
  <Suspense fallback={<div>加载中...</div>}>
    <MapPreviewContent />
  </Suspense>
);

export default ClientMapPreview;
