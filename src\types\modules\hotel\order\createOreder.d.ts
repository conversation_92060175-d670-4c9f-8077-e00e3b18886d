declare namespace API.HotelCreateOrder {
  /** 酒店预订请求接口 */
  interface HotelCreateOrderRequest {
    /** 入住日期 (格式: YYYY-MM-DD) */
    arrivalDate: string;

    /** 离店日期 (格式: YYYY-MM-DD) */
    departureDate: string;

    /** 客户IP地址 */
    customerIPAddress: string;

    /** 预订房间数量 */
    numberOfRooms: number;

    /** 总价格 (字符串格式) */
    totalPrice: string;

    /** 入住人信息列表 */
    customers: Customer[];

    /** 联系人姓名 */
    contactName: string;

    /** 联系电话 */
    contactPhone: string;

    /** 酒店基础信息 */
    hotelBaseInfo: HotelBaseInfo;

    /** 房间基础信息 */
    roomBaseInfo: RoomBaseInfo;

    /** 房间密钥 (用于唯一标识预订) */
    roomKey: string;
  }

  /** 入住人信息 */
  interface Customer {
    /** 姓名 */
    name: string;

    /** 证件号码 */
    idCardNo: string;

    /** 证件类型 (示例: "1"-身份证，"2"-护照等，具体值参考业务文档) */
    certificateType: string;
  }

  /** 酒店基础信息 */
  interface HotelBaseInfo {
    /** 酒店ID */
    hotelId: string;

    /** 取消政策说明 */
    cancelPolicy: string;

    /** 其他可能存在的字段 (根据实际API文档补充) */
    // hotelName?: string;
    // address?: string;
  }

  /** 房间基础信息 */
  interface RoomBaseInfo {
    /** 房间ID */
    roomId: string;

    /** 房型ID */
    roomTypeId: string;

    /** 价格计划ID */
    ratePlanId: string;

    /** 其他可能存在的字段 (根据实际API文档补充) */
    // roomName?: string;
    // bedType?: string;
  }

  interface OrderBack {
    lastPayTime: number; // Assuming this is a timestamp in milliseconds
    orderNo: string; // Order number as string (long number)
    tcOrderId: string; // Third-party order ID
    totalAmount: number; // Monetary amount with decimal places
  }
}
