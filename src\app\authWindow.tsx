'use client';
import { useCallback, useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import Cookies from 'js-cookie';

import { getSilentAuthToken } from '@/api/user';
import { Toast } from '@/components';
import Dialog from '@/components/dialog';
import { useUser } from '@/context';
import { CookiesKeyEnum, StorageKeyEnum } from '@/enum';
import Native from '@/utils/native';

import { tracker } from '../utils/track';

const AuthWindow = () => {
  const router = useRouter();
  const { token, updateToken } = useUser();
  const [visible, setVisible] = useState(false);
  let timer: any = null;

  const getTokenByDmToken = async () => {
    const dmToken = Cookies.get(CookiesKeyEnum.DM_TOKEN);
    if (!dmToken) {
      Toast({
        type: 'fail',
        message: '用户认证信息参数异常',
        duration: 3000,
        customStyle: {
          width: '50vw',
        },
      });
      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        onCancel();
      }, 3000);

      tracker({
        eventType: 'info',
        extra: {
          action: 'home_auth_window_exception',
          message: '无用户认证信息',
        },
      });
      return;
    }
    const res = await getSilentAuthToken(dmToken);
    if (res.isSuccess && res.data.token) {
      updateToken(res.data.token);
      setVisible(false);
    }
  };

  useEffect(() => {
    if (token) return;
    const res = localStorage.getItem(StorageKeyEnum.AUTHORIZED);
    const accessed = res === '1';
    if (accessed) {
      // 已经授权过了，走静默登录
      void getTokenByDmToken();
    } else {
      // 未授权过，开启授权弹窗
      setVisible(true);
    }
    tracker({
      eventType: 'info',
      extra: {
        action: 'home_auth_window_accessed',
        accessed,
      },
    });
    return () => {
      clearTimeout(timer);
    };
  }, []);

  const onConfirm = async () => {
    localStorage.setItem(StorageKeyEnum.AUTHORIZED, '1');
    void getTokenByDmToken();

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_auth_window_confirm',
      },
    });
  };
  const onCancel = () => {
    tracker({
      eventType: 'click',
      extra: {
        action: 'home_auth_window_cancel',
      },
    });
    Native.closePage();
  };

  /** 跳转到隐私协议 */
  const handleToPrivacy = useCallback(() => {
    router.push('/agreements/protect');

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_auth_window_to_agreements_protect',
      },
    });
  }, []);
  const handleToUserTerms = useCallback(() => {
    router.push('/agreements/user');

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_auth_window_to_agreements_user',
      },
    });
  }, []);

  return (
    <>
      <Dialog
        visible={visible}
        maskClosable
        footer={
          <>
            <button
              className="w-[128px] h-[48px] rounded-[24px] text-[16px] bg-btn-secondary-bg text-btn-secondary-text hover:bg-btn-secondary-hover active:bg-btn-secondary-active transition-all duration-200"
              onClick={onCancel}
            >
              不同意
            </button>
            <button
              className="w-[128px] h-[48px] rounded-[24px] text-[16px] bg-btn-primary-bg text-btn-primary-text hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200 font-bold"
              onClick={onConfirm}
            >
              同意
            </button>
          </>
        }
      >
        <div
          style={{
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '16px',
          }}
        >
          请阅读并同意以下协议
        </div>
        <p className="text-left mb-[15px]">本H5页面的服务提供方为翎游</p>
        <p style={{ margin: 0 }} className="text-left">
          为保障您的个人信息安全，使用登录功能需要您阅读并同意
          <span
            className="text-primary cursor-pointer hover:opacity-80 transition-opacity duration-200"
            onClick={handleToUserTerms}
          >
            《用户服务协议》
          </span>
          、
          <span
            className="text-primary cursor-pointer hover:opacity-80 transition-opacity duration-200"
            onClick={handleToPrivacy}
          >
            《个人信息保护政策》
          </span>
        </p>
      </Dialog>
    </>
  );
};

export default AuthWindow;
