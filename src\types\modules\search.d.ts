/* eslint-disable no-unused-vars */
/** 搜索相关类型定义 */

declare namespace Search {
  /** 城市信息接口 */
  interface ICityInfo {
    /** 城市ID */
    cityId: string;
    /** 城市名称 */
    cityName: string;
    /** 国家名称 */
    countryName?: string;
    /** 城市经度 */
    cityLongitude?: string;
    /** 城市纬度 */
    cityLatitude?: string;
  }

  /** 城市分组接口 */
  interface ICityGroup {
    /** 字母 */
    letter: string;
    /** 城市信息列表 */
    cityInfos: ICityInfo[];
  }

  /** 获取城市信息响应接口 */
  interface IGetCityInfoRes {
    /** 热门城市列表 */
    hotCities: ICityInfo[];
    /** 按字母分组的城市列表 */
    cities: ICityGroup[];
  }

  /** 搜索关键词请求参数接口 */
  interface ISearchKeywordsReq {
    /** 搜索关键词 */
    keyword: string;
    /** 城市ID */
    cityId: string;
    /** 城市名称 */
    cityName: string;
    /** 入住日期 */
    arrivalDate: string;
    /** 离店日期 */
    departureDate: string;
    /** 页码 */
    pageIndex: number;
    /** 每页数量 */
    pageSize: number;
    /** 类型 */
    types: SearchItemType[];
  }

  /** 搜索结果项类型枚举 */
  enum SearchItemType {
    City = 1, // 城市
    Brand = 2, // 品牌
    District = 3, // 行政区
    BusinessZone = 4, // 商圈
    Hotel = 5, // 酒店
  }

  /** 搜索结果项接口 */
  interface ISearchResultItem {
    /** 类型 1.城市 2.品牌 3.行政区 4.商圈 5.酒店 */
    type: SearchItemType;
    /** 类型名称 */
    typeName: string;
    /** 品牌名 */
    brandName?: string;
    /** 项目名称 */
    itemName: string;
    /** 国家ID */
    countryId: string;
    /** 国家名称 */
    countryName: string;
    /** 城市ID */
    cityId: string;
    /** 城市名称 */
    cityName: string;
    /** 地址 */
    address: string;
    /** 价格 */
    itemPrice: number;
    /** 货币代码 */
    currencyCode: string;
    /** 货币符号 */
    currencySymbol: string;
    /** 行政区ID */
    district: string;
    /** 行政区名称 */
    districtName: string;
    /** 商圈ID */
    businessZone: string;
    /** 商圈名称 */
    businessZoneName: string;
    reviewScore?: number;
  }

  /** 搜索关键词响应接口 */
  interface ISearchKeywordsRes {
    /** 搜索结果列表 */
    records: ISearchResultItem[];
  }

  /** 搜索页面URL参数接口 */
  interface ISearchParams {
    /** 城市ID */
    cityId?: string;
    /** 城市名称 */
    cityName?: string;
    /** 入住日期 */
    arrivalDate?: string;
    /** 离店日期 */
    departureDate?: string;
    /** 搜索关键词 */
    keyword?: string;
    /** 是否显示城市选择 'true' | 'false' */
    showCity?: string;
    /** 是否启用搜索功能 'true' | 'false' */
    enableSearch?: string;
    /** 是否直接进入搜索模式 'true' | 'false' */
    directSearch?: string;
  }
}
