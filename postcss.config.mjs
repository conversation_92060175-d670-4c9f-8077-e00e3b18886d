const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    'postcss-pxtorem': {
      // 根字体大小，基于375px设计稿
      rootValue: 16,
      // 保留小数点后5位
      unitPrecision: 5,
      // 转换所有属性
      propList: ['*'],
      // 不转换的选择器（保持为空数组）
      selectorBlackList: [],
      // 替换而非添加rem单位
      replace: true,
      // 不转换媒体查询中的px
      mediaQuery: false,
      // 小于2px的值不转换（保留1px边框等）
      minPixelValue: 2,
      // 排除node_modules中的文件
      exclude: /node_modules/i,
    },
  },
};

export default config;
