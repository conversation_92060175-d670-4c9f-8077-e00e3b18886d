'use client';

/** 订单项骨架屏组件 模拟OrderItem的完整布局，包括头部、酒店名称、地址、日期、价格、操作按钮等区域 */
export const OrderItemSkeleton: React.FC = () => (
  <div className="bg-white rounded-[20px] px-[12px] py-[12px] mx-[8px] first:mt-0 mt-[8px]">
    {/* 头部：酒店图标 + 状态 + 删除图标 */}
    <div className="flex items-center justify-between mb-[16px]">
      <div className="flex items-center">
        {/* 酒店图标骨架 */}
        <div className="w-[16px] h-[16px] bg-gray-200 rounded mr-[8px] relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        {/* 酒店文字骨架 */}
        <div className="w-[32px] h-[12px] bg-gray-200 rounded relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>

      <div className="flex items-center">
        {/* 订单状态骨架 */}
        <div className="w-[48px] h-[12px] bg-gray-200 rounded relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        {/* 分隔符骨架 */}
        <div className="w-[1px] h-[12px] bg-gray-200 mx-[10px]" />
        {/* 删除图标骨架 */}
        <div className="w-[16px] h-[16px] bg-gray-200 rounded relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>
    </div>

    {/* 酒店名称骨架 */}
    <div className="h-[16px] bg-gray-200 rounded mb-[6px] w-3/4 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
    </div>

    {/* 地址信息骨架 */}
    <div className="flex items-center mb-[6px]">
      <div className="h-[12px] bg-gray-200 rounded w-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
      <div className="w-[4px] h-[4px] bg-gray-200 rounded mx-1" />
      <div className="h-[12px] bg-gray-200 rounded w-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
    </div>

    {/* 日期和房型信息骨架 */}
    <div className="mb-[6px]">
      <div className="h-[12px] bg-gray-200 rounded mb-1 w-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
      <div className="h-[12px] bg-gray-200 rounded w-40 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
    </div>

    {/* 价格骨架 */}
    <div className="flex items-baseline mb-[12px]">
      <div className="h-[12px] bg-gray-200 rounded w-16 mr-[5px] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
      <div className="h-[10px] bg-gray-200 rounded w-[10px] mx-[5px] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
      <div className="h-[22px] bg-gray-200 rounded w-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
      </div>
    </div>

    {/* 操作按钮骨架 */}
    <div className="flex items-end justify-between">
      <div className="flex gap-2">
        <div className="h-[32px] bg-gray-200 rounded w-16 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="h-[32px] bg-gray-200 rounded w-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>
    </div>
  </div>
);

export default OrderItemSkeleton;
