import { useCallback, useState } from 'react';

/** 弹框状态管理Hook 统一管理多个弹框的显示/隐藏状态 */
export function useModalStates() {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);

  // 日期选择器handlers
  const handleShowDatePicker = useCallback(() => {
    setShowDatePicker(true);
  }, []);

  const handleCloseDatePicker = useCallback(() => {
    setShowDatePicker(false);
  }, []);

  // 搜索弹框handlers
  const handleShowSearchModal = useCallback(() => {
    setShowSearchModal(true);
  }, []);

  const handleCloseSearchModal = useCallback(() => {
    setShowSearchModal(false);
  }, []);

  return {
    // 状态
    showDatePicker,
    showSearchModal,

    // handlers
    handleShowDatePicker,
    handleCloseDatePicker,
    handleShowSearchModal,
    handleCloseSearchModal,
  };
}
