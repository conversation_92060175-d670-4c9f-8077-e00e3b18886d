'use client';
import React from 'react';

interface HotelInvoiceProps {
  [key: string]: any;
}

const HotelInvoice: React.FC<HotelInvoiceProps> = ({ data }) => {
  const handleCall = (phoneNumber: string) => {
    window.location.href = `tel:${phoneNumber}`;
  };
  return data ? (
    <>
      <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[12px]">
        <p className="text-[16px] font-medium">
          <span className="text-[#11111E]">发票金额</span>
          <span className="text-[#568DED]">￥{data?.invoiceAmount}</span>
        </p>
        <p className="mt-[4px] text-[#99999E] text-[12px]">
          {data?.invoiceAmountDesc}
        </p>
        <p className="mt-[12px] text-[#99999E] text-[12px]">
          {data?.userShowDesc}
        </p>
        <p className="mt-[4px] text-[#99999E] text-[12px]">
          如有其他发票需求，请
          <span
            className="text-[#568DED]"
            onClick={() => handleCall(data?.hotelPhone)}
          >
            联系酒店
          </span>
          开具
        </p>
      </div>
      <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[32px] flex flex-col items-center">
        <p className="text-[14px] text-[#66666E]">{data?.pictureTitle}</p>
        <img
          className="w-[240px] h-[240px] mt-[22px]"
          src={data?.contactUrl}
          alt=""
        />
      </div>
    </>
  ) : null;
};

export default HotelInvoice;
