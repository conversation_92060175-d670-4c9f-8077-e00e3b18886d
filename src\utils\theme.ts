import { getThemeByUA, type ThemeUA } from '../themes';

// 将十六进制颜色转换为RGB格式
export const hexToRgb = (hex: string) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `${r}, ${g}, ${b}`;
};

// 根据UA获取主题UA类型
export const getThemeUAType = (userAgent: string): ThemeUA => {
  if (userAgent.includes('_DreameHome_')) {
    return 'dreame';
  }
  if (userAgent.includes('hongtu')) {
    return 'hongtu';
  }
  // return 'default';
  return 'dreame';
};

export const generateThemeConfig = () => {
  const themeColors: Record<string, string> = {
    // 主题色系（会根据环境切换）
    primary: 'var(--primary-color)',
    'primary-light': 'var(--primary-light)',
    'primary-bg': 'var(--primary-bg)',

    // 按钮色系
    'btn-primary-bg': 'var(--btn-primary-bg)',
    'btn-primary-text': 'var(--btn-primary-text)',
    'btn-primary-hover': 'var(--btn-primary-hover)',
    'btn-primary-active': 'var(--btn-primary-active)',
    'btn-secondary-bg': 'var(--btn-secondary-bg)',
    'btn-secondary-text': 'var(--btn-secondary-text)',
    'btn-secondary-hover': 'var(--btn-secondary-hover)',
    'btn-secondary-active': 'var(--btn-secondary-active)',

    // 文字色系
    'text-primary': 'var(--text-primary)',
    'text-secondary': 'var(--text-secondary)',
    'text-tertiary': 'var(--text-tertiary)',
    'text-muted': 'var(--text-muted)',

    // 背景色系
    'bg-page': 'var(--bg-page)',
    'bg-card': 'var(--bg-card)',
    'bg-gray': 'var(--bg-gray)',
    'bg-light': 'var(--bg-light)',

    // 状态色系
    success: 'var(--success)',
    warning: 'var(--warning)',
    error: 'var(--error)',
    info: 'var(--info)',

    // 支付色系
    alipay: 'var(--alipay)',
    wechat: 'var(--wechat)',
    unionpay: 'var(--unionpay)',
  };

  // 生成主题色的透明度变体
  let opacityValue = 90;
  while (opacityValue > 0) {
    themeColors[`primary-${opacityValue}`] =
      `var(--primary-color-${opacityValue})`;
    opacityValue -= 10;
  }

  return themeColors;
};

export const generateThemeVariables = (themeUA: ThemeUA) => {
  const themeConfig = getThemeByUA(themeUA);

  let primaryRgb: string;
  if (/^#/.test(themeConfig.primary)) {
    primaryRgb = hexToRgb(themeConfig.primary);
  } else {
    primaryRgb = themeConfig.primary;
  }

  const themeVariables: Record<string, string> = {
    // 主题色系
    '--primary-color': themeConfig.primary,
    '--primary-light': themeConfig.primaryLight,
    '--primary-bg': themeConfig.primaryBg,

    // 按钮色系
    '--btn-primary-bg': themeConfig.button.primary.background,
    '--btn-primary-text': themeConfig.button.primary.text,
    '--btn-primary-hover':
      themeConfig.button.primary.hover || themeConfig.button.primary.background,
    '--btn-primary-active':
      themeConfig.button.primary.active ||
      themeConfig.button.primary.background,
    '--btn-secondary-bg': themeConfig.button.secondary.background,
    '--btn-secondary-text': themeConfig.button.secondary.text,
    '--btn-secondary-hover':
      themeConfig.button.secondary.hover ||
      themeConfig.button.secondary.background,
    '--btn-secondary-active':
      themeConfig.button.secondary.active ||
      themeConfig.button.secondary.background,

    // 文字色系
    '--text-primary': themeConfig.text.primary,
    '--text-secondary': themeConfig.text.secondary,
    '--text-tertiary': themeConfig.text.tertiary,
    '--text-muted': themeConfig.text.muted,

    // 背景色系
    '--bg-page': themeConfig.background.page,
    '--bg-card': themeConfig.background.card,
    '--bg-gray': themeConfig.background.gray,
    '--bg-light': themeConfig.background.light,

    // 状态色系
    '--success': themeConfig.status.success,
    '--warning': themeConfig.status.warning,
    '--error': themeConfig.status.error,
    '--info': themeConfig.status.info,

    // 支付色系
    '--alipay': themeConfig.payment.alipay,
    '--wechat': themeConfig.payment.wechat,
    '--unionpay': themeConfig.payment.unionpay,
  };

  // 生成主题色的透明度变体
  let opacityValue = 90;
  while (opacityValue > 0) {
    themeVariables[`--primary-color-${opacityValue}`] =
      `rgba(${primaryRgb}, ${opacityValue / 100})`;
    opacityValue -= 10;
  }

  return themeVariables;
};
