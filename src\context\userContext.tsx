'use client';
import { createContext, useContext, useState, type ReactNode } from 'react';

import Cookies from 'js-cookie';

import { CookiesKeyEnum } from '../enum';

// Token过期天数常量
const TOKEN_EXPIRE_DAYS = 30;

interface UserContextType {
  token: string;
  updateToken: (_token: string) => void;
  clearToken: () => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // 兼容处理：优先读取HT-Token，如果没有值再读取token
  const getInitialToken = () => {
    const htToken = Cookies.get(CookiesKeyEnum.AUTH_TOKEN);
    const fallbackToken = Cookies.get('token');
    return htToken || fallbackToken || '';
  };

  const [token, setToken] = useState<string>(getInitialToken());

  const updateToken = (_token: string) => {
    setToken(_token);
    Cookies.set(CookiesKeyEnum.AUTH_TOKEN, _token, {
      expires: TOKEN_EXPIRE_DAYS,
    });
  };

  const clearToken = () => {
    setToken('');
    // 兼容处理：同时移除HT-Token和token
    Cookies.remove(CookiesKeyEnum.AUTH_TOKEN);
    Cookies.remove('token');
  };

  return (
    <UserContext.Provider value={{ token, updateToken, clearToken }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
