'use client';

interface RoomCardProps {
  baseMRoomList: API.HotelDetails.IBaseMRoom[];
  roomInfoList: API.HotelDetails.IRoomInfo[];
  unMatchRoomInfoList: API.HotelDetails.IRoomInfo[];
  selectedFilters: string[];
  filterList: API.HotelDetails.IFilter[];
  roomKey: string;
}

// 单个房型卡片组件
const SingleRoomCard = ({
  room,
  roomKey,
}: {
  room: {
    roomId: string;
    baseRoom: API.HotelDetails.IBaseMRoom;
    roomInfo: API.HotelDetails.IRoomInfo;
  };
  roomKey: string;
}) => {
  // const router = useRouter();

  // 处理订房按钮点击
  const handleBookingClick = (ratePlan: API.HotelDetails.IRatePlan) => {
    const params = new URLSearchParams({
      roomId: room.roomId,
      roomKey,
      ratePlanId: ratePlan.ratePlanId,
    });

    // router.push(`/hotel/order?${params.toString()}`);
    window.location.href = `/hotel/order?${params.toString()}`;
  };

  return (
    <div className="bg-bg-card mx-2 mb-2 rounded-[24px] overflow-hidden p-1">
      {/* 房型头部信息 */}
      <div className="flex mb-2">
        <div className="w-[96px] h-[96px] rounded-[20px] overflow-hidden flex-shrink-0 mr-3">
          <img
            src={room.baseRoom.roomImageUrl}
            alt="房间图片"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-[16px] font-[500] text-text-primary my-1">
            {room.roomInfo.roomInfoName}
          </h3>
          <div className="text-[12px] text-text-primary mb-1">
            {room.baseRoom.description}
          </div>
          <div className="flex w-full h-6 gap-2 py-0.5 flex-wrap overflow-hidden">
            {room.baseRoom.tags?.map((item: string, index: number) => (
              <span
                key={index}
                className="text-[10px] text-text-primary bg-bg-light px-1.5 py-1 rounded-[6px] flex-shrink-0"
              >
                {item}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* 价格选项列表 */}
      {room.roomInfo.rpList.map(
        (ratePlan: API.HotelDetails.IRatePlan, index: number) => (
          <div
            key={index}
            className="p-2 bg-bg-light rounded-[20px] mb-1 last:mb-0"
          >
            <div className="flex justify-between items-end">
              <div className="flex-1">
                {/* 早餐和取消政策 */}
                <div className="flex items-center gap-2 mb-2 text-text-primary text-[16px] font-medium">
                  <span>{ratePlan.breakDesc}</span>
                  <span className="text-[10px] w-[1px] h-[14px] bg-text-muted" />
                  <span>{ratePlan.freeCancelRuleShowDesc}</span>
                </div>

                {/* 房型信息重复 */}
                <div className="text-sm text-text-tertiary mb-3">
                  {ratePlan.saleRoomDescription}
                </div>

                {/* 政策标签 */}
                <div className="flex gap-2">
                  {ratePlan.saleRoomTags.map((item: string, index: number) => (
                    <span
                      key={index}
                      className="text-[10px] text-text-tertiary border-[#CCCCCE] border px-1.5 py-1 rounded-[8px] flex-shrink-0"
                    >
                      {item}
                    </span>
                  ))}
                </div>
                {/* 价格和预订按钮 */}
                <div className="flex float-right items-center ml-4 text-text-primary">
                  <div className="text-[10px] mt-1 mr-1">
                    {ratePlan.currencySymbol}
                  </div>
                  <div className="text-2xl font-semibold mr-2">
                    {ratePlan.averageRate}
                  </div>
                  <button
                    onClick={() => handleBookingClick(ratePlan)}
                    className="bg-btn-primary-bg text-btn-primary-text h-[48px] w-[48px] rounded-lg text-[20px] font-medium hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200"
                  >
                    订
                  </button>
                </div>
              </div>
            </div>
          </div>
        )
      )}
    </div>
  );
};

const RoomCard = ({
  baseMRoomList,
  roomInfoList,
  unMatchRoomInfoList,
  selectedFilters,
  filterList,
  roomKey,
}: RoomCardProps) => {
  // 将筛选ID映射为筛选名称
  const getFilterNames = (filterIds: string[]): string[] => {
    const filterMap = new Map<string, string>();

    // 构建筛选ID到名称的映射
    filterList.forEach(filter => {
      filter.subFilters.forEach(subFilter => {
        filterMap.set(subFilter.filterId.toString(), subFilter.name);
      });
    });

    // 将筛选ID转换为名称
    return filterIds
      .map(id => filterMap.get(id))
      .filter((name): name is string => Boolean(name));
  };
  // 融合符合条件的房型数据
  const mergedMatchedRooms = baseMRoomList
    .map(baseRoom => {
      const matchingRoomInfo = roomInfoList.find(
        roomInfo => roomInfo.roomId === baseRoom.roomId
      );
      return {
        roomId: baseRoom.roomId,
        baseRoom,
        roomInfo: matchingRoomInfo,
      };
    })
    .filter(
      (
        room
      ): room is {
        roomInfo: API.HotelDetails.IRoomInfo;
        baseRoom: API.HotelDetails.IBaseMRoom;
        roomId: string;
      } => Boolean(room.roomInfo)
    );

  // 融合不符合条件的房型数据
  const mergedUnMatchedRooms = baseMRoomList
    .map(baseRoom => {
      const matchingRoomInfo = unMatchRoomInfoList.find(
        roomInfo => roomInfo.roomId === baseRoom.roomId
      );
      return {
        roomId: baseRoom.roomId,
        baseRoom,
        roomInfo: matchingRoomInfo,
      };
    })
    .filter(
      (
        room
      ): room is {
        roomInfo: API.HotelDetails.IRoomInfo;
        baseRoom: API.HotelDetails.IBaseMRoom;
        roomId: string;
      } => Boolean(room.roomInfo)
    );

  return (
    <div className="bg-gray-50">
      {/* 符合条件的房型 */}
      {mergedMatchedRooms.map(room => (
        <SingleRoomCard key={room.roomId} room={room} roomKey={roomKey} />
      ))}

      {/* 不符合条件的房型提示和展示 */}
      {mergedUnMatchedRooms.length > 0 && (
        <>
          {/* 提示文案 */}
          <div className="mx-4 pt-3 pb-2 flex items-center">
            <div className="flex items-center text-[#33333E] text-sm">
              <div className="w-4 h-4 rounded-full border border-[#33333E] flex items-center justify-center mr-2 flex-shrink-0">
                <span className="text-xs">!</span>
              </div>
              <span>
                不满足&ldquo;{getFilterNames(selectedFilters).join('、')}
                &rdquo;筛选条件的房型
              </span>
            </div>
          </div>

          {/* 不符合条件的房型卡片 */}
          {mergedUnMatchedRooms.map(room => (
            <SingleRoomCard
              key={`unmatch-${room.roomId}`}
              room={room}
              roomKey={roomKey}
            />
          ))}
        </>
      )}
    </div>
  );
};

export default RoomCard;
