'use client';
import type { RootState } from '@/store';
import {
  calculateDaysBetweenDates,
  formatDateWithIndicator,
} from '@/utils/tools';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useSelector } from 'react-redux';

interface PolicyInfoProps {
  [key: string]: any;
}

const PolicyInfo: React.FC<PolicyInfoProps> = ({ data }) => {
  const {
    inDate,
    outDate,
    earliestArrivalTime,
    departureTime,
    roomInfoName,
    breakDesc,
    hotelId,
  } = data;

  const router = useRouter();
  const priceInfo = useSelector(
    (state: RootState) => state.hotelOrder.priceInfo
  );

  const jumptoDetail = () => {
    router.push(`/hotel/baseInfo?hotelId=${hotelId}&anchor=readBeforeBooking`);
  };

  return (
    <div className="mx-[8px] mt-[8px] mb-0 rounded-[24px] bg-white px-[12px] py-[16px]">
      {/* 住离时间 + N晚 */}
      <div className="flex items-center">
        <span className="text-[16px] text-[#000000] font-medium">
          {formatDateWithIndicator(inDate)}
        </span>
        <img
          className="w-[12px] h-[12px] ml-[8px] mr-[8px]"
          src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/top_to_icon.png"
          alt=""
        />
        <span className="text-[16px] text-[#000000] font-medium">
          {formatDateWithIndicator(outDate)}
        </span>
        <div className="w-[1px] h-[14px] bg-[#CCCCCE] ml-[8px] mr-[8px]" />
        <span className="text-[16px] text-[#000000] font-medium">
          {calculateDaysBetweenDates(inDate, outDate)}晚
        </span>
      </div>

      {/* 房型名称 + 餐食描述*/}
      <div className="flex items-center mt-[8px]">
        <span className="text-[14px] text-[#000000] font-medium">
          {roomInfoName}·{breakDesc}
        </span>
        <img
          className="w-[12px] h-[12px] ml-[4px]"
          src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/right_black_icon.png"
          alt=""
        />
      </div>

      {/* 政策说明提示 */}
      {priceInfo?.prepayResult?.cancelDescription ? (
        <div className="mt-[12px] flex items-start">
          <img
            className="w-[11px] h-[11px] mt-[2px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/top-tips-icon.png"
            alt=""
          />
          <span className="ml-[4px] text-[12px] text-[#FB7A1E]">
            {priceInfo?.prepayResult?.cancelDescription}
          </span>
        </div>
      ) : null}

      {/* 入离时间说明 + 订房必读 */}
      <div className="flex items-center justify-between mt-[8px]">
        {departureTime && departureTime ? (
          <p className="text-[12px] text-[#99999E]">
            <span>入住时间：{earliestArrivalTime}后</span>
            <span className="ml-[12px]">退房时间：{departureTime}前</span>
          </p>
        ) : null}
        <div
          className="flex items-center justify-center"
          onClick={jumptoDetail}
        >
          <span className="text-[12px] text-[#99999E]">订房必读</span>
          <img
            className="w-[12px] h-[12px] ml-[4px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/top_right_icon.png"
            alt=""
          />
        </div>
      </div>
    </div>
  );
};

export default PolicyInfo;
