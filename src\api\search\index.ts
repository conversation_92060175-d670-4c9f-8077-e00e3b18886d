import { request } from '@/utils/request';

/** 获取城市信息列表 */
export const getCityInfo = (): Promise<
  App.Service.IResponse<Search.IGetCityInfoRes>
> =>
  request<Search.IGetCityInfoRes>('/hotel/search/city/getCityInfo/v1', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

/** 搜索关键词 */
export const searchKeywords = (
  params: Search.ISearchKeywordsReq
): Promise<App.Service.IResponse<Search.ISearchKeywordsRes>> =>
  request<Search.ISearchKeywordsRes>('/hotel/search/keywords/select/v1', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
  });
