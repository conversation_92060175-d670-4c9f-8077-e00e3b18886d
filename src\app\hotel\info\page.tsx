import { getUserList } from '@/api'; // 导入 API
import { Button } from '@/components';

import ClientUserList from './clientUserList'; // 导入 ClientUserList (稍后创建)
import FetchButton from './fetchButton'; // 导入 FetchButton

// eslint-disable-next-line react/function-component-definition
export default async function InfoPage() {
  // 异步服务器组件
  const result = await getUserList(); // 服务端数据获取
  if (result.isSuccess) {
    const { users } = result.data;
    return (
      <>
        {/* 服务端渲染初始列表 */}
        <ul>
          {users.map((user: API.User.IUser) => (
            <li key={user.id}>{user.name}</li>
          ))}
        </ul>
        {/* 客户端组件处理后续更新 */}
        <ClientUserList />
        <FetchButton /> {/* 使用 FetchButton 组件 */}
        <Button />
      </>
    );
  }

  return null;
}
