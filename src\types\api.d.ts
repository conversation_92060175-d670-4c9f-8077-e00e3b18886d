namespace API {
  namespace Common {
    interface PaginatingCommonParams {
      /** Current page number */
      current: number;
      /** Page size */
      size: number;
      /** Total count */
      total: number;
      totalPage?: number;
    }

    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      list: T[];
    }

    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'size'> & {
      page: number;
    };
  }
}
