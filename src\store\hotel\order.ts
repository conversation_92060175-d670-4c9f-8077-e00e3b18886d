// src/store/OrderSlice.ts
import {
  createAsyncThunk,
  createSlice,
  type PayloadAction,
} from '@reduxjs/toolkit';

import {
  createOrder,
  getRoomPriceInfo,
  getSaleRoomInfo,
} from '@/api/hotel/order';

import type { RootState } from '../index';

interface OrderState {
  token: string | undefined;
  roomId: string;
  ratePlanId: string;
  roomKey: string;
  roomInfo: API.HotelSaleRoomInfo.HotelData;
  roomInfoDetail: API.HotelSaleRoomInfo.RoomInfoDetail;
  tourInfo: API.HotelOrderParams.OrderTour;
  priceInfo: API.HotelPriceInfo.HotelAvailabilityResponse;
  getPriceLoading: boolean;
  createOrderLoading: boolean;
  getPriceInfoFail: boolean;
  getPriceInfotext: string;
}

const initialState: OrderState = {
  token: '',
  roomId: '',
  ratePlanId: '',
  roomKey: '',
  roomInfo: {} as API.HotelSaleRoomInfo.HotelData,
  roomInfoDetail: {} as API.HotelSaleRoomInfo.RoomInfoDetail,
  tourInfo: {} as API.HotelOrderParams.OrderTour,
  priceInfo: {} as API.HotelPriceInfo.HotelAvailabilityResponse,
  getPriceLoading: true,
  createOrderLoading: false,
  getPriceInfoFail: false,
  getPriceInfotext: '',
};

// 下单页主接口
export const saleRoomInfo = createAsyncThunk(
  'hotelOrder/getSaleRoomInfo',
  async (_, { getState, dispatch, rejectWithValue }) => {
    const state = getState() as RootState;
    const { roomKey, token, roomId, ratePlanId } = state.hotelOrder;

    try {
      const { code, isSuccess, data, message } = await getSaleRoomInfo({
        roomKey,
        token,
      });
      if (code === 200 && isSuccess) {
        const { roomInfoList, unMatchRoomInfoList, hotelStaticInfo, roomKey } =
          data;
        const HotelList = roomInfoList.concat(unMatchRoomInfoList);
        const roomInfo = HotelList.find(item => item.roomId === roomId);
        if (roomInfo) {
          const { rpList, ...rest } = roomInfo;
          const rpInfo = rpList.find(item => item.ratePlanId === ratePlanId);
          const obj = {
            roomKey,
            ...rpInfo,
            ...rest,
            ...hotelStaticInfo,
          };
          dispatch(setroomInfoDetail(obj));
        }

        return data;
      }
      return rejectWithValue(message || '请求失败');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : '未知错误'
      );
    }
  }
);

// 获取价格信息接口
export const getPriceInfo = createAsyncThunk(
  'hotelOrder/booking',
  async (
    params: API.HotelPriceInfo.HotelBookingRequest,
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setRriceLoading(true));
      const { code, isSuccess, data, message } = await getRoomPriceInfo(params);

      if (code === 200 && isSuccess && data) {
        dispatch(setPriceInfo(data));
        dispatch(setRriceLoading(false));
        return data;
      } else {
        dispatch(setGetPriceInfoFail(true));
        dispatch(setGetPriceInfotext(message || '酒店不可预定'));
      }
      return rejectWithValue(message || '请求失败');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : '未知错误'
      );
    }
  }
);

// 创单接口
export const createOrderApi = createAsyncThunk(
  'hotelOrder/createOrder',
  async (
    params: API.HotelCreateOrder.HotelCreateOrderRequest,
    { dispatch, rejectWithValue }
  ) => {
    try {
      dispatch(setCreateOrderLoading(true));

      const { code, isSuccess, data, message } = await createOrder(params);
      if (code === 200 && isSuccess && data) {
        // dispatch(setCreateOrderLoading(false));
        return data;
      }

      dispatch(setCreateOrderLoading(false));
      return rejectWithValue(message || '请求失败');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : '未知错误'
      );
    }
  }
);

export const initTourList = createAsyncThunk(
  'hotelOrder/initTourList',
  async (
    params: {
      identification: number;
      roomNum: number;
    },
    { getState, dispatch }
  ) => {
    const { roomNum, identification } = params;
    const state = getState() as RootState;
    const { tourInfo, priceInfo } = state.hotelOrder;

    const defaultTourItem: API.HotelOrderParams.tourItem = {
      isMust: true,
      name: '',
      cardNo: '',
    };

    if (!Object.keys(tourInfo).length) {
      const newTourInfo: API.HotelOrderParams.OrderTour = {
        roomNum: 1,
        linkPhone: '',
        subDisabled: true,
        addDisabled: roomNum >= Number(priceInfo?.totalRoomNum),
        tourList: [defaultTourItem],
      };
      dispatch(setTourInfo(newTourInfo));

      return newTourInfo;
    }

    // 如果 tourInfo 已存在，调整 tourList 长度
    const currentTourList = [...tourInfo.tourList];
    const difference = currentTourList.length - roomNum;

    let updatedTourList: API.HotelOrderParams.tourItem[];
    const needAll =
      identification === 2 || identification === 3 || identification === 4;
    if (needAll) {
      if (difference > 0) {
        // 如果当前 tourList 比需要的 roomNum 多，则截断
        updatedTourList = currentTourList.slice(0, roomNum);
      } else if (difference < 0) {
        // 如果当前 tourList 比需要的 roomNum 少，则补充默认项
        const itemsToAdd = Array(Math.abs(difference)).fill(defaultTourItem);
        updatedTourList = [...currentTourList, ...itemsToAdd];
      } else {
        // 如果长度刚好，保持不变
        updatedTourList = currentTourList;
      }
    } else {
      // 如果长度刚好，保持不变
      updatedTourList = currentTourList;
    }

    // 更新 tourInfo 并 dispatch
    const updatedTourInfo: API.HotelOrderParams.OrderTour = {
      ...tourInfo,
      roomNum,
      subDisabled: roomNum === 1,
      addDisabled: roomNum >= Number(priceInfo?.totalRoomNum),
      tourList: updatedTourList,
    };

    dispatch(setTourInfo(updatedTourInfo));
    return updatedTourInfo;
  }
);

const OrderSlice = createSlice({
  name: 'hotelOrder',
  initialState,
  reducers: {
    setKeyValue: (
      state,
      action: PayloadAction<API.HotelOrderParams.OrderPageProps>
    ) => {
      state.roomId = action.payload.roomId;
      state.roomKey = action.payload.roomKey;
      state.ratePlanId = action.payload.ratePlanId;
      state.token = action.payload.token;
    },
    setroomInfoDetail: (state, action) => {
      state.roomInfoDetail = action.payload;
    },
    setTourInfo: (state, action) => {
      state.tourInfo = action.payload;
    },
    setPriceInfo: (state, action) => {
      state.priceInfo = action.payload;
    },
    setRriceLoading: (state, action) => {
      state.getPriceLoading = action.payload;
    },
    setCreateOrderLoading: (state, action) => {
      state.createOrderLoading = action.payload;
    },
    setGetPriceInfoFail: (state, action) => {
      state.getPriceInfoFail = action.payload;
    },
    setGetPriceInfotext: (state, action) => {
      state.getPriceInfotext = action.payload;
    },
  },
  extraReducers: builder => {
    builder.addCase(saleRoomInfo.fulfilled, (state, action) => ({
      ...state,
      roomInfo: action.payload,
    }));
  },
});

export const {
  setKeyValue,
  setroomInfoDetail,
  setTourInfo,
  setPriceInfo,
  setRriceLoading,
  setCreateOrderLoading,
  setGetPriceInfotext,
  setGetPriceInfoFail,
} = OrderSlice.actions;
export const selectRoomInfo = (state: RootState) => state.hotelOrder.roomInfo;
export default OrderSlice.reducer;
