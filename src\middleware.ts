// middleware.ts
import { NextResponse, type NextRequest } from 'next/server';

import { setupDreameAuth } from '@/plugins/dreame-auth';
import { setupRouteGuard } from '@/plugins/route-guard';
import { isInDreameApp } from '@/server/utils';
import { tryCatchTracker } from '@/utils/track';

export async function middleware(request: NextRequest) {
  try {
    let response = NextResponse.next();
    response = await setupRouteGuard(request, response);
    if (
      response.redirected ||
      (response.status >= 300 && response.status < 400)
    ) {
      return response;
    }
    const isDreameApp = await isInDreameApp();
    if (isDreameApp) {
      // 在Dreame App内处理Dreame的用户认证信息
      response = await setupDreameAuth(request, response);
    }
    return response;
  } catch (error) {
    tryCatchTracker(error, { scene: 'middleware', url: request.url });
    return NextResponse.next();
  }
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
