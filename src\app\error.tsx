'use client';
import { useEffect } from 'react';

import { useRouter } from 'next/navigation';

import { tracker } from '@/utils/track';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  const router = useRouter();

  useEffect(() => {
    tracker({
      eventType: 'react_error',
      message: error?.message,
      stack: error?.stack,
      path: window.location.pathname,
    });
  }, [error]);

  const handleRefresh = () => {
    tracker({
      eventType: 'click',
      path: window.location.pathname,
      extra: { action: 'refresh_on_error_page' },
    });
    // Next.js推荐用reset()，但也可直接刷新
    reset?.();
    router.replace(window.location.pathname);
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <h1 style={{ color: '#e53e3e', marginBottom: 16 }}>服务异常</h1>
      <p style={{ marginBottom: 24 }}>很抱歉，页面发生了错误，请稍后重试。</p>
      <button
        onClick={handleRefresh}
        style={{
          padding: '8px 24px',
          background: '#568DED',
          color: '#fff',
          border: 'none',
          borderRadius: 4,
          cursor: 'pointer',
        }}
      >
        刷新页面
      </button>
    </div>
  );
}
