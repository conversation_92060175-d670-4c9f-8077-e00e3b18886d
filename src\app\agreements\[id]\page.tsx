import { headers } from 'next/headers';

import { detectDevice, isInDreameApp } from '@/server/utils';
import type {
  IAgreementPageProps,
  IAgreementsFetchResponse,
} from '@/types/agreements/index'; // 导入 User 和 PostsResponse 类型
// import { post } from '@/untils'; // 导入 post 函数
// import { API_GET_HOTEL_USERS } from '@/api'; // 导入 API

import Native from '@/utils/native';
import { tryCatchTracker } from '@/utils/track';

import { AgreementNavbarWrapper } from './agreementNavbarWrapper';
import {
  agreementsMessage,
  agreementsMessageAndroid,
  browserAgreements,
  dreameAgreements,
} from './config';

// 异常页面
const errorPage = () => (
  <div className="w-screen min-h-screen flex items-center justify-center text-red-500">
    协议不存在
  </div>
);

export default async function AgreementPage({
  params,
}: {
  params: Promise<IAgreementPageProps>;
}) {
  const awaitedParams = await params; // await params Promise 获取包含 id 的对象
  const { id } = awaitedParams; // 从解析后的对象中解构出 id
  const agreements = [
    'user',
    'protect',
    'sdk',
    'children',
    'bookNoteInfo',
    'business',
  ];

  // 如果没有提供 id 或者 id 不在预定义的协议列表中，返回 协议不存在
  if (!id || !agreements.includes(id)) {
    return errorPage();
  }

  const isInDreame = await isInDreameApp();
  const platform = await detectDevice();
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';

  if (isInDreame) {
    // 追觅APP
    Object.assign(agreementsMessage, dreameAgreements);
  } else if (Native.isOwnApp(userAgent)) {
    // 翎游APP
    if (platform === 'android') {
      Object.assign(agreementsMessage, agreementsMessageAndroid);
    }
  } else {
    // 追觅浏览器
    Object.assign(agreementsMessage, browserAgreements);
  }

  try {
    // const result = await post(`${API_GET_HOTEL_USERS}/${id}`); // 添加类型参数
    // const { content } = result?.data as IAgreementsFetchResponse;

    const result = agreementsMessage.find(item => item.type === id);
    const { content, effectiveDate, releaseDate, title } =
      result as IAgreementsFetchResponse;

    if (!content) {
      return errorPage();
    }

    return (
      <AgreementNavbarWrapper title={title || '协议详情'}>
        <div className="w-screen min-h-screen bg-bg-card px-[16px] text-text-primary">
          {effectiveDate || releaseDate ? (
            <div className="p-[16px]">
              {!!releaseDate && (
                <p className="mb-[16px] text-[14px] text-text-primary text-right">
                  发布日期:{releaseDate}
                </p>
              )}
              {!!effectiveDate && (
                <p className="text-[14px] text-text-primary text-right">
                  生效日期:{effectiveDate}
                </p>
              )}
            </div>
          ) : null}
          {content?.map((section, index) => (
            <div key={index} className="mt-[16px]">
              {/* 标题 */}
              {section?.title && (
                <h2 className="text-[px] font-medium">{section?.title}</h2>
              )}

              {/* 文本 */}
              {section?.agreements && (
                <div className="my-[16px]">
                  {section?.agreements?.map((paragraph, pIndex) => (
                    <p
                      key={pIndex}
                      className={`leading-6 text-[14px] ${pIndex > 0 ? 'mt-[8px]' : ''}`}
                      dangerouslySetInnerHTML={{ __html: paragraph }}
                    />
                  ))}
                </div>
              )}

              {/* 表格 */}
              {section?.table && (
                <table className="w-full table-fixed border border-gray-300 border-collapse">
                  <thead className="bg-gray-100">
                    <tr>
                      {section.table?.headers.map((header, index) => (
                        <th
                          key={index}
                          className="border border-gray-300 p-[4px] text-[12px] text-[#11111E] text-center font-semibold align-middle bg-[#FFFFFF]"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {section.table?.rows.map((row, rowIndex) => (
                      <tr key={rowIndex}>
                        {row.cells.map((cell, cellIndex) => (
                          <td
                            key={cellIndex}
                            className="border border-gray-300 p-[4px] text-[12px] text-[#11111E] align-middle leading-relaxed whitespace-pre-wrap"
                          >
                            <span
                              className="break-all"
                              dangerouslySetInnerHTML={{ __html: cell }}
                            />
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}

              {/* 图片 */}
              {section?.img && (
                <img src={section.img} alt="" style={{ width: '100%' }} />
              )}
            </div>
          ))}
        </div>
      </AgreementNavbarWrapper>
    );
  } catch (error) {
    tryCatchTracker(error, { scene: 'agreement_page', id });
    return errorPage();
  }
}
