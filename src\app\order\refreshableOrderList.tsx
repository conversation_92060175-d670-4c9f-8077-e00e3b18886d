'use client';
import { useCallback, useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { List, PullRefresh } from 'react-vant';

import { deleteOrder, getOrderList } from '@/api/order';
import Dialog from '@/components/dialog';
import { tracker, tryCatchTracker } from '@/utils/track';

import { getImageUrl } from '../../utils';

import { OrderListSkeleton } from './components/orderListSkeleton';
import { OrderItem } from './orderItem';

const PlaneIcon: React.FC = () => (
  <div className="flex flex-col items-center justify-center py-6">
    <span className="w-[72px] h-[72px] block">
      <img
        src={getImageUrl('loading.gif')}
        alt="loading"
        className="w-full h-full object-contain"
      />
    </span>
  </div>
);

/** 空状态组件 */
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center bg-[#fff] w-[100vw] h-[calc(100vh-112px)] pt-[174px]">
    <img src={getImageUrl('order/empty.png')} alt="" className="w-[196px]" />
    <h3 className="text-[#000] font-bold text-[16px] mt-[10px]">暂无订单</h3>
  </div>
);

/** 可刷新订单列表组件属性 */
export interface RefreshableOrderListProps {
  /** 初始订单数据 */
  initialOrders: Order.IOrderItem[];
  /** 当前激活的tab */
  activeTab: string;
  /** 用户token */
  token?: string;
  /** 安全区域高度（像素） */
  safeAreaHeightPx?: number;
}

/** 可刷新订单列表组件 */
export const RefreshableOrderList: React.FC<RefreshableOrderListProps> = ({
  initialOrders,
  activeTab,
  token,
}) => {
  // 每页数据量
  const PAGE_SIZE = 10;

  // 获取router实例
  const router = useRouter();

  // 将tab字符串转换为订单状态
  const getOrderStatus = (tabKey: string): string => {
    switch (tabKey) {
      case 'pending_payment':
        return '0'; // 待付款
      case 'not_traveled':
        return '2'; // 未出行
      default:
        return ''; // 全部状态传空字符串
    }
  };

  // 状态管理
  const [orders, setOrders] = useState<Order.IOrderItem[]>(initialOrders);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [finished, setFinished] = useState(initialOrders.length < PAGE_SIZE);
  const [currentPage, setCurrentPage] = useState(1);
  const prevTab = useRef(activeTab);

  // 跟踪是否已经初始化
  const [isInitialized, setIsInitialized] = useState(false);
  // 跟踪是否正在切换tab
  const [isSwitchingTab, setIsSwitchingTab] = useState(false);

  // Dialog状态
  const [dialogVisible, setDialogVisible] = useState(false);
  const [pendingDeleteOrderNo, setPendingDeleteOrderNo] = useState<
    string | null
  >(null);

  // 加载订单数据
  const loadOrders = useCallback(
    async (page: number, isRefresh = false, status?: string) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        // 使用传入的status参数，如果没有传入则使用当前activeTab
        const orderStatus = status ?? getOrderStatus(activeTab);

        const params: Order.IOrderListReq = {
          pageIndex: page,
          pageSize: PAGE_SIZE,
          status: orderStatus,
          keyword: '',
        };

        const response = await getOrderList(params, token);

        if (response.isSuccess && response.data) {
          const newOrders = response.data;

          if (isRefresh) {
            // 刷新时替换所有数据
            setOrders(newOrders);
            setCurrentPage(1);
          } else {
            // 加载更多时追加数据
            setOrders(prev => [...prev, ...newOrders]);
          }

          // 判断是否还有更多数据
          setFinished(newOrders.length < PAGE_SIZE);
        } else {
          // 获取订单列表失败埋点
          tracker({
            eventType: 'click',
            extra: {
              action: 'order_list_load_fail',
              activeTab,
              orderStatus: getOrderStatus(activeTab),
              page,
              isRefresh,
              responseCode: response?.code,
              message: response?.message,
            },
          });
          throw new Error(response.message || '获取订单列表失败');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '网络错误';
        console.error('加载订单数据失败:', errorMessage);

        // 加载订单列表异常埋点
        tryCatchTracker(err, {
          scene: 'order_list_load',
          activeTab,
          orderStatus: getOrderStatus(activeTab),
          page,
          isRefresh,
          errorMessage,
        });
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },

    [token, activeTab] // 添加activeTab依赖以确保getOrderStatus函数能正确获取当前状态
  );

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    // 下拉刷新埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_list_pull_refresh',
        activeTab,
        orderStatus: getOrderStatus(activeTab),
        currentOrdersCount: orders.length,
      },
    });

    setCurrentPage(1);
    await loadOrders(1, true, getOrderStatus(activeTab));
  }, [loadOrders, activeTab, orders.length]);

  // 上拉加载更多
  const handleLoadMore = useCallback(async () => {
    // 在刷新状态或切换tab状态下不允许加载更多，防止重复调用
    if (!loading && !finished && !refreshing && !isSwitchingTab) {
      // 上拉加载更多埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'order_list_load_more',
          activeTab,
          orderStatus: getOrderStatus(activeTab),
          currentPage,
          currentOrdersCount: orders.length,
        },
      });

      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await loadOrders(nextPage, false, getOrderStatus(activeTab));
    }
  }, [
    loading,
    finished,
    refreshing,
    isSwitchingTab,
    currentPage,
    loadOrders,
    activeTab,
    orders.length,
  ]);

  // 初始化数据（只在首次挂载时使用SSR数据）
  useEffect(() => {
    if (!isInitialized) {
      setOrders(initialOrders);
      setFinished(initialOrders.length < PAGE_SIZE);
      setCurrentPage(1);
      setIsInitialized(true);
      prevTab.current = activeTab;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized]);

  // 切换tab时才请求接口
  useEffect(() => {
    if (isInitialized && activeTab !== prevTab.current) {
      // 设置切换tab状态，防止加载更多被触发
      setIsSwitchingTab(true);

      // 立即重置所有状态，防止竞态条件
      setOrders([]);
      setCurrentPage(1);
      setFinished(false);
      setLoading(false);
      setRefreshing(false);

      // 延迟一帧执行，确保状态已经更新
      const timeoutId = setTimeout(() => {
        void (async () => {
          await loadOrders(1, true, getOrderStatus(activeTab));
          setIsSwitchingTab(false); // 完成后重置状态
        })();
      }, 0);

      prevTab.current = activeTab;

      return () => {
        clearTimeout(timeoutId);
        setIsSwitchingTab(false);
      };
    }
  }, [activeTab, isInitialized, loadOrders]);

  // 渲染错误状态
  const renderErrorText = (retry: () => void) => (
    <div className="flex flex-col items-center py-4">
      <span className="text-sm text-gray-500 mb-2">加载失败</span>
      <button
        onClick={retry}
        className="px-4 py-2 bg-blue-500 text-white text-sm rounded-full"
      >
        重试
      </button>
    </div>
  );

  // 删除订单点击
  const handleDeleteOrder = (orderNo: string) => {
    // 删除订单点击埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_delete_click',
        orderNo,
        activeTab,
        orderStatus: getOrderStatus(activeTab),
      },
    });

    setPendingDeleteOrderNo(orderNo);
    setDialogVisible(true);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    if (!pendingDeleteOrderNo) return;

    // 确认删除订单埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_delete_confirm',
        orderNo: pendingDeleteOrderNo,
        activeTab,
        orderStatus: getOrderStatus(activeTab),
      },
    });

    setDialogVisible(false);
    const res = await deleteOrder({ orderNo: pendingDeleteOrderNo });
    if (res.code === 200) {
      // 删除订单成功埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'order_delete_success',
          orderNo: pendingDeleteOrderNo,
          activeTab,
          orderStatus: getOrderStatus(activeTab),
        },
      });
      await handleRefresh();
    } else {
      // 删除订单失败埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'order_delete_fail',
          orderNo: pendingDeleteOrderNo,
          activeTab,
          orderStatus: getOrderStatus(activeTab),
          responseCode: res?.code,
          message: res?.message,
        },
      });
    }
    setPendingDeleteOrderNo(null);
  };

  // 取消删除
  const handleCancelDelete = () => {
    // 取消删除订单埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_delete_cancel',
        orderNo: pendingDeleteOrderNo,
        activeTab,
        orderStatus: getOrderStatus(activeTab),
      },
    });

    setDialogVisible(false);
    setPendingDeleteOrderNo(null);
  };

  return (
    <div
      className={`bg-gray-50 ${orders.length > 0 ? 'mt-[8px]' : ''}`}
      style={{
        paddingTop: orders.length > 0 ? `${56}px` : '0',
      }}
    >
      <PullRefresh
        headHeight={96}
        onRefresh={handleRefresh}
        pullingText={<PlaneIcon />}
        loosingText={<PlaneIcon />}
        loadingText={<PlaneIcon />}
      >
        <div style={{ minHeight: `calc(100vh - ${112}px)` }}>
          {isSwitchingTab ? (
            // Tab切换时显示骨架屏
            <OrderListSkeleton count={5} />
          ) : orders.length === 0 && !loading && !refreshing ? (
            <EmptyState />
          ) : (
            <List
              finished={finished}
              onLoad={handleLoadMore}
              finishedText="没有更多了"
              errorText={renderErrorText}
              offset={100}
            >
              {orders.map(order => (
                <OrderItem
                  key={order.id}
                  order={order}
                  router={router}
                  onDelete={handleDeleteOrder}
                />
              ))}
            </List>
          )}
        </div>
      </PullRefresh>
      <Dialog
        visible={dialogVisible}
        title="确认删除订单"
        confirmText="删除"
        cancelText="取消"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      >
        删除后订单将无法恢复，确定要删除吗？
      </Dialog>
    </div>
  );
};

export default RefreshableOrderList;
