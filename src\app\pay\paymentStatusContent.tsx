'use client';

import { useCallback, useEffect, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { queryPaymentStatus } from '@/api/pay';

import { getImageUrl } from '../../utils';

/** 支付状态内容组件 */
const PaymentStatusContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderNo = searchParams.get('orderNo');

  const [paymentData, setPaymentData] =
    useState<Pay.IPaymentStatusResponse | null>(null);
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  // 获取支付状态
  const fetchPaymentStatus = useCallback(async () => {
    if (!orderNo) {
      setError('订单号不能为空');
      setLoading(false);
      return;
    }
    setLoading(true);
    setError('');
    setPaymentData(null);
    try {
      const response = await queryPaymentStatus({ orderNo });
      if (response.code === 200 && response.data) {
        setPaymentData(response.data);
        setError('');
      } else {
        setError(response.message || '查询支付状态失败');
        setPaymentData(null);
      }
    } catch (_unusedErr) {
      setError('网络错误，请稍后重试');
      setPaymentData(null);
    } finally {
      setLoading(false);
    }
  }, [orderNo]);

  useEffect(() => {
    void fetchPaymentStatus();
  }, [fetchPaymentStatus]);

  if (loading) {
    return (
      <div className="p-4 text-center text-[#666]">正在获取支付状态...</div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center min-h-screen bg-[#fff]">
        <div className="text-center max-w-sm">
          <div className=" rounded-full flex items-center justify-center mx-auto mt-[40px]">
            <img
              src={getImageUrl('pay/fail.png')}
              alt=""
              className="w-[76px] h-[76px]"
            />
          </div>
          <div className="text-text-secondary mb-[12px] mt-[12px]">
            支付失败
          </div>
          <div>
            <button
              type="button"
              onClick={() => window.history.back()}
              className="w-[calc(100vw-32px)] ml-[16px] flex items-center justify-center text-btn-primary-text bg-btn-primary-bg text-[16px] h-[50px] rounded-[60px] hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200"
            >
              返回
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (paymentData) {
    return (
      <div className="min-h-screen bg-bg-card">
        <div className="max-w-sm mx-auto">
          {/* 状态图标和标题 */}
          <div className="text-center mb-8 pt-16">
            <div className="rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="iconfont icon-a-Property1gouxuanxuanzhong !text-[70px] text-success" />
            </div>
            <h1 className="text-text-secondary text-[14px] mt-[12px]">
              支付成功
            </h1>
          </div>
          {/* 操作按钮 */}
          <div className="space-y-3">
            <button
              type="button"
              onClick={() =>
                router.push(`/hotel/orderDetail?orderNo=${orderNo}`)
              }
              className="w-[calc(100vw-32px)] ml-[16px] flex items-center justify-center text-[#11111E] bg-[#E8DEC1] text-[16px] h-[50px] rounded-[60px] "
            >
              查看订单
            </button>
            <button
              type="button"
              onClick={() => window.history.back()}
              className="w-[calc(100vw-32px)] ml-[16px] flex items-center justify-center text-[#11111E] bg-[#F7F7F7] text-[16px] h-[50px] rounded-[60px] "
            >
              完成
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 理论上不会走到这里
  return null;
};

export default PaymentStatusContent;
