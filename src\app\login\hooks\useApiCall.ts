import { useCallback, useState } from 'react';

/** API调用状态 */
interface ApiState<T> {
  /** 是否正在加载 */
  loading: boolean;
  /** 数据 */
  data: T | null;
  /** 错误信息 */
  error: string | null;
}

/** API函数类型 */
type ApiFunction<T, <PERSON>rgs extends readonly unknown[] = unknown[]> = (
  ..._args: Args
) => Promise<T>;

/** API调用Hook返回类型 */
interface UseApiCallReturn<T, Args extends readonly unknown[] = unknown[]> {
  /** 是否正在加载 */
  loading: boolean;
  /** 数据 */
  data: T | null;
  /** 错误信息 */
  error: string | null;
  /** 执行API调用 */
  execute: (..._args: Args) => Promise<T | null>;
}

/**
 * API调用Hook
 *
 * @param apiFunction API函数
 * @returns API调用状态和控制方法
 */
export function useApiCall<T, <PERSON>rgs extends readonly unknown[] = unknown[]>(
  apiFunction: ApiFunction<T, Args>
): UseApiCallReturn<T, Args> {
  const [state, setState] = useState<ApiState<T>>({
    loading: false,
    data: null,
    error: null,
  });

  /** 执行API调用 */
  const execute = useCallback(
    async (..._args: Args): Promise<T | null> => {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const result = await apiFunction(..._args);
        setState(prev => ({
          ...prev,
          loading: false,
          data: result,
          error: null,
        }));
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : '请求失败';
        setState(prev => ({
          ...prev,
          loading: false,
          data: null,
          error: errorMessage,
        }));
        return null;
      }
    },
    [apiFunction]
  );

  return {
    loading: state.loading,
    data: state.data,
    error: state.error,
    execute,
  };
}
