@tailwind base;
@tailwind components;
@tailwind utilities;
@import 'react-vant/lib/index.css';
/* 基础字体定义 */
@font-face {
  font-family: '<PERSON>eist';
  font-style: normal;
  font-weight: 400;
  src: local('Arial');
  font-display: block;
}

@font-face {
  font-family: '<PERSON>eist Mono';
  font-style: normal;
  font-weight: 400;
  src: local('Courier New');
  font-display: block;
}

@layer base {
  /* 基础样式重置 */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    -webkit-text-size-adjust: 100%;
    font-family: 'Geist', 'Arial', sans-serif;
    /* 移动端适配 - 基于375px设计稿的响应式根字体 */
    font-size: 16px; /* 默认字体大小 */
  }

  /* 移动端适配媒体查询 - 基于375px设计稿 */
  /* 320px及以下屏幕 */
  @media screen and (max-width: 320px) {
    html {
      font-size: 13.65px; /* 320/375 * 16 */
    }
  }

  /* 321px-374px屏幕 */
  @media screen and (min-width: 321px) and (max-width: 374px) {
    html {
      font-size: 15px; /* 约350/375 * 16 */
    }
  }

  /* 375px屏幕（设计稿基准） */
  @media screen and (min-width: 375px) and (max-width: 413px) {
    html {
      font-size: 16px; /* 基准大小 */
    }
  }

  /* 414px及以上大屏手机 */
  @media screen and (min-width: 414px) and (max-width: 767px) {
    html {
      font-size: 17.64px; /* 414/375 * 16 */
    }
  }

  /* 768px及以上平板/桌面 - 限制最大根字体 */
  @media screen and (min-width: 768px) {
    html {
      font-size: 20px; /* 限制最大字体，避免过大 */
    }
  }

  body {
    font-family: 'Geist', 'Arial', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.5;
    background-color: var(--bg-page);
    color: var(--text-primary);
  }
}

@layer utilities {
  /* 基础工具类 */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto;
  }
  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    /* Firefox */
    scrollbar-width: none;
    /* Safari and Chrome */
    -webkit-scrollbar: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* iOS 14 Grid 支持检测 */
@supports not (display: grid) {
  .grid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .grid > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .grid-cols-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .gap-3 > * {
    margin: 12px; /* 0.375rem = 12px */
  }
}

/* 移除所有使用 CSS 变量的样式 */
.bg-white {
  background-color: rgba(255, 255, 255, 1);
}

.bg-background {
  background-color: rgba(255, 255, 255, 1);
}

.bg-background-dark {
  background-color: rgba(10, 10, 10, 1);
}

.text-foreground {
  color: rgba(23, 23, 23, 1);
}

.text-foreground-dark {
  color: rgba(237, 237, 237, 1);
}

/* 默认主题变量 - 会被ServerThemeProvider覆盖 */
:root {
  /* 主题色系 */
  --primary-color: rgb(86, 141, 237);
  --primary-light: rgba(86, 141, 237, 0.1);
  --primary-bg: rgba(86, 141, 237, 0.05);

  /* 主题色透明度变体 */
  --primary-color-90: rgba(86, 141, 237, 0.9);
  --primary-color-80: rgba(86, 141, 237, 0.8);
  --primary-color-70: rgba(86, 141, 237, 0.7);
  --primary-color-60: rgba(86, 141, 237, 0.6);
  --primary-color-50: rgba(86, 141, 237, 0.5);
  --primary-color-40: rgba(86, 141, 237, 0.4);
  --primary-color-30: rgba(86, 141, 237, 0.3);
  --primary-color-20: rgba(86, 141, 237, 0.2);
  --primary-color-10: rgba(86, 141, 237, 0.1);

  /* 按钮色系 */
  --btn-primary-bg: #568ded;
  --btn-primary-text: #ffffff;
  --btn-primary-hover: #4a7bd9;
  --btn-primary-active: #3e6bc5;
  --btn-secondary-bg: #f7f7f7;
  --btn-secondary-text: #568ded;
  --btn-secondary-hover: #eeeeee;
  --btn-secondary-active: #e5e5e5;

  /* 文字色系 */
  --text-primary: #11111e;
  --text-secondary: #33333e;
  --text-tertiary: #66666e;
  --text-muted: #99999e;

  /* 背景色系 */
  --bg-page: #f9fafb;
  --bg-card: #ffffff;
  --bg-gray: #f7f7f7;
  --bg-light: #f9f9f9;

  /* 状态色系 */
  --success: #07c160;
  --warning: #ff7a00;
  --error: #ff6b6b;
  --info: #1677ff;

  /* 支付色系 */
  --alipay: #1677ff;
  --wechat: #07c160;
  --unionpay: #e21918;
}

/* Shimmer 动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
}
