import { Suspense } from 'react';

import ServerNavbar from '../../components/navbar/ServerNavbar';

import PaymentStatusContent from './paymentStatusContent';

/** 支付状态页面 用户支付完成后跳转到此页面查看支付结果 */
const PaymentStatusPage = () => (
  <div className="min-h-screen bg-gray-50">
    {/* 顶部自定义Navbar */}
    <ServerNavbar
      left={{ showBack: true }}
      title=""
      className="border-b"
      style={{ borderBottom: '1px solid #F7F7F7' }}
    />
    <Suspense fallback={<div className="p-4 text-center">加载中...</div>}>
      <PaymentStatusContent />
    </Suspense>
  </div>
);

export default PaymentStatusPage;
