// 纯服务端渲染的OrderItem组件 - 不使用任何React hooks
'use client';

import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

import { tracker } from '@/utils/track';

import { getImageUrl } from '../../utils';

import { OrderItemActions } from './orderItemActions';

export interface OrderItemProps {
  /** 订单数据 */
  order: Order.IOrderItem;
  /** 自定义类名 */
  className?: string;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  router: AppRouterInstance;
  onDelete?: (_orderNo: string) => void;
}
/** 订单项组件 根据设计稿实现的订单列表项 */
export const OrderItem: React.FC<OrderItemProps> = ({
  order,
  className = '',
  onDelete,
}) => (
  <div
    className={`bg-white rounded-[20px] px-[12px] py-[12px] mx-[8px] first:mt-0 mt-[8px] ${className}`}
  >
    {/* 头部：酒店图标 + 状态 + 删除图标 */}
    <div className="flex items-center justify-between mb-[16px]">
      <div className="flex items-center">
        {/* 酒店图标 */}
        <img
          src={getImageUrl('order/hotel.png')}
          alt=""
          className="w-[16px] h-[16px] mr-[8px]"
        />
        <span className="text-[#66666E] text-[12px]">酒店</span>
      </div>

      <div className="flex items-center">
        {/* 订单状态 */}
        <div className={` text-[#11111E] text-[12px] `}>
          {order.showOrderStatus}
        </div>

        {/* 删除图标 */}
        {order.orderStatus === 5 || order.orderStatus === 4 ? ( // 已取消
          <>
            <span className="text-[#F0F0F0] text-[12px] mx-[10px]">|</span>
            <button
              type="button"
              onClick={() => {
                // 删除订单按钮点击埋点
                tracker({
                  eventType: 'click',
                  extra: {
                    action: 'order_item_delete_click',
                    orderNo: order.orderNo,
                    hotelName: order.hotelName,
                    orderStatus: order.orderStatus,
                    orderAmount: order.orderAmount,
                  },
                });
                onDelete?.(order.orderNo);
              }}
              className="border-0 bg-transparent p-0"
              aria-label="删除订单"
            >
              <img
                src={getImageUrl('order/detele.png')}
                alt=""
                className="w-[16px] h-[16px] mr-[8px]"
              />
            </button>
          </>
        ) : null}
      </div>
    </div>
    <div
      onClick={() => {
        // 订单项点击埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'order_item_click',
            orderNo: order.orderNo,
            hotelName: order.hotelName,
            orderStatus: order.orderStatus,
            orderAmount: order.orderAmount,
            hotelId: order.hotelId,
          },
        });
        // router.push(`/hotel/orderDetail?orderNo=${order?.orderNo}`);
        window.location.href = `/hotel/orderDetail?orderNo=${order?.orderNo}`;
      }}
    >
      {/* 酒店名称 */}
      <h3 className="text-[16px] font-medium text-[#11111E] mb-[6px]">
        {order.hotelName}
      </h3>

      {/* 地址信息 */}
      <div className="text-[#11111E] text-[12px] mb-[6px]">
        <span>{order.areaName}</span>
        <span className="mx-1">·</span>
        <span>{order.hotelAddress}</span>
      </div>

      {/* 日期和房型信息 */}
      <div className="text-[#11111E] text-[12px] mb-[6px]">
        <div className="mb-1">
          <span>{order.arriveDate} 至</span>
        </div>
        <div>
          <span>
            {order.departDate} · {order.roomDesc}
          </span>
        </div>
      </div>

      {/* 价格和操作按钮 */}
      <div className="flex items-end justify-between flex-col mb-[12px]">
        {/* 价格 */}
        <div className="flex items-baseline">
          <span className="text-[#11111E] text-[12px]">在线支付</span>
          <span className="text-[#11111E] text-[10px] mx-[5px]">¥</span>
          <span className="text-[#11111E]  text-[22px] font-bold">
            {order.orderAmount}
          </span>
        </div>
      </div>
    </div>
    <div className="flex items-end justify-between flex-col">
      <OrderItemActions
        lastPayTime={order.lastPayTime}
        hotelId={order.hotelId}
        orderId={order.orderNo}
        goodsName={order.hotelName}
        orderAmount={order.orderAmount}
        showPayButton={order.orderStatus === 0} // 只有待付款状态才显示支付按钮
      />
    </div>
  </div>
);

export default OrderItem;
