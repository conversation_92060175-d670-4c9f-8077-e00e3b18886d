.home-quick-search-container {
  padding: 24px;
  margin-top: 19px;
  background-color: #fff;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.25);
  border-radius: 28px;
  box-sizing: border-box;
  position: relative;
}

.home-quick-search-container::before {
  content: '';
  position: absolute;
  top: -13px;
  left: 27px;
  width: 29px;
  height: 13px;
  background-image: url('https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/home/<USER>');
  background-size: 100% 100%;
  transition: left linear 0.3s;
}

.home-quick-search-container.index-1::before {
  left: calc(calc(100% - 78px * 4) / 3 + 27px + 78px);
}
.home-quick-search-container.index-2::before {
  left: calc(calc(100% - 78px * 4) * 2 / 3 + 27px + 78px * 2);
}
.home-quick-search-container.index-3::before {
  left: calc(100% - 78px + 27px);
}
