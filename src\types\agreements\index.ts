export interface IAgreementPageProps {
  [key: string]: string | string[];
  id: string;
}

// 表格单元格行：每行是一组单元格内容（字符串，可含 HTML）
export interface TableCell {
  cells: string[];
}

// 表格结构：包含表头和数据行
export interface AgreementTable {
  headers: string[]; // 表头标题列表
  rows: TableCell[]; // 表格的每一行
}

// 内容区块，每个区块可包含标题、协议文本段落和可选表格
export interface IAgreementsContent {
  title: string; // 如 "一、协议总则"
  agreements?: string[]; // 可选的协议段落，可能为空
  table?: AgreementTable; // 可选的表格
  img?: string;
}
// 最外层返回的用户协议数据结构
export interface IAgreementsFetchResponse {
  type: string; // 如 "user"
  title: string; // 如 "用户服务协议"
  content: IAgreementsContent[]; // 多段内容块
  version: string; // 如 "2023v1.0"
  releaseDate: string; // 如 "2023-07-01"
  effectiveDate: string; // 如 "2023-07-01"
  isRequired: boolean; // 是否必选
}
