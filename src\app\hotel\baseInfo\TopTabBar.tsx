'use client';

import React, { useEffect, useRef, useState } from 'react';

import { Link, scroller } from 'react-scroll';

import { getSafeAreaHeight, OwnApp } from '@/utils/native';

interface NavItem {
  id: string;
  title: string;
}

interface NavBarProps {
  items: NavItem[];
}

const TopTabBar: React.FC<NavBarProps> = ({ items }) => {
  const [activeId, setActiveId] = useState<string>('');
  const isManualClick = useRef(false);
  const manualClickTimer = useRef<NodeJS.Timeout | null>(null);

  // 计算固定元素的总高度偏移
  const navbarHeight = 56 + getSafeAreaHeight();
  const topTabBarHeight = 44;
  const totalFixedHeight = OwnApp.isInApp()
    ? navbarHeight
    : navbarHeight + topTabBarHeight + 20;

  // 手动点击处理函数
  const handleClick = (targetId: string) => {
    // 立即设置选中状态
    setActiveId(targetId);

    // 标记为手动点击，防止spy干扰
    isManualClick.current = true;

    // 清除之前的定时器
    if (manualClickTimer.current) {
      clearTimeout(manualClickTimer.current);
    }

    // 2秒后恢复spy监听
    manualClickTimer.current = setTimeout(() => {
      isManualClick.current = false;
    }, 2000);

    // 更新URL hash
    window.history.replaceState(null, '', `#${targetId}`);
  };

  // spy监听处理函数
  const handleSetActive = (targetId: string) => {
    // 如果是手动点击期间，忽略spy的自动更新
    if (!isManualClick.current) {
      setActiveId(targetId);
    }
  };

  // 初始化和hash处理
  useEffect(() => {
    const initializeActiveId = () => {
      // 优先检查URL hash
      let targetId = window.location.hash.replace('#', '');

      // 如果没有hash，检查查询参数中的anchor
      if (!targetId) {
        const urlParams = new URLSearchParams(window.location.search);
        const queryAnchor = urlParams.get('anchor');
        if (queryAnchor) {
          targetId = queryAnchor;
          // 将查询参数转换为hash，便于后续处理
          window.history.replaceState(null, '', `#${queryAnchor}`);
        }
      }

      if (targetId && items.some(item => item.id === targetId)) {
        setActiveId(targetId);
        // 延迟滚动，确保Element组件已渲染
        setTimeout(() => {
          scroller.scrollTo(targetId, {
            duration: 600,
            delay: 0,
            smooth: true,
            offset: -totalFixedHeight,
          });
        }, 100);
      } else if (items.length > 0) {
        // 如果没有匹配的hash，默认激活第一项
        setActiveId(items[0].id);
      }
    };

    // 监听hashchange事件
    const handleHashChange = () => {
      const newHash = window.location.hash.replace('#', '');
      if (newHash && items.some(item => item.id === newHash)) {
        setActiveId(newHash);
        // 滚动到新的目标位置
        scroller.scrollTo(newHash, {
          duration: 600,
          delay: 0,
          smooth: true,
          offset: -totalFixedHeight,
        });
      }
    };

    // 确保组件和数据已准备好
    if (items.length > 0) {
      initializeActiveId();
    }

    window.addEventListener('hashchange', handleHashChange);
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
      if (manualClickTimer.current) {
        clearTimeout(manualClickTimer.current);
      }
    };
  }, [items, totalFixedHeight]);

  return (
    <div className="bg-white px-4 py-2 flex justify-center overflow-x-auto whitespace-nowrap">
      {items.map(item => (
        <Link
          key={item.id}
          to={item.id}
          spy
          smooth
          offset={-totalFixedHeight} // 偏移固定元素高度
          duration={600}
          hashSpy
          isDynamic
          ignoreCancelEvents={false}
          spyThrottle={100}
          onSetActive={() => handleSetActive(item.id)}
          onClick={() => handleClick(item.id)}
          className={`px-3 py-2 mx-1 rounded-full text-sm relative cursor-pointer group no-underline transition-colors duration-200 ${
            activeId === item.id
              ? 'text-primary font-medium'
              : 'text-text-secondary'
          }`}
        >
          {item.title}
          <span
            className={`absolute bottom-0 left-1/2 transform-center-x w-[36%] bg-primary h-0.5 rounded-full transition-opacity duration-300 ${
              activeId === item.id ? 'opacity-100' : 'opacity-0'
            }`}
          />
        </Link>
      ))}
    </div>
  );
};

export default TopTabBar;
