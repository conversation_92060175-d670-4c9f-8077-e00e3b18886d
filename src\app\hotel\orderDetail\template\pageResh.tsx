'use client';
import { useEffect, useRef, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';

import type { AppDispatch, RootState } from '@/store';
import { getOrderDetail } from '@/store/hotel/orderDetail';

import FloatingBall from './FloatingBall';
import HotelAddress from './hotelAddress';
import ItineraryInfo from './itineraryInfo';
import OrderInfo from './orderInfo';
import OrderStatus from './orderStatus';
import PriceInfo from './priceInfo';

interface PullToRefreshProps {
  data: any;
  pullDownThreshold?: number;
}

export default function PullToRefresh({
  data,
  pullDownThreshold = 80,
}: PullToRefreshProps) {
  const [refreshing, setRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const startY = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch<AppDispatch>();

  const orderDetailStore = useSelector(
    (state: RootState) => state.hotelOrderDetail.orderDetail
  );
  const orderDetail =
    Object.keys(orderDetailStore).length > 0 ? orderDetailStore : data;
  const { orderNo } = orderDetail;

  const closeRefresh = () => {
    setTimeout(() => {
      setRefreshing(false);
      setPullDistance(0);
      startY.current = 0;
    }, 500);
  };

  const handleTouchStart = (e: TouchEvent) => {
    if (window.scrollY <= 0) {
      startY.current = e.touches[0].clientY;
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (window.scrollY <= 0 && startY.current > 0) {
      const y = e.touches[0].clientY;
      const distance = Math.max(0, y - startY.current);

      if (distance > 0) {
        e.preventDefault();
        setPullDistance(Math.min(distance, pullDownThreshold * 1.5));
      }
    }
  };

  const handleTouchEnd = () => {
    if (pullDistance >= pullDownThreshold) {
      setRefreshing(true);

      dispatch(getOrderDetail({ orderNo }))
        .then(() => closeRefresh())
        .catch(error => {
          closeRefresh();
          console.error('Failed to fetch order details:', error);
        });
    } else {
      setPullDistance(0);
      startY.current = 0;
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('touchstart', handleTouchStart, {
      passive: false,
    });
    container.addEventListener('touchmove', handleTouchMove, {
      passive: false,
    });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [pullDistance]);

  return (
    <div
      ref={containerRef}
      className="relative h-full"
      // style={{overflowY: 'scroll'}}
    >
      {/* 下拉提示区域 */}
      <div
        className="flex justify-center items-center w-full overflow-hidden bg-[#fff]"
        style={{
          height: `${Math.min(pullDistance, pullDownThreshold)}px`,
          transition: refreshing ? 'none' : 'height 0.7s ease-out',
        }}
      >
        {!refreshing && (
          <div
            className="transition-transform duration-200"
            style={{
              transform: `scale(${Math.min(1, pullDistance / pullDownThreshold)})`,
            }}
          >
            <img
              className="w-[70px] h-[70px]"
              src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/20250627-103649.gif"
              alt=""
            />
          </div>
        )}
      </div>

      {/* 刷新指示器 */}
      {refreshing && (
        <div className="flex justify-center items-center py-2 bg-[#fff]">
          <img
            className="w-[70px] h-[70px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/20250627-103649.gif"
            alt=""
          />
        </div>
      )}

      {/* 内容区域 */}
      <div className="pb-[100px]">
        <OrderStatus {...orderDetail} />
        <OrderInfo {...orderDetail} />
        <PriceInfo {...orderDetail} />
        <HotelAddress {...orderDetail} />
        <ItineraryInfo {...orderDetail} />
        <FloatingBall />
      </div>
    </div>
  );
}
