import { useMemo } from 'react';

import dayjs from 'dayjs';

const WEEK_DAY_MAP = {
  0: '周日',
  1: '周一',
  2: '周二',
  3: '周三',
  4: '周四',
  5: '周五',
  6: '周六',
} as const;

const DateDisplay = ({
  date,
  weekdayPosition = 'prefix',
}: {
  date: string;
  weekdayPosition?: 'prefix' | 'suffix';
}) => {
  const [month, _date, weekday] = useMemo(() => {
    const dateVm = dayjs(date);
    return [dateVm.month() + 1, dateVm.date(), WEEK_DAY_MAP[dateVm.day()]];
  }, [date]);
  return (
    <div className="text-[24px] text-[#11111E] leading-[20px] font-bold">
      {weekdayPosition === 'prefix' && (
        <span className="text-[12px] font-normal">{weekday}</span>
      )}
      <span className="text-[20px]">{month}</span>
      <span className="text-[16px]">月</span>
      <span className="text-[20px]">{_date}</span>
      <span className="text-[16px]">日</span>
      {weekdayPosition === 'suffix' && (
        <span className="text-[12px] font-normal">{weekday}</span>
      )}
    </div>
  );
};

export default DateDisplay;
