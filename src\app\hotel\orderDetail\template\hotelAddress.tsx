'use client';
import React, { useState } from 'react';

import { useApp } from '@/context';
import { MixBridge } from '@/utils/mixbridge';

interface HotelAddressProps {
  [key: string]: any;
}

const HotelAddress: React.FC<HotelAddressProps> = data => {
  const {
    hotelPictureUrl,
    hotelName,
    hotelAddress,
    hotelPhone,
    hotelLatitude,
    hotelLongitude,
  } = data;
  const { geoInfo } = useApp();

  // 复制功能
  const [isCopied, setIsCopied] = useState(false);
  const handleCopy = async (text: string) => {
    try {
      if (!navigator.clipboard) {
        // 备用方案
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      } else {
        await navigator.clipboard.writeText(text);
      }
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // 2秒后恢复
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 打电话
  const handleCall = (phoneNumber: string) => {
    window.location.href = `tel:${phoneNumber}`;
  };

  // 唤起三方地图导航
  const openMap = (lat: number, lng: number) => {
    MixBridge.openMap({
      name: hotelName,
      location: {
        latitude: geoInfo.lat,
        longitude: geoInfo.lng,
      },
      position: {
        latitude: lat,
        longitude: lng,
      },
    });
  };

  return (
    <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[4px]">
      <div className="flex items-center">
        <div className="w-[66px] h-[66px] rounded-[24px] overflow-hidden">
          <img className="w-[66px] h-[66px]" src={hotelPictureUrl} alt="" />
        </div>
        <div className="ml-[12px] flex-1">
          <p className="text-[16px] text-text-primary font-medium">
            {hotelName}
          </p>
          <p className="mt-8px text-[12px] text-text-primary">
            {hotelAddress}
            <span
              className="ml-1 cursor-pointer text-primary hover:opacity-80 transition-opacity duration-200"
              onClick={() => handleCopy(hotelAddress)}
            >
              {isCopied ? '已复制' : '复制'}
            </span>
          </p>
        </div>
      </div>
      <div className="flex items-center justify-between mt-[8px]">
        <div
          className="flex items-center justify-center h-[36px] w-[165px] bg-[#F7F7F7] rounded-[60px]"
          onClick={() => handleCall(hotelPhone)}
        >
          <img
            className="w-[16px] h-[16px] mr-[8px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/phone_icon.png"
            alt=""
          />
          <span className="text-[14px] text-black">联系酒店</span>
        </div>
        <div
          className="flex items-center justify-center h-[36px] w-[165px] bg-[#F7F7F7] rounded-[60px]"
          onClick={() => {
            openMap(Number(hotelLatitude), Number(hotelLongitude));
          }}
        >
          <img
            className="w-[16px] h-[16px] mr-[8px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/map_icon.png"
            alt=""
          />
          <span className="text-[14px] text-black">地图</span>
        </div>
        {/* 收藏 本期无 */}
        {/* <div className="flex items-center justify-center h-[36px] w-[112px] bg-[#F7F7F7] rounded-[60px]">
          <img className="w-[16px] h-[16px] mr-[8px]" src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/collect_icon.png" alt="" />
          <span className="text-[14px] text-black">收藏</span>
        </div> */}
      </div>
    </div>
  );
};

export default HotelAddress;
