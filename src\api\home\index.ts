import { request } from '@/utils/request';

export const getHomeNearbyHotels = (params: Home.GetRecommendHotelParams) =>
  request<
    API.Common.PaginatingCommonParams & {
      records: Home.IRecommendHotel[];
    }
  >('/hotel/home/<USER>/v1/queryHomeItems', {
    params,
  });

/** 获取机票登录授权码 */
export const getFlightAuthCode = (): Promise<
  App.Service.IResponse<Home.IFlightAuthResponse>
> =>
  request<Home.IFlightAuthResponse>('/hotel/user/auth/pushAndGetH5Login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });
