'use client';
import { useCallback, useEffect, useRef, useState } from 'react';

import Masonry from 'react-masonry-css';

import { getHomeNearbyHotels } from '@/api/home';
import { useApp, useHotel } from '@/context';
import { debounce } from '@/utils';

import { tryCatchTracker } from '../../../utils/track';

import RecommendHotelCard from './hotelCard';

const RecommendHotels = () => {
  const { cityInfo } = useHotel();
  const { geoInfo } = useApp();
  const [list, setList] = useState<Home.IRecommendHotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // 使用 useRef 来存储最新的参数值
  const paramsRef = useRef({
    longitude: geoInfo.lng,
    latitude: geoInfo.lat,
    cityName: cityInfo.cityName,
  });

  // 更新参数引用
  useEffect(() => {
    paramsRef.current = {
      longitude: geoInfo.lng,
      latitude: geoInfo.lat,
      cityName: cityInfo.cityName,
    };
  }, [geoInfo.lng, geoInfo.lat, cityInfo.cityName]);

  const fetchHotels = useCallback(async () => {
    try {
      setLoading(true);
      setError(false);
      const res = await getHomeNearbyHotels({
        page: 1,
        size: 10,
        longitude: paramsRef.current.longitude,
        latitude: paramsRef.current.latitude,
        cityName: paramsRef.current.cityName,
        domesticIntl: 1,
        businessType: 1,
      });
      if (res.isSuccess) {
        setList(res.data.records ?? []);
      }
    } catch (err) {
      tryCatchTracker(err, {
        scene: 'home_hotel_get_hotel_api_exception',
      });
      console.error('获取推荐酒店失败:', err);
      setError(true);
    } finally {
      setLoading(false);
    }
  }, []); // 移除所有依赖项，使用 ref 来获取最新值

  // 创建 debounced 版本的 fetchHotels，只创建一次
  const debouncedFetchRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!debouncedFetchRef.current) {
      debouncedFetchRef.current = debounce(fetchHotels, 200);
    }
  }, [fetchHotels]);

  useEffect(() => {
    // 只有当 cityInfo 和 geoInfo 都有有效值时才执行
    if (cityInfo.cityId && cityInfo.cityName && geoInfo.lng && geoInfo.lat) {
      debouncedFetchRef.current?.();
    }
  }, [cityInfo.cityId, cityInfo.cityName, geoInfo.lng, geoInfo.lat]);

  return (
    <div>
      <div className="mt-[24px] mb-[12px] leading-[22px] text-[16px] font-bold">
        推荐酒店
      </div>
      {loading ? (
        <div className="flex flex-wrap">
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              className="mb-[12px] w-[calc(50%-6px)] h-[200px] mr-[12px] [&:nth-child(2n)]:mr-[0] bg-gray-200 rounded-[20px] animate-pulse"
            />
          ))}
        </div>
      ) : error ? (
        <div className="col-span-2 text-center py-4 text-text-muted">
          加载失败，
          <button
            onClick={fetchHotels}
            className="text-primary underline hover:opacity-80 transition-opacity duration-200"
          >
            点击重试
          </button>
        </div>
      ) : (
        <Masonry
          breakpointCols={{
            default: 2,
          }}
          className="flex w-auto -ml-[12px]"
          columnClassName="pl-[12px] bg-clip-padding"
        >
          {list.map(item => (
            <RecommendHotelCard key={item.outId} {...item} />
          ))}
        </Masonry>
      )}
      {!loading && !error && (
        <div className="leading-[18px] text-center text-[12px] mt-[24px]">
          {list.length === 0 ? '暂无推荐酒店' : '没有更多了'}
        </div>
      )}
    </div>
  );
};

export default RecommendHotels;
