#!/bin/bash
set -e
log() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"; }
err() { log "错误: $1"; exit 1; }

# 检查部署包
[ -f "/home/<USER>/app/package.tgz" ] || err "部署包不存在"
log "部署包: $(du -h /home/<USER>/app/package.tgz | cut -f1)"

# 清理旧的依赖
log "清理旧的依赖..."
rm -rf /home/<USER>/app/node_modules
rm -rf /home/<USER>/app/.pnpm-store

# 解压部署包
log "解压部署包..."
tar -xzf /home/<USER>/app/package.tgz -C /home/<USER>/app --overwrite --no-same-owner --no-same-permissions || err "解压失败"
log "解压完成"

# 执行部署脚本
if [ ! -f "/home/<USER>/app/scripts/deploy/deploy.test.sh" ]; then
    err "部署脚本不存在"
fi

chmod +x /home/<USER>/app/scripts/deploy/deploy.test.sh || err "无法设置执行权限"
log "执行部署脚本..."
bash /home/<USER>/app/scripts/deploy/deploy.test.sh
deploy_status=$?

[ $deploy_status -eq 0 ] || err "部署失败，退出码: $deploy_status"
log "测试环境部署完成"
exit $deploy_status
