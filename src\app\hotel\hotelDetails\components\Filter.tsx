'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { calculateDaysBetweenDates, getMonthAndDay } from '@/utils/tools';

interface FilterProps {
  filterList: API.HotelDetails.IFilter[];
  onFilterChange?: (_conditions: string[]) => void;
  /** 入住日期 */
  arrivalDate?: string;
  /** 离店日期 */
  departureDate?: string;
  /** 日期选择回调 */
  onDateSelect?: () => void;
  /** 初始筛选条件（来自URL） */
  initialFilters?: string[];
}

const Filter = ({
  filterList,
  onFilterChange,
  arrivalDate,
  departureDate,
  onDateSelect,
  initialFilters = [],
}: FilterProps) => {
  const filters = filterList.flatMap(filter =>
    filter.subFilters.map(item => ({
      filterId: item.filterId,
      name: item.name,
    }))
  );

  // 使用初始筛选条件初始化选中状态
  const [selectedFilters, setSelectedFilters] = useState<Set<number>>(() => {
    const initialSet = new Set<number>();
    initialFilters.forEach(filterId => {
      const id = parseInt(filterId, 10);
      if (!isNaN(id)) {
        initialSet.add(id);
      }
    });
    return initialSet;
  });

  // 使用ref来访问最新的回调函数，避免useCallback的依赖问题
  const onFilterChangeRef = useRef(onFilterChange);
  onFilterChangeRef.current = onFilterChange;

  // 日期格式化和天数计算
  const { formattedArrival, formattedDeparture, totalDays } = useMemo(() => {
    if (!arrivalDate || !departureDate) {
      return {
        formattedArrival: { date: '4月23日', weekday: '周日' },
        formattedDeparture: { date: '4月25日', weekday: '周二' },
        totalDays: 2,
      };
    }

    // 使用工具函数获取月份和日期
    const arrivalInfo = getMonthAndDay(arrivalDate);
    const departureInfo = getMonthAndDay(departureDate);

    // 使用工具函数计算天数
    const days = calculateDaysBetweenDates(arrivalDate, departureDate);

    // 获取星期几
    const arrivalDay = new Date(arrivalDate).getDay();
    const departureDay = new Date(departureDate).getDay();
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

    return {
      formattedArrival: {
        date: `${arrivalInfo.month}月${arrivalInfo.day}日`,
        weekday: weekdays[arrivalDay],
      },
      formattedDeparture: {
        date: `${departureInfo.month}月${departureInfo.day}日`,
        weekday: weekdays[departureDay],
      },
      totalDays: days,
    };
  }, [arrivalDate, departureDate]);

  // 切换筛选条件选中状态
  const toggleFilter = useCallback((filterId: number) => {
    console.log('Filter组件：用户点击筛选按钮:', filterId);

    setSelectedFilters(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(filterId)) {
        newSelected.delete(filterId);
      } else {
        newSelected.add(filterId);
      }
      return newSelected;
    });
  }, []); // 空依赖数组，确保函数引用稳定

  // 使用useEffect监听selectedFilters变化，避免在setState回调中调用父组件回调
  useEffect(() => {
    const selectedConditions = Array.from(selectedFilters).map(id =>
      id.toString()
    );
    console.log('Filter组件：筛选条件变化，通知父组件:', selectedConditions);

    // 通知父组件筛选条件变化
    onFilterChangeRef.current?.(selectedConditions);
  }, [selectedFilters]);

  // 检查筛选条件是否被选中
  const isFilterSelected = (filterId: number) => selectedFilters.has(filterId);

  return (
    <div className="m-2 rounded-t-2xl bg-white px-2 py-4 flex flex-col gap-4">
      <div
        id="date"
        className="flex justify-between items-center"
        onClick={onDateSelect}
      >
        <div className="flex items-end gap-4">
          <div className="flex items-end gap-1">
            <div className="text-xl font-bold text-[#11111E]">
              {formattedArrival.date}
            </div>
            <div className="text-xs font-normal text-[#11111E] mb-0.5">
              {formattedArrival.weekday}
            </div>
          </div>
          <div className="text-xl font-bold text-[#11111E]">-</div>
          <div className="flex items-end gap-1">
            <div className="text-xl font-bold text-[#11111E]">
              {formattedDeparture.date}
            </div>
            <div className="text-xs font-normal text-[#11111E] mb-0.5">
              {formattedDeparture.weekday}
            </div>
          </div>
        </div>
        <div className="text-xs font-normal text-black flex items-center">
          <div>共{totalDays}晚</div>
          <div>
            <img
              src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotelDetails/rightIcon.png"
              alt="查看更多"
              className="flex-shrink-0 w-[10px] h-[10px]"
            />
          </div>
        </div>
      </div>
      <div className="flex flex-nowrap gap-2 overflow-x-auto scrollbar-hide py-2">
        {filters.map(filter => {
          const isSelected = isFilterSelected(filter.filterId);
          return (
            <button
              key={filter.filterId}
              onClick={() => toggleFilter(filter.filterId)}
              className={`
                px-2 py-1 rounded-[8px] text-[12px] font-medium whitespace-nowrap
                ${
                  isSelected
                    ? 'bg-primary-light text-primary'
                    : 'bg-[#f3f3f3] text-text-secondary'
                }
              `}
            >
              {filter.name}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default Filter;
