declare namespace API.HotelOrderParams {
  interface OrderPageProps {
    roomKey: string;
    roomId: string;
    ratePlanId: string;
    token: string;
    [key: string]: string | string[] | undefined;
  }

  interface tourItem {
    isMust: boolean;
    cardNo: string;
    name: string;
  }

  interface OrderTour {
    roomNum: number | string;
    linkPhone: string;
    tourList: TourItem[];
    subDisabled: boolean;
    addDisabled: boolean;
  }
}
