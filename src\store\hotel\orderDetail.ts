// src/store/OrderDetailSlice.ts
import { cancelOrderApi, getOrderDetailApi } from '@/api/hotel/orderDetail';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

interface OrderDetailState {
  orderDetail: API.HotelOrderDetailsApi.OrderDetail;
}

const initialState: OrderDetailState = {
  orderDetail: {} as API.HotelOrderDetailsApi.OrderDetail,
};

// 订单详情页接口
export const getOrderDetail = createAsyncThunk(
  'hotelOrderDetail/getOrderDetail',
  async (
    params: API.HotelOrderDetails.OrderDetailProps,
    { dispatch, rejectWithValue }
  ) => {
    try {
      const { code, isSuccess, data, message } =
        await getOrderDetailApi(params);

      if (code === 200 && isSuccess && data) {
        dispatch(setOrderDetail(data));
        return data;
      }

      return rejectWithValue(message || '请求失败');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : '未知错误'
      );
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'hotelOrderDetail/cancelOrder',
  async (
    params: API.HotelOrderDetails.OrderDetailProps,
    { rejectWithValue }
  ) => {
    try {
      const { code, isSuccess, data, message } = await cancelOrderApi(params);

      if (code === 200 && isSuccess && data) {
        return code;
      }

      return rejectWithValue(message || '取消失败');
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : '未知错误'
      );
    }
  }
);

const OrderDetailSlice = createSlice({
  name: 'hotelOrderDetail',
  initialState,
  reducers: {
    setOrderDetail: (state, action) => {
      state.orderDetail = action.payload;
    },
  },
});

export const { setOrderDetail } = OrderDetailSlice.actions;
export default OrderDetailSlice.reducer;
