import { useEffect, useRef, useState } from 'react';

import { AMAP_KEY } from '@/config';
import { FETCH_BASE_URL } from '@/const';
import { tryCatchTracker } from '@/utils/track';
import '@amap/amap-jsapi-types';

export const useAMap = () => {
  const AMapRef = useRef<Nullable<typeof AMap>>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    window._AMapSecurityConfig = {
      // securityJsCode: '2bc42ce949ce56e64a7487b7eab44925',
      // 使用nginx反向代理保护密钥，防止安全密钥被盗用
      serviceHost: `${FETCH_BASE_URL}/_AMapService/hotel/search/gaode/redirect`,
    };
  }, []);

  const loadAMap = async (
    options: {
      plugins?: string[]; //插件列表
      // 是否加载 AMapUI，缺省不加载
      AMapUI?: {
        version?: string; // AMapUI 缺省 1.1
        plugins?: string[]; // 需要加载的 AMapUI ui插件
      };
      // 是否加载 Loca， 缺省不加载
      Loca?: {
        version?: string; // Loca 版本，缺省 1.3.2
      };
    } = {}
  ) => {
    try {
      const AMapLoader = (await import('@amap/amap-jsapi-loader')).default;

      const AMap = await AMapLoader.load({
        key: AMAP_KEY,
        version: '2.0',
        ...options,
      });

      AMapRef.current = AMap;
      setIsMapReady(true);
      return AMapRef.current;
    } catch (error) {
      tryCatchTracker(error, {
        scene: 'load_amap_sdk_exception',
      });
      console.error('Failed to load AMap:', error);
    }
  };
  return {
    loadAMap,
    isMapReady,
    AMapRef,
  };
};
