'use client';
import React, { useEffect } from 'react';

import { useDispatch, useSelector } from 'react-redux';

import { Toast } from '@/components';
import type { AppDispatch, RootState } from '@/store';
import { getPriceInfo, initTourList, setTourInfo } from '@/store/hotel/order';

interface TouristsInfoProps {
  [key: string]: any;
}

/**
 * @param identification 0-无特殊验证要求（默认值） 1-整个订单至少传一个身份证 2-订单中每个房间至少传一个证件
 *   3-订单中每个房间至少传一个身份证 4-每个客人传一个身份证 5-整个订单至少传一个身份证且需预订本人入住
 *
 *   N间房--- N个名字+N个证件。（2、3、4） N间房---1个名字 + 1个证件。（1、5） N间房---1个名字（0）
 */
const TouristsInfo: React.FC<TouristsInfoProps> = ({ data }) => {
  const { identification } = data;
  const {
    inDate,
    outDate,
    hotelId,
    hotelCode,
    roomTypeId,
    payType,
    ratePlanId,
    priceLimitedType,
    isPriceLimittedProduct,
    littleMajiaId,
    goodsUniqId,
  } = data;
  const dispatch = useDispatch<AppDispatch>();

  const tourInfo = useSelector((state: RootState) => state.hotelOrder.tourInfo);
  const priceInfo = useSelector(
    (state: RootState) => state.hotelOrder.priceInfo
  );

  const {
    roomNum,
    linkPhone = '',
    tourList,
    subDisabled,
    addDisabled,
  } = tourInfo;

  // 初始化常旅
  useEffect(() => {
    dispatch(initTourList({ identification, roomNum: 1 })).catch(error =>
      console.error('初始化失败:', error)
    );
  }, []);

  // 修改数量
  const changeNum = async (type: string) => {
    if ((type === 'sub' && subDisabled) || (type === 'add' && addDisabled)) {
      Toast(`${subDisabled ? '最少' : '最多'}预定${roomNum}间`);
      return;
    }

    if (Number(roomNum) >= Number(priceInfo?.totalRoomNum || 0)) {
      Toast(`最多预定${roomNum}间`);
      return;
    }

    try {
      const roomNumNew =
        type === 'sub' ? Number(roomNum) - 1 : Number(roomNum) + 1;

      await dispatch(
        initTourList({
          identification,
          roomNum: roomNumNew,
        })
      );

      await dispatch(
        getPriceInfo({
          arrivalDate: inDate,
          departureDate: outDate,
          hotelId,
          hotelCode,
          roomTypeId,
          ratePlanId,
          paymentType: payType,
          numberOfRooms: roomNumNew,
          priceLimitedType,
          isPriceLimittedProduct,
          littleMajiaId,
          goodsUniqId,
        })
      );
    } catch (error) {
      console.error('Failed to update room number:', error);
    }
  };

  const inputTouristsInfo = async (event: any, type: string, index: number) => {
    const {
      target: { value },
    } = event;
    const tourInfoNew = { ...tourInfo };

    if (type === 'tel') {
      tourInfoNew.linkPhone = value.slice(0, 11);
    } else if (type === 'name') {
      const updatedList = tourInfoNew.tourList.map((item, i) =>
        i === index
          ? { ...item, name: value } // 创建新对象
          : item
      );
      tourInfoNew.tourList = updatedList;
    } else if (type === 'cardNo') {
      const updatedList = tourInfoNew.tourList.map((item, i) =>
        i === index
          ? { ...item, cardNo: value } // 创建新对象
          : item
      );
      tourInfoNew.tourList = updatedList;
    }

    dispatch(setTourInfo(tourInfoNew));
  };

  const loseFocusTouristsInfo = (event: any, type: string) => {
    const {
      target: { value },
    } = event;
    if (type === 'tel') {
      const validateMobile = /^1[3-9]\d{9}$/.test(value);
      if (!validateMobile) {
        // intentionally left blank to avoid no-empty ESLint error
      }
    }
  };

  return (
    <div className="mx-[8px] mt-[8px] mb-0 rounded-[24px] bg-white px-[4px] py-[4px]">
      {/* 标题+预定数量 */}
      <div className="flex items-center justify-between">
        <span className="font-medium text-[16px] text-black ml-[16px]">
          入住信息
        </span>
        <div className="flex items-center">
          {/* 库存 */}
          {priceInfo?.totalRoomNum > 0 && priceInfo?.totalRoomNum < 5 ? (
            <span className="text-[12px] text-[#F04838] mr-[8px]">
              仅剩{priceInfo.totalRoomNum}间
            </span>
          ) : null}

          {/* 修改数量 */}
          <div className="flex items-center justify-center px-[8px] py-[9px] bg-[#F7F7F7] rounded-[30px]">
            <div onClick={() => changeNum('sub')}>
              <img
                className="w-[20px] h-[20px]"
                src={`https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/${subDisabled ? 'sub_disable_icon' : 'sub_dm_icon'}.png`}
                alt=""
              />
            </div>

            <span className="font-medium text-[16px] text-black mx-[8px]">
              {roomNum}间
            </span>
            <div onClick={() => changeNum('add')}>
              <img
                className="w-[20px] h-[20px]"
                src={`https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/${addDisabled ? 'add_dm_icon' : 'add_dm_icon'}.png`}
                alt=""
              />
            </div>
          </div>
        </div>
      </div>

      {/* 入住人信息 */}
      {tourList?.length > 0 ? (
        <div>
          {/* 姓名+证件 */}
          <div className="pt-[4px]">
            {tourList?.map((item: any, index: number) => (
              <div key={index}>
                <div className="h-[52px] bg-[#F9F9F9] rounded-[20px] mt-[4px] flex items-center nter justify-between px-[12px]">
                  <div className="flex items-center">
                    <div className="flex items-center w-[81px]">
                      {/* <img className='w-[14px] h-[14px] mr-[4px]' src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/check_men_sub.png" alt="" /> */}
                      <div
                        className={`
                          text-[#66666E]
                          ${item.isMust ? " after:content-['*'] after:text-[14px] after:text-[#F04838]" : ''}
                        `}
                      >
                        入住人{tourList.length > 1 ? index + 1 : ''}
                      </div>
                    </div>
                    <input
                      className="text-[14px] text-black placeholder:text-[#CCCCCE] bg-[#F9F9F9]"
                      placeholder="请输入住客姓名"
                      type="text"
                      maxLength={30}
                      value={item.name}
                      onChange={e => inputTouristsInfo(e, 'name', index)}
                    />
                  </div>
                  <img
                    className="w-[20px] h-[20px]"
                    src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/tour_list_dm.png"
                    alt=""
                  />
                </div>
                {identification > 0 ? (
                  <div className="h-[52px] bg-[#F9F9F9] rounded-[20px] mt-[4px] flex items-center nter justify-between px-[12px]">
                    <div className="flex items-center">
                      <div className="flex items-center w-[81px]">
                        <div
                          className={`text-[#66666E] after:content-['*'] after:text-[14px] after:text-[#F04838]`}
                        >
                          身份证号码
                        </div>
                      </div>
                      <input
                        className="text-[14px] text-black placeholder:text-[#CCCCCE] bg-[#F9F9F9]"
                        placeholder="请输入住客身份证号码"
                        type="text"
                        maxLength={30}
                        onChange={e => inputTouristsInfo(e, 'cardNo', index)}
                      />
                    </div>
                  </div>
                ) : null}
              </div>
            ))}
          </div>

          {/* 提示信息 */}
          {/* <div className='pt-[4px] pb-[8px] pl-[12px] text-[12px] text-[#FB7A1E]'>请输入住客姓名，每间只需填1人，姓名不可重复</div> */}
          {/* <div className='pt-[12px] pb-[8px] pl-[12px] flex items-center'>
          <img className='w-[14px] h-[14px] mr-[4px]' src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/check_men_add.png" alt="" />
          <span className='text-[12px] text-[#568DED]'>填写房间2住客</span>
          <span className='text-[12px] text-[#99999E]'>(可选)</span>
        </div> */}

          {/* 预定手机号 */}
          <div className="h-[52px] bg-[#F9F9F9] rounded-[20px] mt-[4px] flex items-center justify-between px-[12px]">
            <div className="flex items-center">
              <div className="flex items-center w-[81px]">
                <div
                  className={`text-[#66666E] after:content-['*'] after:text-[14px] after:text-[#F04838]`}
                >
                  手机号
                </div>
              </div>
              <span className="text-[16px] text-[#000000] mr-[20px]">+86</span>
              <input
                className="text-[14px] text-black placeholder:text-[#CCCCCE] bg-[#F9F9F9]"
                placeholder="请输入住客手机号"
                type="number"
                maxLength={11}
                pattern="[0-9]*"
                value={linkPhone}
                onChange={e => inputTouristsInfo(e, 'tel', 0)}
                onBlur={e => loseFocusTouristsInfo(e, 'tel')}
              />
            </div>
            <img
              className="w-[20px] h-[20px]"
              src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/phone_dm_icon.png"
              alt=""
            />
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default TouristsInfo;
