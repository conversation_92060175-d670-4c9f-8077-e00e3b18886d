'use client';

import { useCallback, useEffect, useState } from 'react';

import { getHotelListFilter } from '@/api/hotel/hotelList';
// 骨架屏组件 - 固定标签样式，无动效
const QuickFilterSkeleton = () => (
  <div className="bg-white py-3 border-b border-gray-100 fixed top-[54px] left-0 right-0 z-40 h-[54px]">
    <div
      className="flex overflow-x-auto pb-1 scroll-smooth scrollbar-none"
      style={{
        scrollbarWidth: 'none',
        msOverflowStyle: 'none',
      }}
    >
      <div className="flex min-w-max">
        {/* 生成6个固定骨架屏标签 - 无动效 */}
        {[72, 85, 68, 60, 76].map((width, index) => (
          <div
            key={index}
            className="px-2 ml-2 rounded-[8px] bg-gray-200"
            style={{
              width: `${width}px`,
              height: '28px',
            }}
          />
        ))}
      </div>
    </div>
  </div>
);

interface FilterSectionProps {
  /** 城市ID */
  cityId: string;
  /** 选中的筛选条件 */
  selectedFilters: API.HotelList.IFilter[];
  onFilterChange?: (_conditions: API.HotelList.IFilter[]) => void;
}

const FilterSection = ({
  cityId,
  selectedFilters,
  onFilterChange,
}: FilterSectionProps) => {
  const [filters, setFilters] = useState<API.HotelList.IQuickFilterItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 根据外部传入的选中条件构建Set
  const selectedFiltersSet = new Set(
    selectedFilters.map(filter => `${filter.typeId}-${filter.filterId}`)
  );

  // 获取快筛数据
  const fetchQuickFilters = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const { code, data } = await getHotelListFilter(cityId);

      if (code === 200 && data) {
        setFilters(data);
      } else {
        setError('获取筛选数据失败');
      }
    } catch (error) {
      console.error('获取快筛数据失败:', error);
      setError('获取筛选数据失败');
    } finally {
      setIsLoading(false);
    }
  }, [cityId]);

  // 组件挂载时获取数据
  useEffect(() => {
    void fetchQuickFilters();
  }, [fetchQuickFilters]);

  // 切换筛选条件选中状态
  const toggleFilter = useCallback(
    (filter: API.HotelList.IQuickFilterItem) => {
      const filterKey = `${filter.typeId}-${filter.filterId}`;

      // 基于当前选中状态计算新的筛选条件数组
      let newSelectedConditions: API.HotelList.IFilter[];

      if (selectedFiltersSet.has(filterKey)) {
        // 如果当前已选中，则移除
        newSelectedConditions = selectedFilters.filter(
          f => `${f.typeId}-${f.filterId}` !== filterKey
        );
      } else {
        // 如果当前未选中，则添加
        newSelectedConditions = [
          ...selectedFilters,
          {
            typeId: filter.typeId,
            filterId: filter.filterId,
          },
        ];
      }

      onFilterChange?.(newSelectedConditions);
    },
    [selectedFilters, selectedFiltersSet, onFilterChange]
  );

  // 检查筛选条件是否被选中
  const isFilterSelected = (filter: API.HotelList.IQuickFilterItem) => {
    const filterKey = `${filter.typeId}-${filter.filterId}`;
    return selectedFiltersSet.has(filterKey);
  };

  // 显示骨架屏
  if (isLoading) {
    return <QuickFilterSkeleton />;
  }

  // 错误状态或无数据时不显示组件
  if (error || !filters.length) {
    return null;
  }

  return (
    <div className="bg-bg-card py-3 border-b border-bg-light fixed left-0 right-0 z-40 h-[54px]">
      {/* 筛选标签 */}
      <div
        className="flex overflow-x-auto pb-1 scroll-smooth scrollbar-none"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <div className="flex min-w-max">
          {filters.map(filter => {
            const isSelected = isFilterSelected(filter);
            return (
              <button
                key={`${filter.typeId}-${filter.filterId}`}
                onClick={() => toggleFilter(filter)}
                className={`
                  px-2 py-1 ml-2 rounded-[8px] text-[12px] font-medium whitespace-nowrap transition-colors duration-200
                  ${
                    isSelected
                      ? 'bg-primary-light text-primary'
                      : 'bg-bg-light text-text-secondary hover:bg-bg-gray'
                  }
                `}
              >
                {filter.name}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FilterSection;
