declare namespace API.HotelOrderDetailsApi {
  interface OrderDetail {
    hotelId: string;
    hotelName: string;
    hotelAddress: string;
    hotelPhone: string;
    hotelLongitude: string;
    hotelLatitude: string;
    hotelPictureUrl: string;
    roomTypeId: string;
    roomTypeName: string;
    roomSizeDesc: string | null;
    bedDesc: string | null;
    windowDesc: string | null;
    breakfastDesc: string;
    checkInDesc: string;
    checkOutDesc: string;
    supplierOrderNo: string;
    orderNo: string;
    checkInDate: string;
    checkOutDate: string;
    roomCount: number;
    nightCount: number;
    totalAmount: string;
    actualAmount: string;
    showOrderStatus: string;
    orderStatus: number;
    paymentType: number;
    paymentStatus: number;
    paymentTime: string | null;
    contactName: string;
    contactPhone: string;
    earliestArrivalTime: string;
    latestArrivalTime: string;
    currencyCode: string;
    lastCancelTime: string;
    cancelPolicy: string;
    orderCancelTime: string;
    createTime: string;
    detailInfos: OrderDetailInfo[];
    saleRoomDesc: string;
    needInvoice: number;
    lastPayTime: number;
    payStatusGoodDesc: string;
    paymentTag: string;
    selfPayAmountDesc: string | null;
    selfPayRefundDesc: string | null;
    packageInfo: PackageInfo | null;
    userHitBehavior: boolean;
  }

  interface OrderDetailInfo {
    checkInDate: string;
    mealDesc: string;
    numberOfRooms: Number;
    price: string;
  }

  interface PackageInfo {
    foodDescription: string;
    foodName: string | null;
    foodPrefix: string;
    enjoyDescription: string;
    enjoyName: string | null;
    enjoyPrefix: string;
    foodDetails: PackageItem[];
    enjoyDetails: PackageItem[];
    packageName: string | null;
  }

  interface PackageItem {
    imageUrl: string | null;
    packageName: string;
    packageDetails: PackageDetail[];
    title: string;
  }

  interface PackageDetail {
    title: string;
    description: string;
  }
}
