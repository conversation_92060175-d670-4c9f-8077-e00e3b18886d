const HotelDetailsSkeleton = () => (
  <div className="bg-gray-50 min-h-screen">
    {/* 轮播图骨架 */}
    <div className="w-full h-[300px] rounded-b-[40px] bg-gray-200" />

    {/* 酒店信息骨架 */}
    <div className="p-2 bg-white gap-2 flex flex-col">
      {/* 酒店名称骨架 */}
      <div className="h-6 bg-gray-200 rounded px-2 w-3/4" />

      {/* 主要信息区域骨架 */}
      <div className="flex items-center space-x-6 flex-1 px-2">
        {/* 左侧：装修时间和标签骨架 */}
        <div className="flex flex-col space-y-1 flex-1 min-w-0">
          {/* 装修时间骨架 */}
          <div className="h-3 bg-gray-200 rounded w-24" />

          {/* 标签骨架 */}
          <div className="flex gap-1">
            <div className="h-3 bg-gray-200 rounded w-12" />
            <div className="h-3 bg-gray-200 rounded w-16" />
            <div className="h-3 bg-gray-200 rounded w-14" />
          </div>
        </div>

        {/* 右侧：设施/服务骨架 */}
        <div className="h-3 bg-gray-200 rounded w-16" />
      </div>

      {/* 位置信息区域骨架 */}
      <div className="p-3 bg-gray-100 rounded-2xl">
        <div className="flex items-center justify-between">
          {/* 左侧：距离和位置信息骨架 */}
          <div className="flex flex-col space-y-1 flex-1">
            <div className="h-3 bg-gray-200 rounded w-32" />
            <div className="h-3 bg-gray-200 rounded w-48" />
          </div>

          {/* 右侧：地图图标骨架 */}
          <div className="w-10 h-10 bg-gray-200 rounded" />
        </div>
      </div>
    </div>

    {/* 筛选器骨架 */}
    <div className="m-2 rounded-t-2xl bg-white px-2 py-4 flex flex-col gap-4">
      {/* 日期选择骨架 */}
      <div className="flex justify-between items-center">
        <div className="flex items-end gap-4">
          <div className="flex items-end gap-1">
            <div className="h-6 bg-gray-200 rounded w-12" />
            <div className="h-4 bg-gray-200 rounded w-8" />
          </div>
          <div className="h-6 bg-gray-200 rounded w-2" />
          <div className="flex items-end gap-1">
            <div className="h-6 bg-gray-200 rounded w-12" />
            <div className="h-4 bg-gray-200 rounded w-8" />
          </div>
        </div>
        <div className="flex items-center gap-1">
          <div className="h-3 bg-gray-200 rounded w-8" />
          <div className="w-2 h-2 bg-gray-200 rounded" />
        </div>
      </div>

      {/* 筛选标签骨架 */}
      <div className="flex flex-wrap gap-2">
        <div className="px-2 py-1 rounded-lg bg-gray-200 w-16 h-7" />
        <div className="px-2 py-1 rounded-lg bg-gray-200 w-20 h-7" />
        <div className="px-2 py-1 rounded-lg bg-gray-200 w-14 h-7" />
        <div className="px-2 py-1 rounded-lg bg-gray-200 w-18 h-7" />
      </div>
    </div>

    {/* 房间卡片骨架 */}
    <div className="bg-gray-50">
      {/* 骨架房间卡片 1 */}
      <div className="bg-white mx-2 mb-2 rounded-[24px] overflow-hidden p-1">
        {/* 房型头部信息骨架 */}
        <div className="flex mb-2">
          <div className="w-[96px] h-[96px] rounded-[20px] bg-gray-200 flex-shrink-0 mr-3" />
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded my-1 w-3/4" />
            <div className="h-3 bg-gray-200 rounded mb-1 w-full" />
            <div className="flex gap-2 py-0.5">
              <div className="h-5 bg-gray-200 rounded w-12" />
              <div className="h-5 bg-gray-200 rounded w-16" />
              <div className="h-5 bg-gray-200 rounded w-14" />
            </div>
          </div>
        </div>

        {/* 价格选项骨架 */}
        <div className="p-2 bg-gray-100 rounded-[20px] mb-1">
          <div className="flex justify-between items-end">
            <div className="flex-1">
              {/* 早餐和取消政策骨架 */}
              <div className="flex items-center gap-2 mb-2">
                <div className="h-4 bg-gray-200 rounded w-16" />
                <div className="w-[1px] h-[14px] bg-gray-300" />
                <div className="h-4 bg-gray-200 rounded w-20" />
              </div>

              {/* 房型信息骨架 */}
              <div className="h-3 bg-gray-200 rounded mb-3 w-2/3" />

              {/* 政策标签骨架 */}
              <div className="flex gap-2 mb-2">
                <div className="h-5 bg-gray-200 rounded w-12" />
                <div className="h-5 bg-gray-200 rounded w-16" />
              </div>
            </div>

            {/* 价格和预订按钮骨架 */}
            <div className="flex items-center ml-4">
              <div className="h-3 bg-gray-200 rounded w-2 mr-1" />
              <div className="h-6 bg-gray-200 rounded w-12 mr-2" />
              <div className="bg-gray-200 h-[48px] w-[48px] rounded-lg" />
            </div>
          </div>
        </div>
      </div>

      {/* 骨架房间卡片 2 */}
      <div className="bg-white mx-2 mb-2 rounded-[24px] overflow-hidden p-1">
        {/* 房型头部信息骨架 */}
        <div className="flex mb-2">
          <div className="w-[96px] h-[96px] rounded-[20px] bg-gray-200 flex-shrink-0 mr-3" />
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded my-1 w-2/3" />
            <div className="h-3 bg-gray-200 rounded mb-1 w-full" />
            <div className="flex gap-2 py-0.5">
              <div className="h-5 bg-gray-200 rounded w-14" />
              <div className="h-5 bg-gray-200 rounded w-12" />
              <div className="h-5 bg-gray-200 rounded w-18" />
            </div>
          </div>
        </div>

        {/* 价格选项骨架 */}
        <div className="p-2 bg-gray-100 rounded-[20px] mb-1">
          <div className="flex justify-between items-end">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-4 bg-gray-200 rounded w-14" />
                <div className="w-[1px] h-[14px] bg-gray-300" />
                <div className="h-4 bg-gray-200 rounded w-18" />
              </div>
              <div className="h-3 bg-gray-200 rounded mb-3 w-1/2" />
              <div className="flex gap-2 mb-2">
                <div className="h-5 bg-gray-200 rounded w-16" />
                <div className="h-5 bg-gray-200 rounded w-14" />
              </div>
            </div>
            <div className="flex items-center ml-4">
              <div className="h-3 bg-gray-200 rounded w-2 mr-1" />
              <div className="h-6 bg-gray-200 rounded w-12 mr-2" />
              <div className="bg-gray-200 h-[48px] w-[48px] rounded-lg" />
            </div>
          </div>
        </div>
      </div>

      {/* 骨架房间卡片 3 */}
      <div className="bg-white mx-2 mb-2 rounded-[24px] overflow-hidden p-1">
        {/* 房型头部信息骨架 */}
        <div className="flex mb-2">
          <div className="w-[96px] h-[96px] rounded-[20px] bg-gray-200 flex-shrink-0 mr-3" />
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded my-1 w-4/5" />
            <div className="h-3 bg-gray-200 rounded mb-1 w-full" />
            <div className="flex gap-2 py-0.5">
              <div className="h-5 bg-gray-200 rounded w-16" />
              <div className="h-5 bg-gray-200 rounded w-14" />
            </div>
          </div>
        </div>

        {/* 价格选项骨架 */}
        <div className="p-2 bg-gray-100 rounded-[20px]">
          <div className="flex justify-between items-end">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-4 bg-gray-200 rounded w-18" />
                <div className="w-[1px] h-[14px] bg-gray-300" />
                <div className="h-4 bg-gray-200 rounded w-16" />
              </div>
              <div className="h-3 bg-gray-200 rounded mb-3 w-3/5" />
              <div className="flex gap-2 mb-2">
                <div className="h-5 bg-gray-200 rounded w-14" />
                <div className="h-5 bg-gray-200 rounded w-12" />
                <div className="h-5 bg-gray-200 rounded w-16" />
              </div>
            </div>
            <div className="flex items-center ml-4">
              <div className="h-3 bg-gray-200 rounded w-2 mr-1" />
              <div className="h-6 bg-gray-200 rounded w-12 mr-2" />
              <div className="bg-gray-200 h-[48px] w-[48px] rounded-lg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default HotelDetailsSkeleton;
