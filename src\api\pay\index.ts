import { request } from '@/utils/request';

/** 获取支付方式列表 */
export const getPaymentTypeList = (): Promise<
  App.Service.IResponse<Pay.IPaymentType[]>
> =>
  request<Pay.IPaymentType[]>(
    '/hotel/payment/lianLianQuery/v1/queryPaymentTypeList',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

/** 调用支付接口 */
export const invocationPayment = (
  params: Pay.IPaymentRequest
): Promise<App.Service.IResponse<Pay.IPaymentResponse>> =>
  request<Pay.IPaymentResponse>(
    '/hotel/payment/lianLianPay/v1/invocationPayment',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: params,
    }
  );

/** 查询支付状态 */
export const queryPaymentStatus = (
  params: Pay.IPaymentStatusRequest
): Promise<App.Service.IResponse<Pay.IPaymentStatusResponse>> =>
  request<Pay.IPaymentStatusResponse>(
    '/hotel/payment/lianLianQuery/v1/queryPaymentStatus',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: params,
    }
  );
