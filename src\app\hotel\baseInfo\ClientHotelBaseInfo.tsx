'use client';

import React from 'react';

import { Element } from 'react-scroll';

// FacilityItem组件，使用react-scroll的Element包装
const FacilityItem = ({
  children,
  title,
  id,
  isLast = false,
}: {
  children: React.ReactNode;
  title: string;
  id: string;
  isLast?: boolean;
}) => (
  <Element
    name={id}
    className={`bg-white rounded-3xl p-[12px] mb-[8px] ${isLast ? 'mb-[300px]' : ''}`}
  >
    <div className="text-[16px] font-medium">{title}</div>
    {children}
  </Element>
);

interface ClientHotelBaseInfoProps {
  data: {
    facilityModel: API.HotelBaseInfo.IFacilityModel;
    detailPolicyInfo: API.HotelBaseInfo.IDetailPolicyInfo[];
  };
  navbarTotalHeight: number;
  isDreameApp: boolean;
}

const ClientHotelBaseInfo: React.FC<ClientHotelBaseInfoProps> = ({
  data,
  navbarTotalHeight,
  isDreameApp,
}) => {
  const { facilityModel, detailPolicyInfo } = data;

  return (
    <div className="w-screen min-h-screen pb-[40px]">
      <div
        className="pt-2 px-3"
        style={{
          marginTop: `${navbarTotalHeight - (isDreameApp ? 40 : 0)}px`,
        }}
      >
        <FacilityItem
          id={facilityModel.morFacList[0].anchorId}
          title="设施服务"
        >
          {/* 渲染更多设施列表 */}
          {facilityModel?.morFacList?.length > 0 && (
            <div className="mt-[8px]">
              {facilityModel.morFacList.map(facilityType => (
                <div
                  key={`fac-type-${facilityType.facilityTypeId}`}
                  className="mb-[16px] last:mb-0"
                >
                  {/* 设施类型标题和图标 */}
                  <div className="flex items-center mb-[8px]">
                    {facilityType.iconUrl && (
                      <img
                        src={facilityType.iconUrl}
                        alt={facilityType.facilityTypeName}
                        className="w-[16px] h-[16px] mr-[8px]"
                      />
                    )}
                    <div className="text-[14px] font-medium">
                      {facilityType.facilityTypeName}
                    </div>
                  </div>

                  {/* 设施项目列表 */}
                  <div className="grid grid-cols-2 gap-3 ml-[24px] grid-fallback">
                    {facilityType.facList?.map((item, index) => (
                      <div
                        key={`more-fac-${item.facilityId}-${index}`}
                        className="flex items-center py-[6px]"
                      >
                        <div className="text-[14px] text-[#33333e]">
                          {item.facilityName}
                        </div>
                        {item.tagList && item.tagList.length > 0 && (
                          <div className="text-[10px] text-[#fb7a1e] bg-[#fff6f0] px-[4px] rounded-[2px] ml-[4px]">
                            {item.tagList[0].desc}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </FacilityItem>

        {detailPolicyInfo.map(
          (item: API.HotelBaseInfo.IDetailPolicyInfo, index) => (
            <FacilityItem
              key={`policy-${item.firstTitle.title}`}
              id={item.anchorId}
              title={item.firstTitle.title}
              isLast={index === detailPolicyInfo.length - 1} // 最后一个元素添加额外底部边距
            >
              {item.descList.map(
                (desc: API.HotelBaseInfo.IDetailPolicyDesc) => (
                  <div key={`policy-desc-${desc.secondTitle.title}`}>
                    <div className="flex items-center py-[12px] mt-2">
                      {desc.secondTitle.title && (
                        <img
                          src={desc.secondTitle.iconUrl}
                          alt={desc.secondTitle.title}
                          className="w-[16px] h-[16px]"
                        />
                      )}
                      <div className="text-[14px] font-medium ml-[8px]">
                        {desc.secondTitle.title}
                      </div>
                    </div>
                    <div className="text-[14px] text-[#33333e] ml-[24px]">
                      {desc.detail.detailDesc.map(
                        (detailItem: API.HotelBaseInfo.IDetailDesc) => (
                          <div
                            key={`detail-${detailItem.info.substring(0, 10)}`}
                            className="last:mb-0 mb-[2px]"
                          >
                            {detailItem.info}
                          </div>
                        )
                      )}
                    </div>
                  </div>
                )
              )}
            </FacilityItem>
          )
        )}
      </div>
    </div>
  );
};

export default ClientHotelBaseInfo;
