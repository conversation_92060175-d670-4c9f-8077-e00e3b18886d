/** JSBridge工具 - 用于与Flutter原生应用进行交互 类型定义在 src/types/modules/native.d.ts 中全局声明 */

import { tryCatchTracker } from '../track';

import { OwnApp } from './ownApp';
import {
  getAppVersion,
  getDeviceInfo,
  getDeviceUUID,
  getPlatform,
  getSafeAreaHeight,
  getUserId,
  isAndroidDevice,
  isInApp,
  isIOSDevice,
  isMobileDevice,
  parseUserAgent,
} from './uaParser';

const CHANNEL_NAME = 'messageChannel';

// 全局日志函数，可以被外部设置
let globalLogFunction: ((_message: string) => void) | null = null;

/** 设置全局日志函数 */
export const setNativeLogFunction = (logFn: (_message: string) => void) => {
  globalLogFunction = logFn;
};

/** 内部日志函数 */
const log = (message: string) => {
  // 使用 console.warn 避免 ESLint 错误
  console.warn(`[Native Bridge] ${message}`);
  if (globalLogFunction) {
    globalLogFunction(message);
  }

  // 同时输出到页面日志区域（如果存在）
  if (typeof window !== 'undefined' && (window as any).logMessage) {
    (window as any).logMessage(`[Native Bridge] ${message}`);
  }
};

/** 检查Flutter WebView是否可用 */
const isFlutterAvailable = (): boolean =>
  typeof window !== 'undefined' && !!window.flutter_inappwebview;

/** 检查是否在追觅App壳内 */
const isDreameApp = (_userAgent?: string): boolean => {
  if (typeof window === 'undefined') {
    return _userAgent?.includes('_DreameHome_') ?? false;
  }
  const { userAgent } = window.navigator;
  return userAgent.includes('_DreameHome_');
};

/** 检查是否在自有App壳内 */
const isOwnApp = (userAgent?: string): boolean => OwnApp.isInApp(userAgent);

/** 检查是否在任何App壳内 */
const isInNativeApp = (userAgent?: string): boolean =>
  isDreameApp(userAgent) || isOwnApp(userAgent);

/** 获取当前App壳类型 */
const getAppShellType = (userAgent?: string): AppShellType => {
  if (isDreameApp(userAgent)) return 'dreame';
  if (isOwnApp(userAgent)) return 'own';
  if (typeof window !== 'undefined') return 'browser';
  return 'unknown';
};

// 存储待处理的Promise回调，按消息类型分组
const pendingCallbacks = new Map<
  string,
  {
    resolve: (_value: unknown) => void;
    reject: (_error: Error) => void;
    timeoutId?: NodeJS.Timeout;
  }
>();

// 防止重复初始化的标志
let isInitialized = false;

/** 初始化原生消息监听器 */
const initNativeMessageListener = (): void => {
  if (typeof window === 'undefined') return;

  if (isInitialized) {
    log(`⚠️ Native Bridge 已初始化，跳过重复初始化`);
    return;
  }

  log(`🚀 开始初始化 Native Bridge`);
  isInitialized = true;

  // 通用回调处理函数 - 按消息类型匹配
  const handleCallback = (response: any, callbackType: string) => {
    log(`收到${callbackType}回调: ${JSON.stringify(response)}`);

    // 尝试获取消息类型 - 优先从params字段解析（追觅特有的结构）
    let messageType: string | undefined;

    // 首先检查params字段（追觅特有的结构）
    if (response?.params) {
      try {
        // params可能是JSON字符串，需要解析
        const paramsData =
          typeof response.params === 'string'
            ? JSON.parse(response.params)
            : response.params;
        messageType = paramsData?.type;
        log(`从params字段解析到消息类型: ${messageType}`);
      } catch (e) {
        log(`解析params字段失败: ${e}`);
      }
    }

    // 如果params中没有找到，再检查其他字段
    if (!messageType) {
      messageType = response?.type;
      if (messageType) {
        log(`从type字段获取消息类型: ${messageType}`);
      }
    }

    // 检查data字段
    if (!messageType && response?.data) {
      messageType = response.data.type;
      if (messageType) {
        log(`从data.type字段获取消息类型: ${messageType}`);
      }
    }

    // 如果还是没找到，尝试从其他字段获取
    if (!messageType) {
      const possibleTypes = ['messageType', 'msgType', 'action'];
      for (const field of possibleTypes) {
        if (response?.[field]) {
          messageType = response[field];
          log(`从${field}字段获取消息类型: ${messageType}`);
          break;
        }
      }
    }

    const data = response?.data ?? response;

    log(`🔍 回调数据分析:`);
    log(`  - 原始回调: ${JSON.stringify(response)}`);
    log(`  - 解析的消息类型: ${messageType}`);
    log(`  - 数据部分: ${JSON.stringify(data)}`);

    if (!messageType) {
      log(`❌ 回调中未找到消息类型，无法匹配回调`);
      log(`  - 回调包含的字段: ${Object.keys(response ?? {}).join(', ')}`);
      return;
    }

    // 查找匹配的回调（按消息类型）
    const callback = pendingCallbacks.get(messageType);

    if (callback) {
      log(`✅ 找到匹配的回调，消息类型: ${messageType}`);

      // 清除超时定时器
      if (callback.timeoutId) {
        clearTimeout(callback.timeoutId);
      }

      // 从待处理列表中移除
      pendingCallbacks.delete(messageType);

      // 执行回调 - 原生端返回数据表示成功
      callback.resolve(data);
    } else {
      log(`❌ 未找到匹配的回调，消息类型: ${messageType}`);
      log(
        `  - 当前待处理的消息类型: [${Array.from(pendingCallbacks.keys()).join(', ')}]`
      );
      log(
        `  - 消息类型比较: 回调中的"${messageType}" vs 待处理的"${Array.from(pendingCallbacks.keys()).join('", "')}"`
      );
    }
  };

  // 避免重复设置监听器
  if (!window.onAppMessage) {
    log(`🔧 设置 onAppMessage 监听器`);
    window.onAppMessage = (response: NativeCallbackResponse) => {
      log(`🔔 onAppMessage 被调用`);
      handleCallback(response, '原生端');
    };
  } else {
    log(`⚠️ onAppMessage 监听器已存在，跳过设置`);
  }

  if (!window.onSharedStorage) {
    log(`🔧 设置 onSharedStorage 监听器`);
    window.onSharedStorage = (response: NativeCallbackResponse) => {
      log(`🔔 onSharedStorage 被调用`);
      handleCallback(response, '共享存储');
    };
  } else {
    log(`⚠️ onSharedStorage 监听器已存在，跳过设置`);
  }
};

/**
 * 发送消息到原生端并返回Promise
 *
 * @param message 消息对象
 * @param options 配置选项
 */
const sendMessageWithPromise = <T = any>(
  message: NativeMessage,
  options: NativeCallOptions = {}
): Promise<NativeCallResult<T>> =>
  new Promise(resolve => {
    if (!isFlutterAvailable()) {
      resolve({
        success: false,
        error: 'Flutter WebView not available',
      });
      return;
    }

    const webview = window.flutter_inappwebview;
    if (!webview) {
      resolve({
        success: false,
        error: 'Flutter WebView not available',
      });
      return;
    }

    // 不再使用requestId，直接使用原始消息

    // 设置超时处理，默认10秒
    const timeout = options.timeout ?? 10000;

    log(`📤 发送消息到原生端:`);
    log(`  - 消息类型: ${message.type}`);
    log(`  - 完整消息: ${JSON.stringify(message)}`);
    log(`  - 超时时间: ${timeout}ms`);

    const timeoutId = setTimeout(() => {
      log(`⏰ 请求超时，消息类型: ${message.type}, 超时时间: ${timeout}ms`);
      // 从待处理列表中移除
      pendingCallbacks.delete(message.type);

      // 返回超时结果
      resolve({
        success: false,
        error: `Native call timeout after ${timeout}ms`,
        isTimeout: true,
      });
    }, timeout);

    // 存储回调（使用消息类型作为key）
    pendingCallbacks.set(message.type, {
      resolve: (data: unknown) => {
        log(`✅ 执行成功回调，消息类型: ${message.type}`);
        clearTimeout(timeoutId);
        resolve({
          success: true,
          data: data as T,
        });
      },
      reject: (error: Error) => {
        log(
          `❌ 执行失败回调，消息类型: ${message.type}, error: ${error.message}`
        );
        clearTimeout(timeoutId);
        resolve({
          success: false,
          error: error.message,
        });
      },
      timeoutId,
    });

    log(`📝 已将消息类型 ${message.type} 添加到待处理列表`);
    log(
      `📝 当前待处理的消息类型: [${Array.from(pendingCallbacks.keys()).join(', ')}]`
    );

    // 发送消息
    try {
      const messageStr = JSON.stringify(message);
      log(`发送消息到原生端: ${messageStr}`);

      if (webview.callHandler) {
        webview.callHandler(CHANNEL_NAME, messageStr);
        log('使用 callHandler 发送消息');
      } else if (webview._callHandler) {
        webview._callHandler(CHANNEL_NAME, null, messageStr);
        log('使用 _callHandler 发送消息');
      } else {
        // 清理回调
        pendingCallbacks.delete(message.type);
        clearTimeout(timeoutId);
        log('没有可用的调用处理方法');
        resolve({
          success: false,
          error: 'No available call handler method',
        });
      }
    } catch (error) {
      // 清理回调
      pendingCallbacks.delete(message.type);
      clearTimeout(timeoutId);
      log(`发送消息到原生端失败: ${error}`);
      tryCatchTracker(error, {
        scene: 'jsbridge_sendMessageWithPromise',
        message,
      });
      resolve({
        success: false,
        error: `Failed to send message to native: ${error}`,
      });
    }
  });

/**
 * 发送消息到原生端（不等待回调）
 *
 * @param message 消息对象
 */
const sendMessage = (message: NativeMessage): void => {
  if (!isFlutterAvailable()) {
    console.warn('Flutter WebView not available');
    return;
  }

  const messageStr = JSON.stringify(message);
  const webview = window.flutter_inappwebview;
  if (!webview) {
    console.warn('Flutter WebView not available');
    return;
  }
  try {
    if (webview.callHandler) {
      webview.callHandler(CHANNEL_NAME, messageStr);
    } else if (webview._callHandler) {
      webview._callHandler(CHANNEL_NAME, null, messageStr);
    } else {
      console.warn('No available call handler method');
    }
  } catch (error) {
    console.error('Failed to send message to native:', error);
    tryCatchTracker(error, { scene: 'jsbridge_sendMessage', message });
  }
};

// 初始化消息监听器
if (typeof window !== 'undefined') {
  initNativeMessageListener();
}

/** Native JSBridge 对象 */
export const Native = {
  // === 环境检测方法 ===

  /** 检查是否在追觅App壳内 */
  isDreameApp,

  /** 检查是否在自有App壳内 */
  isOwnApp,

  /** 检查是否在任何App壳内 */
  isInNativeApp,

  /** 获取当前App壳类型 */
  getAppShellType,

  // === 通用方法（兼容多种壳） ===

  /**
   * 关闭当前页面
   *
   * @param isClose 是否关闭页面（自有壳参数）
   */
  closePage: (isClose?: boolean): void => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        // 自有壳需要 boolean 参数
        OwnApp.closePage(isClose ?? true);
        break;

      case 'dreame':
        // 追觅壳通过发送消息关闭
        sendMessage({
          type: 'closeWebView',
          data: {},
        });
        break;

      case 'browser':
      case 'unknown':
      default:
        console.warn('Not in any native app, cannot close page');
        break;
    }
  },

  /**
   * 跳转页面
   *
   * @param url 目标URL
   * @param customJump 自定义跳转方法（自有壳参数）
   */
  jumpPage: (url: string, customJump?: (_url: string) => void): void => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        OwnApp.jumpPage(url, customJump);
        break;

      case 'dreame':
        // 追觅壳通过发送消息跳转
        sendMessage({
          type: 'openWebview',
          data: { url },
        });
        break;

      case 'browser':
        // 浏览器环境，使用自定义跳转或默认跳转
        if (typeof customJump === 'function') {
          customJump(url);
        } else if (typeof window !== 'undefined') {
          window.location.href = url;
        }
        break;

      case 'unknown':
      default:
        console.warn('Unknown environment, cannot jump to page');
        break;
    }
  },
  /**
   * 跳转页面
   *
   * @param url 目标URL
   * @param customJump 自定义跳转方法（自有壳参数）
   */
  jumpOuterPage: (url: string, customJump?: (_url: string) => void): void => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        OwnApp.jumpPage(url, customJump);
        break;

      case 'dreame':
        // 追觅壳通过发送消息跳转
        sendMessage({
          type: 'openOuterPage',
          data: {
            url,
            extras: { 'load-close': '1' },
          },
        });
        break;

      case 'browser':
        // 浏览器环境，使用自定义跳转或默认跳转
        if (typeof customJump === 'function') {
          customJump(url);
        } else if (typeof window !== 'undefined') {
          window.location.href = url;
        }
        break;

      case 'unknown':
      default:
        console.warn('Unknown environment, cannot jump to page');
        break;
    }
  },

  /**
   * 设置页面标题
   *
   * @param title 页面标题
   */
  setPageTitle: (title: string): void => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        OwnApp.setPageTitle(title);
        break;

      case 'dreame':
        // 追觅壳通过发送消息设置标题
        sendMessage({
          type: 'setPageTitle',
          data: { title },
        });
        break;

      case 'browser':
        // 浏览器环境，设置网页标题
        if (typeof document !== 'undefined') {
          document.title = title;
        }
        break;

      case 'unknown':
      default:
        console.warn('Unknown environment, cannot set page title');
        break;
    }
  },

  /** 返回上一页 */
  goBack: (): void => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        OwnApp.goBack();
        break;

      case 'dreame':
        // 追觅壳通过发送消息返回
        sendMessage({
          type: 'goBack',
          data: {},
        });
        break;

      case 'browser':
        // 浏览器环境，使用浏览器返回
        if (typeof window !== 'undefined' && window.history.length > 1) {
          window.history.back();
        }
        break;

      case 'unknown':
      default:
        console.warn('Unknown environment, cannot go back');
        break;
    }
  },

  /** 获取用户Token */
  getUserToken: (): string => {
    const shellType = getAppShellType();

    switch (shellType) {
      case 'own':
        return OwnApp.getUserToken();

      case 'dreame':
        // 追觅壳暂时返回空字符串，可以根据需要实现异步获取
        console.warn('getUserToken not implemented for Dreame app');
        return '';

      case 'browser':
        console.warn('Not in any native app, cannot get user token');
        return '';

      case 'unknown':
      default:
        console.warn('Unknown environment, cannot get user token');
        return '';
    }
  },

  // === 追觅专用方法 ===

  /**
   * 获取位置信息
   *
   * @param options 配置选项
   */
  getLocation: (
    options?: NativeCallOptions
  ): Promise<NativeCallResult<LocationData>> =>
    sendMessageWithPromise<LocationData>(
      {
        type: 'getLocation',
        data: {},
      },
      options
    ),
  /**
   * 监听app切换后台返回到前台
   *
   * @param options 配置选项
   */
  onAppPageShow: (
    options?: NativeCallOptions
  ): Promise<NativeCallResult<LocationData>> =>
    sendMessageWithPromise<LocationData>(
      {
        type: 'onAppPageShow',
        data: { code: 0, result: 1 },
      },
      options
    ),
  /**
   * 获取联系人
   *
   * @param options 配置选项
   */
  getContact: (options?: NativeCallOptions): Promise<NativeCallResult> =>
    sendMessageWithPromise(
      {
        type: 'getContact',
        data: {},
      },
      options
    ),

  /**
   * 获取地图信息
   *
   * @param options 配置选项
   */
  getMap: (options?: NativeCallOptions): Promise<NativeCallResult> =>
    sendMessageWithPromise(
      {
        type: 'getMap',
        data: {},
      },
      options
    ),

  /**
   * 跳转到地图
   *
   * @param location 位置坐标
   */
  openMap: (location: LocationData): void => {
    sendMessage({
      type: 'jump2Map',
      data: {
        wgs84: location,
        gcj02: location,
      },
    });
  },

  // === 新增的追觅专用方法 ===

  /**
   * 获取位置信息（带消息）
   *
   * @param options 配置选项
   */
  getLocationWithMessage: (
    options?: NativeCallOptions
  ): Promise<NativeCallResult<LocationData>> =>
    sendMessageWithPromise<LocationData>(
      {
        type: 'getLocation',
        data: {
          msg: '允许地理位置权限后,翎游会推荐附近酒店和其他服务，用户可以在手机设置中的App隐私权限管理页面上随时开启/关闭位置信息读取',
        },
      },
      options
    ),

  /**
   * 获取联系人（带消息）
   *
   * @param message 请求消息
   * @param options 配置选项
   */
  getContactWithMessage: (
    message: string,
    options?: NativeCallOptions
  ): Promise<NativeCallResult> =>
    sendMessageWithPromise(
      {
        type: 'getContact',
        data: { msg: message },
      },
      options
    ),

  /**
   * 跳转到地图
   *
   * @param mapData 地图数据（包含wgs84和gcj02坐标）
   */
  jumpToMap: (mapData: MapData): void => {
    sendMessage({
      type: 'jump2Map',
      data: mapData,
    });
  },

  /**
   * 设置共享存储
   *
   * @param key 存储键
   * @param value 存储值
   * @param options 配置选项
   */
  setSharedStorage: (
    key: string,
    value: string,
    options?: NativeCallOptions
  ): Promise<NativeCallResult> =>
    sendMessageWithPromise(
      {
        type: 'sharedStorage',
        data: {
          type: 'set',
          data: { key, value },
        },
      },
      options
    ),

  /**
   * 获取共享存储
   *
   * @param key 存储键
   * @param options 配置选项
   */
  getSharedStorage: (
    key: string,
    options?: NativeCallOptions
  ): Promise<NativeCallResult<string>> =>
    sendMessageWithPromise<string>(
      {
        type: 'sharedStorage',
        data: {
          type: 'get',
          data: { key },
        },
      },
      options
    ),

  /**
   * 删除共享存储
   *
   * @param key 存储键
   * @param options 配置选项
   */
  removeSharedStorage: (
    key: string,
    options?: NativeCallOptions
  ): Promise<NativeCallResult> =>
    sendMessageWithPromise(
      {
        type: 'sharedStorage',
        data: {
          type: 'remove',
          data: { key },
        },
      },
      options
    ),

  // === UserAgent 解析方法 ===

  /** 解析 UserAgent 获取设备信息 */
  getUserAgent: parseUserAgent,

  /** 获取设备信息 */
  getDeviceInfo,

  /** 检查是否在App内 */
  getIsInApp: isInApp,

  /** 获取平台信息 */
  getPlatform,

  /** 获取应用版本 */
  getAppVersion,

  /** 获取用户ID */
  getUserId,

  /** 获取设备UUID */
  getDeviceUUID,

  /** 获取安全区域高度 */
  getSafeAreaHeight,

  /** 检测是否为移动设备 */
  isMobileDevice,

  /** 检测是否为Android设备 */
  isAndroidDevice,

  /** 检测是否为iOS设备 */
  isIOSDevice,
};
