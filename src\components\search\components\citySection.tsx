'use client';

import { useEffect, useMemo, useState } from 'react';

import HistorySection from './historySection';
import { ScrollHighlight } from './scrollHighlight';

import type { CitySectionProps } from '../types';

// 历史记录相关工具函数（与hotelSection一致）
const HISTORY_KEY = 'search_history';
const MAX_HISTORY_COUNT = 6;

function getHistory(): Search.ISearchResultItem[] {
  if (typeof window === 'undefined') return [];
  try {
    const history = localStorage.getItem(HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch {
    return [];
  }
}

function addToHistory(item: Search.ISearchResultItem) {
  if (typeof window === 'undefined') return;
  try {
    const history = getHistory();
    // 以type+itemName+cityId为唯一
    const filtered = history.filter(
      h =>
        !(
          h.type === item.type &&
          h.itemName === item.itemName &&
          h.cityId === item.cityId
        )
    );
    const newHistory = [item, ...filtered].slice(0, MAX_HISTORY_COUNT);
    localStorage.setItem(HISTORY_KEY, JSON.stringify(newHistory));
  } catch (error) {
    console.warn('Failed to save search history:', error);
  }
}

function clearHistory() {
  if (typeof window === 'undefined') return;
  try {
    localStorage.removeItem(HISTORY_KEY);
  } catch (error) {
    console.warn('Failed to clear search history:', error);
  }
}

export const CitySection: React.FC<CitySectionProps> = ({
  cityData,
  onCitySelect,
  currentCity,
  LocationComp,
  onClose,
  onHotelHistoryClick,
}) => {
  const [history, setHistory] = useState<Search.ISearchResultItem[]>([]);
  // 新增唯一选中索引状态，默认选中'热门'
  const [selectedLetter, setSelectedLetter] = useState<string>('热门');

  // 初始化历史记录
  useEffect(() => {
    setHistory(getHistory());
  }, []);
  // 调试信息
  console.warn('CitySection - cityData:', cityData);
  console.warn('CitySection - hotCities:', cityData?.hotCities?.length);
  console.warn('CitySection - cities:', cityData?.cities?.length);

  // 生成字母索引 - 添加"历史"和"热门"选项
  const letterIndexes = useMemo(() => {
    const indexes = [];
    if (history.length > 0) {
      indexes.push('历史');
    }
    indexes.push('热门');
    indexes.push(...cityData.cities.map(group => group.letter));
    return indexes;
  }, [cityData.cities, history.length]);

  // 处理城市选择
  const handleCitySelect = (city: Search.ICityInfo) => {
    console.warn('City selected:', city);
    // 构造Search.ISearchResultItem对象
    const cityItem: Search.ISearchResultItem = {
      type: 1,
      typeName: '城市',
      itemName: city.cityName,
      countryId: '',
      countryName: city.countryName || '',
      cityId: city.cityId,
      cityName: city.cityName,
      address: '',
      itemPrice: 0,
      currencyCode: '',
      currencySymbol: '',
      district: '',
      districtName: '',
      businessZone: '',
      businessZoneName: '',
      brandName: '',
      reviewScore: undefined,
    };
    addToHistory(cityItem);
    setHistory(getHistory());
    onCitySelect?.(city);
  };

  // 清空历史记录
  const handleClearHistory = () => {
    clearHistory();
    setHistory([]);
  };

  // 处理字母索引点击 - 用React状态管理唯一选中
  const handleLetterClick = (letter: string, _index: number) => {
    setSelectedLetter(letter);
    if (letter === '历史') {
      const historySection = document.getElementById('history-cities');
      if (historySection) {
        historySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else if (letter === '热门') {
      const hotSection = document.getElementById('hot-cities');
      if (hotSection) {
        hotSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    } else {
      const element = document.getElementById(`letter-${letter}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  // 处理历史记录点击
  const handleHistoryClick = (item: Search.ISearchResultItem) => {
    if (item.type === 1) {
      onCitySelect?.({ cityId: item.cityId, cityName: item.cityName });
    } else if (item.type === 5) {
      onHotelHistoryClick?.(item);
    }
  };

  // 处理当前位置点击
  const handleCurrentLocationClick = () => {
    if (currentCity.isUserLocation) {
      onCitySelect?.({
        cityId: currentCity.cityId,
        cityName: currentCity.cityName,
      });
    } else if (typeof window !== 'undefined' && typeof onClose === 'function') {
      onClose();
    }
  };

  // 如果没有城市数据，显示错误信息
  if (!cityData?.hotCities || !cityData.cities) {
    return (
      <div className="flex-1 bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-sm text-gray-500 mb-2">城市数据加载失败</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-white flex flex-col relative">
      {/* 可滚动的内容容器 */}
      <div
        className="flex-1 overflow-y-auto city-scroll-container mr-[25px]"
        style={{
          WebkitOverflowScrolling: 'touch',
        }}
      >
        {/* 当前位置 */}
        <div className="px-4 py-4 bg-white">
          <h3 className="text-text-primary mb-3" style={{ fontSize: '14px' }}>
            当前位置
          </h3>
          <div className="flex gap-3">
            {currentCity.isUserLocation ? (
              <>
                {/* 左侧城市按钮 */}
                <button
                  className="bg-primary-light text-primary flex items-center justify-center transition-colors hover:opacity-80"
                  style={{
                    fontSize: '14px',
                    width: '83px',
                    height: '52px',
                    borderRadius: '12px',
                  }}
                  onClick={handleCurrentLocationClick}
                >
                  {currentCity.cityName}
                </button>

                {/* 右侧详细地址 */}
                <button
                  className="bg-primary-light text-left px-3 transition-colors hover:opacity-80 flex-1 flex flex-col justify-center"
                  style={{
                    borderRadius: '12px',
                    height: '52px',
                  }}
                  onClick={handleCurrentLocationClick}
                >
                  <div className="text-primary" style={{ fontSize: '14px' }}>
                    {currentCity?.cityName}
                  </div>
                  <div
                    className="text-primary mt-1"
                    style={{ fontSize: '12px' }}
                  >
                    {currentCity?.address}
                  </div>
                </button>
              </>
            ) : (
              <LocationComp />
            )}
          </div>
        </div>
        {/* 历史记录复用组件 */}
        <HistorySection
          history={history}
          onClear={handleClearHistory}
          onItemClick={handleHistoryClick}
        />

        {/* 热门城市 */}
        <div id="hot-cities" className="px-4 py-4 bg-white hot-cities-section">
          <h3 className="text-[#11111E] mb-3" style={{ fontSize: '14px' }}>
            热门目的地
          </h3>
          <div className="grid grid-cols-4 gap-1">
            {cityData.hotCities.map(city => (
              <button
                key={city.cityId}
                onClick={() => handleCitySelect(city)}
                className="bg-gray-50 text-[#11111E] text-center hover:bg-gray-100 transition-colors"
                style={{
                  fontSize: '14px',
                  height: '36px',
                  borderRadius: '12px',
                }}
              >
                {city.cityName}
              </button>
            ))}
          </div>
        </div>

        {/* 城市列表 */}
        <div className="bg-white pr-10">
          {cityData.cities.map(group => (
            <div key={group.letter} id={`letter-${group.letter}`}>
              {/* 字母标题 */}
              <div className="sticky top-0 bg-white px-4 py-2 text-sm font-medium text-gray-700 z-10">
                {group.letter}
              </div>

              {/* 城市列表 */}
              <div className="px-4">
                {group.cityInfos.map(city => (
                  <button
                    key={city.cityId}
                    onClick={() => handleCitySelect(city)}
                    className="w-full text-left border-b border-gray-100 last:border-b-0 hover:bg-gray-50 transition-colors flex items-center"
                    style={{ height: '56px' }}
                  >
                    <div
                      className="text-[#11111E]"
                      style={{ fontSize: '14px' }}
                    >
                      {city.cityName}
                      {city.countryName && ` (${city.countryName})`}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧字母索引 - 固定定位 */}
      <div className="absolute right-1 top-32 bottom-4 w-8 flex flex-col items-end justify-start bg-white z-20 py-2 overflow-y-auto">
        {letterIndexes.map((letter, index) => (
          <button
            key={letter}
            data-letter={letter}
            onClick={() => handleLetterClick(letter, index)}
            className={`index-button flex items-center justify-center transition-all duration-200 text-primary hover:bg-primary-light rounded-full ${
              letter === '热门' || letter === '历史' ? 'w-6 flex-col' : 'w-6'
            } ${letter === selectedLetter ? 'bg-[var(--primary-light)]' : ''}`}
            style={{
              fontSize: '10px',
              marginBottom: '4px',
            }}
          >
            {letter === '热门' ? (
              <>
                <span>热</span>
                <span>门</span>
              </>
            ) : letter === '历史' ? (
              <>
                <span>历</span>
                <span>史</span>
              </>
            ) : (
              letter
            )}
          </button>
        ))}
      </div>

      {/* 滚动高亮逻辑 - 客户端组件 */}
      <ScrollHighlight letterIndexes={letterIndexes} />
    </div>
  );
};
