import { useCallback, useMemo } from 'react';

import dayjs from 'dayjs';
import { Calendar, type CalendarProps } from 'react-vant';

const DatePicker = ({
  value,
  onSelect,
  ...props
}: Omit<CalendarProps, 'value' | 'onSelect'> & {
  value: string | string[] | null;
  onSelect?: (_value: string[] | string) => void;
}) => {
  const CalendarValue = useMemo(() => {
    if (!value) return null;
    return Array.isArray(value)
      ? value.map(item => dayjs(item).toDate())
      : dayjs(value).toDate();
  }, [value]);

  const handleDateSelect = useCallback(
    (data: Date | Date[]) => {
      if (!onSelect) return;

      const formattedDates = Array.isArray(data)
        ? data.map(item => dayjs(item).format('YYYY-MM-DD'))
        : dayjs(data).format('YYYY-MM-DD');

      onSelect(formattedDates);
    },
    [onSelect]
  );

  return (
    <Calendar value={CalendarValue} {...props} onSelect={handleDateSelect} />
  );
};

export default DatePicker;
