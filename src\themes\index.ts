// 主题类型定义

// 导入主题配置
import defaultTheme from './default';
import dreameTheme from './dreame';
import hongtuTheme from './hongtu';

export interface ThemeConfig {
  // 主色系
  primary: string;
  primaryLight: string;
  primaryBg: string;

  // 按钮配置
  button: {
    primary: {
      background: string;
      text: string;
      hover?: string;
      active?: string;
    };
    secondary: {
      background: string;
      text: string;
      hover?: string;
      active?: string;
    };
  };

  // 文字色系
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    muted: string;
  };

  // 背景色系
  background: {
    page: string;
    card: string;
    gray: string;
    light: string;
  };

  // 状态色系
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };

  // 支付色系
  payment: {
    alipay: string;
    wechat: string;
    unionpay: string;
  };
}

// 主题UA类型
export type ThemeUA = 'dreame' | 'default' | 'hongtu';

// 导出所有主题配置
export { defaultTheme, dreameTheme, hongtuTheme };

// 主题映射
export const THEME_MAP: Record<ThemeUA, ThemeConfig> = {
  dreame: dreameTheme,
  default: defaultTheme,
  hongtu: hongtuTheme,
};

// 根据UA获取主题配置
export const getThemeByUA = (ua: ThemeUA): ThemeConfig =>
  THEME_MAP[ua] || THEME_MAP.default;
