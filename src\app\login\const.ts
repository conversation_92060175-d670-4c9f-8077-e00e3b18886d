/** 登录相关常量 */

/** 验证码长度 */
export const SMS_CODE_LENGTH = 6;

/** 倒计时初始秒数 */
export const COUNTDOWN_INITIAL_SECONDS = 59;

/** 设备ID（临时） */
export const DEFAULT_DEVICE_ID = '';

/** 登录类型 */
export const LOGIN_TYPE = 'sms_code' as const;

/** 短信类型 */
export const SMS_TYPE = 'LOGIN' as const;

/** 表单字段名称 */
export const FORM_FIELDS = {
  PHONE: 'phone',
  SMS_CODE: 'smsCode',
  AGREED: 'isAgreed',
} as const;

/** 协议类型 */
export const AGREEMENT_TYPES = {
  USER: 'user',
  PROTECT: 'protect',
} as const;

/** 错误消息 */
export const ERROR_MESSAGES = {
  PHONE_REQUIRED: '请输入手机号',
  PHONE_INVALID: '请填写正确的手机号',
  SMS_CODE_REQUIRED: '请输入验证码',
  SMS_CODE_INVALID: '验证码为6位数字',
  AGREEMENT_REQUIRED: '请先同意用户协议',
  SEND_CODE_FAILED: '验证码发送失败，请重试',
  LOGIN_FAILED: '登录失败，请重试',
} as const;

/** 成功消息 */
export const SUCCESS_MESSAGES = {
  SEND_CODE_SUCCESS: '验证码发送成功',
  LOGIN_SUCCESS: '登录成功',
} as const;

/** 按钮文本 */
export const BUTTON_TEXTS = {
  SEND_CODE: '发送验证码',
  SEND_CODE_COUNTDOWN: 's后重新获取',
  LOGIN: '立即登录',
} as const;

/** 占位符文本 */
export const PLACEHOLDER_TEXTS = {
  PHONE: '请输入手机号码',
  SMS_CODE: '请输入验证码',
} as const;

/** 协议文本 */
export const AGREEMENT_TEXTS = {
  USER_SERVICE: '《用户服务协议》',
  PRIVACY: '《隐私协议》',
  CONTENT_PLATFORM: '《内容平台协议》',
  AGREED_TEXT: '已阅读并同意',
} as const;
