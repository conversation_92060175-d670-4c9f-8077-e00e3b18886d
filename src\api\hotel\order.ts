import { request } from '@/utils/request';

export const getSaleRoomInfo = (params: API.HotelSaleRoomInfo.RoomInfoParams) =>
  request<API.HotelSaleRoomInfo.HotelData>(`/hotel/detail/getSaleRoomInfo`, {
    params,
  });

export const getRoomPriceInfo = (
  params: API.HotelPriceInfo.HotelBookingRequest
) =>
  request<API.HotelPriceInfo.HotelAvailabilityResponse>(
    `/hotel/order/booking`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: params,
    }
  );

export const createOrder = (
  params: API.HotelCreateOrder.HotelCreateOrderRequest
) =>
  request<API.HotelCreateOrder.OrderBack>(`/hotel/order/createOrder`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
  });
