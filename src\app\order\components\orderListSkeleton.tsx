'use client';

import { OrderItemSkeleton } from './orderItemSkeleton';

interface OrderListSkeletonProps {
  /** 显示的骨架屏订单项数量 */
  count?: number;
}

/** 订单列表骨架屏组件 包装多个OrderItemSkeleton，处理列表的间距和布局 */
export const OrderListSkeleton: React.FC<OrderListSkeletonProps> = ({
  count = 5,
}) => (
  <div className="bg-gray-50">
    {Array.from({ length: count }).map((_, index) => (
      <OrderItemSkeleton key={index} />
    ))}
  </div>
);

export default OrderListSkeleton;
