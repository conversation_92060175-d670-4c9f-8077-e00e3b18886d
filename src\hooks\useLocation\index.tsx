import { useCallback, useState } from 'react';

import { getGeoInfoByServer } from '@/api/common';
import { Toast } from '@/components';
import { useApp, useHotel } from '@/context';
import { useAMap } from '@/hooks/useAMap';
import {
  getLocationByAmapSdk,
  getLocationByBrowser,
  getLocationByDreame,
} from '@/utils/location/index';
import Native from '@/utils/native';
import { tracker } from '@/utils/track';

export const useLocation = ({
  ignoreMessage = false,
}: {
  /** 忽略定位错误信息 */
  ignoreMessage?: boolean;
}) => {
  const { geoInfo, updateGeoInfo } = useApp();
  const { updateHotelCityInfo } = useHotel();
  const [loading, setLoading] = useState(false);
  const { AMapRef, loadAMap } = useAMap();

  /** 根据平台-获取定位数据 */
  const getLocationByPlatform = async ({
    ignoreMessage = false,
  }: {
    ignoreMessage?: boolean;
  }): Promise<App.Service.IResponse<IGeoInfo>> => {
    let res: Nullable<
      App.Service.IResponse<ILocation & Pick<IGeoInfo, 'coordinateType'>>
    > = null;
    const isInDreameApp = Native.isDreameApp();
    if (isInDreameApp) {
      // dreame app内使用native定位
      res = await getLocationByDreame();
      tracker({
        eventType: 'info',
        extra: {
          action: 'location_log',
          scene: 'Dreame App原生定位结果',
          ...res,
        },
      });
    } else {
      // h5定位
      res = await getLocationByBrowser();
      if (!res.isSuccess) {
        // h5定位失败，走高德sdk兜底
        tracker({
          eventType: 'info',
          extra: {
            action: 'location_log',
            scene: 'Browser定位失败走高德sdk兜底',
            ...res,
          },
        });
        await loadAMap({ plugins: ['AMap.Geolocation'] });
        res = await getLocationByAmapSdk(AMapRef.current);

        tracker({
          eventType: 'info',
          extra: {
            action: 'location_log',
            scene: '高德sdk定位结果',
            ...res,
          },
        });
      }
    }
    if (!res.isSuccess || !res.data.latitude || !res.data.longitude) {
      if (!ignoreMessage) {
        Toast({
          type: 'fail',
          message: res.message,
          duration: 3000,
          customStyle: {
            width: '50vw',
          },
        });
      }
      return {
        ...res,
        data: {
          countryName: '',
          coordinateType: 'GCJ-02',
          cityId: '',
          cityName: '',
          latitude: '',
          longitude: '',
          provinceName: '',
          districtName: '',
          township: '',
          address: '',
        },
      };
    }

    // LBS-根据定位数据换取位置信息
    const { latitude, longitude, coordinateType } = res.data;
    const result = await getGeoInfoByServer({
      coordinateType,
      latitude,
      longitude,
    });
    return result;
  };

  const getLocation = async () => {
    if (loading) return;

    setLoading(true);
    const res = await getLocationByPlatform({ ignoreMessage });
    setLoading(false);

    if (res.isSuccess) {
      const {
        cityId,
        cityName,
        longitude,
        latitude,
        township,
        countryName,
        provinceName,
        address,
        coordinateType,
        districtName,
      } = res.data;
      updateHotelCityInfo({
        cityId,
        cityName,
      });
      updateGeoInfo({
        lat: latitude,
        lng: longitude,
        countryName,
        provinceName,
        address,
        township,
        coordinateType,
        districtName,
        cityId,
        cityName,
      });

      tracker({
        eventType: 'info',
        extra: {
          action: 'location_log',
          scene: 'LBS换取定位结果',
          ...res,
        },
      });
      return res.data;
    }
  };

  /** 定位组件 */
  const LocationComponent = () => {
    const handleGetLocation = useCallback(() => {
      void getLocation();

      tracker({
        eventType: 'click',
        extra: {
          action: 'getLocation_click',
          scene: '用户定位点击',
        },
      });
    }, []);
    return (
      <button
        onClick={handleGetLocation}
        className="bg-[#F7F7F7] flex items-center h-[52px] py-[17px] px-[16px] text-[14px] font-[500] rounded-[8px]"
      >
        <i className="iconfont icon-a-Property1chengshidingweididian ml-[4px]">
          {loading ? '正在定位' : '点击获取当前位置信息'}
        </i>
      </button>
    );
  };

  return {
    getLocation,
    geoInfo,
    loading,
    LocationComponent,
  };
};
