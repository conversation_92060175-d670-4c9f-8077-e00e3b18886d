'use client';
import React, { useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';
import './businessGrid.css';

import { staticBaseUrl } from '@/config';

import { tracker, tryCatchTracker } from '../../../utils/track';

// 第三方页面访问提示弹框组件
const FlightDialog: React.FC<{
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}> = ({ visible, onConfirm, onCancel }) => {
  const [Dialog, setDialog] = React.useState<any>(null);

  React.useEffect(() => {
    if (visible && !Dialog) {
      void import('@/components/dialog').then(
        ({ default: DialogComponent }) => {
          setDialog(() => DialogComponent);
        }
      );
    }
  }, [visible, Dialog]);

  if (!Dialog) return null;

  return (
    <Dialog
      visible={visible}
      title="第三方页面访问提示"
      onConfirm={onConfirm}
      onCancel={onCancel}
      maskClosable={false}
      footer={
        <div className="w-full flex items-center justify-between gap-3">
          {/* 返回原页面按钮 */}
          <button
            className="flex-1 h-[50px] bg-btn-secondary-bg var(--text-secondary) text-[16px] rounded-[60px] transition-all duration-200 hover:bg-btn-secondary-hover active:bg-btn-secondary-active"
            onClick={onCancel}
          >
            返回原页面
          </button>

          {/* 继续访问按钮 - 使用主题色 */}
          <button
            className="flex-1 h-[50px] bg-btn-primary-bg text-btn-primary-text text-[16px] rounded-[60px] transition-all duration-200 hover:bg-btn-primary-hover active:bg-btn-primary-active font-medium"
            onClick={onConfirm}
          >
            继续访问
          </button>
        </div>
      }
    >
      <div className="text-left space-y-3">
        <p>
          为向您提供【机票预订】服务，当前页面将跳转至第三方【苏州思客科技（集团）有限公司】运营的H5页面。
        </p>

        <div>
          <p className="font-medium mb-2">重要提示：</p>
          <div className="space-y-2 text-sm">
            <p>
              1、第三方H5页面的内容、服务及隐私政策由第三方独立负责，其隐私政策、用户协议与翎游可能存在差异，请您仔细阅读并注意保护个人信息及财产安全；
            </p>
            <p>
              2、如您在第三方页面发生交易、信息提交等行为时，请您仔细确认对方资质的及条款；
            </p>
            <p>
              3、第三方用户协议链接：
              <a
                href="https://m.ceekee.com/pages/index/iframeWeb?url=https://agreement.ceekee.com/%23/pages/ceekee/serviceAgreement"
                rel="noopener noreferrer"
                className="text-primary underline ml-1"
              >
                查看用户协议
              </a>
            </p>
            <p>
              4、第三方隐私政策链接：
              <a
                href="https://dingding.ceekee.com/pages/index/iframeWeb?url=https://agreement.ceekee.com/%23/pages/ceekee/privacyPolicy"
                rel="noopener noreferrer"
                className="text-primary underline ml-1"
              >
                查看隐私政策
              </a>
            </p>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

interface BusinessItemType {
  inactiveIcon: string;
  activeIcon: string;
  name: string;
  type: string;
}

interface BusinessGridClientProps {
  businessList: BusinessItemType[];
  HotelQuerier: React.FC;
  RecommendHotel: React.FC;
}

const EmptyComponent = () => (
  <div className="flex flex-col items-center justify-center h-full">
    <img
      src={`${staticBaseUrl}/home/<USER>
      className="w-[156px] h-[120px]"
      alt="敬请期待"
    />
    <span className="mt-[12px] text-center leading-[25px] text-[18px] color-[#99999E]">
      敬请期待
    </span>
  </div>
);

const BusinessItem: React.FC<{
  item: BusinessItemType;
  isActive: boolean;
  onClick: () => void;
}> = React.memo(({ item, isActive, onClick }) => (
  <div
    className={`w-[78px] h-[78px] bg-[#fff] rounded-[20px] text-[#66666E] text-[12px] leading-[17px] flex flex-col items-center justify-center ${isActive ? 'active' : ''} active:bg-[linear-gradient(149.98deg,#F3EACB_6.05%,#B59A6E_76.89%)] active:text-white`}
    onClick={onClick}
  >
    <img
      className="w-[32px] h-[32px] mb-[4px]"
      src={isActive ? item.activeIcon : item.inactiveIcon}
      alt={item.name}
    />
    <span>{item.name}</span>
  </div>
));

export const BusinessGridClient: React.FC<BusinessGridClientProps> = ({
  businessList,
  HotelQuerier,
  RecommendHotel,
}) => {
  const [businessType, setBusinessType] = useState('hotel');
  const [showFlightDialog, setShowFlightDialog] = useState(false);
  const router = useRouter();

  // 检查用户登录态
  const checkUserLogin = async () => {
    try {
      const Cookies = await import('js-cookie');
      const { CookiesKeyEnum } = await import('@/enum');

      // 兼容处理：优先读取HT-Token，如果没有值再读取token
      const htToken = Cookies.default.get(CookiesKeyEnum.AUTH_TOKEN);
      const fallbackToken = Cookies.default.get('token');
      return htToken || fallbackToken || '';
    } catch {
      return '';
    }
  };

  // 处理第三方页面访问确认
  const handleFlightDialogConfirm = async () => {
    // 确认访问埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'flight_dialog_confirm',
      },
    });

    setShowFlightDialog(false);

    // 检查登录态并调用API
    await handleFlightAuth();
  };

  // 处理第三方页面访问取消
  const handleFlightDialogCancel = () => {
    // 取消访问埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'flight_dialog_cancel',
      },
    });

    setShowFlightDialog(false);
  };

  // 处理机票授权逻辑
  const handleFlightAuth = async () => {
    // 检查登录态
    const token = await checkUserLogin();

    if (!token) {
      // 未登录，跳转到登录页
      tracker({
        eventType: 'click',
        extra: {
          action: 'flight_no_login_redirect',
        },
      });
      router.push('/login');
      return;
    }

    // 显示loading
    const { Toast } = await import('@/components');
    const { getImageUrl } = await import('@/utils');

    const loadingToast = Toast.loading({
      icon: (
        <img
          src={getImageUrl('loading.gif')}
          alt="loading"
          className="w-[100px] h-[100px] bg-[#fff]  rounded-[20px]"
        />
      ),
      duration: 0, // 不自动关闭
      overlay: true, // 显示遮罩层
      forbidClick: true, // 禁止背景点击
      iconOnly: true, // 只显示图标，不显示外框
    });

    // 调用API获取授权码
    try {
      const { getFlightAuthCode } = await import('@/api/home');

      // API调用开始埋点
      tracker({
        eventType: 'info',
        extra: {
          action: 'flight_auth_api_start',
          api: '/hotel/user/auth/pushAndGetH5Login',
        },
      });

      const { isSuccess, message, data } = await getFlightAuthCode();
      const { flightUrl } = data || {};

      if (isSuccess && flightUrl) {
        // API调用成功埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'flight_auth_api_success',
            api: '/hotel/user/auth/pushAndGetH5Login',
            hasFlightUrl: !!flightUrl,
          },
        });

        // 关闭loading
        loadingToast.close();

        // 直接跳转
        window.location.href = flightUrl;
      } else {
        // API调用失败埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'flight_auth_api_fail',
            api: '/hotel/user/auth/pushAndGetH5Login',
            message: message || '未知错误',
            hasData: !!data,
            hasFlightUrl: !!flightUrl,
          },
        });

        // 关闭loading
        loadingToast.close();
      }
    } catch (error) {
      // API调用异常埋点
      tryCatchTracker(error, {
        scene: 'flight_auth_api_call',
        action: 'flight_button_click',
        api: '/hotel/user/auth/pushAndGetH5Login',
        source: 'home_business_grid',
      });

      // 关闭loading
      loadingToast.close();
    }
  };

  const handleBusinessSwitch = (type: string): void => {
    if (type === 'travel') {
      router.push('/customTour');
    } else if (type === 'flight') {
      // 机票按钮点击埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'flight_button_click',
          source: 'home_business_grid',
        },
      });

      // 直接显示第三方页面访问提示弹框
      setShowFlightDialog(true);
    } else {
      setBusinessType(type);
    }

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_tab_click',
        tab: type,
      },
    });
  };

  const activeIndex = useMemo(
    () => businessList.findIndex(item => item.type === businessType),
    [businessType, businessList]
  );

  return (
    <>
      <div className="flex justify-between">
        {businessList.map(item => (
          <BusinessItem
            key={item.type}
            item={item}
            isActive={businessType === item.type}
            onClick={() => handleBusinessSwitch(item.type)}
          />
        ))}
      </div>
      <div className={`home-quick-search-container index-${activeIndex}`}>
        {businessType === 'hotel' ? <HotelQuerier /> : <EmptyComponent />}
      </div>
      {businessType === 'hotel' && <RecommendHotel />}

      {/* 第三方页面访问提示弹框 */}
      <FlightDialog
        visible={showFlightDialog}
        onConfirm={handleFlightDialogConfirm}
        onCancel={handleFlightDialogCancel}
      />
    </>
  );
};
