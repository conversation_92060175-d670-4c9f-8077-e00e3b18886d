import React from 'react';

import { createPortal } from 'react-dom';

import type { ToastInstance, ToastOptions } from './types';

export interface ToastItemProps extends ToastOptions {
  id: string;
  visible: boolean;
  onDestroy: (_id: string) => void;
}

export class ToastManager {
  private toasts: Map<string, ToastItemProps> = new Map();
  private container: HTMLElement | null = null;
  private defaultOptions: Partial<ToastOptions> = {};
  private allowMultipleToasts = false;
  private updateCallback: (() => void) | null = null;

  private getContainer(teleport?: HTMLElement | (() => HTMLElement)) {
    if (teleport) {
      return typeof teleport === 'function' ? teleport() : teleport;
    }

    // 确保只在客户端环境中创建容器
    if (typeof window === 'undefined') {
      return null;
    }

    if (!this.container) {
      this.container = document.createElement('div');
      this.container.className = 'toast-container';
      document.body.appendChild(this.container);
    }
    return this.container;
  }

  private generateId() {
    return `toast_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  show(options: ToastOptions | string): ToastInstance {
    const config = typeof options === 'string' ? { message: options } : options;
    const finalOptions = { ...this.defaultOptions, ...config };

    if (!this.allowMultipleToasts) {
      this.clear();
    }

    const id = this.generateId();
    const toastProps: ToastItemProps = {
      ...finalOptions,
      id,
      visible: true,
      onDestroy: toastId => {
        this.toasts.delete(toastId);
        this.updateCallback?.();
      },
    };

    this.toasts.set(id, toastProps);
    this.updateCallback?.();

    return {
      config: (newOptions: Partial<ToastOptions>) => {
        const existingToast = this.toasts.get(id);
        if (existingToast) {
          this.toasts.set(id, { ...existingToast, ...newOptions });
          this.updateCallback?.();
        }
      },
      close: () => {
        const existingToast = this.toasts.get(id);
        if (existingToast) {
          this.toasts.set(id, { ...existingToast, visible: false });
          this.updateCallback?.();
        }
      },
    };
  }

  info(options: ToastOptions | string) {
    const config = typeof options === 'string' ? { message: options } : options;
    return this.show({ ...config, type: 'info' });
  }

  loading(options: ToastOptions | string) {
    const config = typeof options === 'string' ? { message: options } : options;
    return this.show({ ...config, type: 'loading' });
  }

  success(options: ToastOptions | string) {
    const config = typeof options === 'string' ? { message: options } : options;
    return this.show({ ...config, type: 'success' });
  }

  fail(options: ToastOptions | string) {
    const config = typeof options === 'string' ? { message: options } : options;
    return this.show({ ...config, type: 'fail' });
  }

  clear(clearAll = true) {
    if (clearAll) {
      // 立即删除所有 toast，而不是等待动画
      this.toasts.clear();
    } else {
      const toastArray = Array.from(this.toasts.values());
      if (toastArray.length > 0) {
        const lastToast = toastArray[toastArray.length - 1];
        this.toasts.delete(lastToast.id);
      }
    }
    this.updateCallback?.();
  }

  allowMultiple() {
    this.allowMultipleToasts = true;
  }

  setDefaultOptions(
    typeOrOptions: string | Partial<ToastOptions>,
    options?: Partial<ToastOptions>
  ) {
    if (typeof typeOrOptions === 'string' && options) {
      // 为特定类型设置默认选项
      this.defaultOptions = {
        ...this.defaultOptions,
        ...(typeOrOptions === 'loading' ? options : {}),
      };
    } else if (typeof typeOrOptions === 'object') {
      this.defaultOptions = { ...this.defaultOptions, ...typeOrOptions };
    }
  }

  resetDefaultOptions(type?: string) {
    if (type) {
      // 重置特定类型的默认选项
      this.defaultOptions = {};
    } else {
      this.defaultOptions = {};
    }
  }

  setUpdateCallback(callback: () => void) {
    this.updateCallback = callback;
  }

  getToasts() {
    return Array.from(this.toasts.values());
  }

  render(
    ToastItemComponent: React.ComponentType<ToastItemProps>,
    teleport?: HTMLElement | (() => HTMLElement)
  ) {
    const container = this.getContainer(teleport);
    const toasts = this.getToasts();

    // 如果在服务端或容器不存在，返回null
    if (!container) {
      return null;
    }

    return createPortal(
      <>
        {toasts.map(toast => (
          <ToastItemComponent key={toast.id} {...toast} />
        ))}
      </>,
      container
    );
  }
}

export const toastManager = new ToastManager();
