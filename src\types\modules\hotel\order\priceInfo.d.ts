declare namespace API.HotelPriceInfo {
  interface HotelBookingRequest {
    arrivalDate: string; // Format: "YYYY-MM-DD"
    departureDate: string; // Format: "YYYY-MM-DD"
    hotelId: string; // Example: "69497353"
    hotelCode: string; // Example: "69569538"
    roomTypeId: string; // Example: "0079"
    ratePlanId: number; // Example: 357677997
    paymentType: 'Prepay' | string; // Assuming other payment types might exist
    numberOfRooms: number; // Example: 1
    priceLimitedType: number; // Example: 7
    isPriceLimittedProduct: boolean;
    littleMajiaId: string; // Complex ID format example
    goodsUniqId: string; // Complex unique ID format
  }

  interface HotelAvailabilityResponse {
    /** Total price for all rooms as a string */
    totalPrice: string;

    /** Total number of rooms */
    totalRoomNum: number;

    /** Inventory information */
    inventories: Inventory[];

    /** Rate information */
    rates: Rate[];

    /** Prepayment and cancellation policy */
    prepayResult: PrepayResult;

    bookingDetailInfos: bookingDetailInfos[];
  }

  interface Inventory {
    /** Hotel ID */
    hotelId: string;

    /** Room type ID */
    roomTypeId: string;

    /** Hotel code */
    hotelCode: string;

    /** Date of inventory in YYYY-MM-DD format */
    date: string;

    /** Availability status */
    status: boolean;

    /** Available room count */
    amount: number;

    /** Overbooking count */
    overBooking: number;

    /** Inventory start date in ISO format with timezone */
    startDate: string;

    /** Inventory end date in ISO format with timezone */
    endDate: string;
  }

  interface Rate {
    /** Hotel ID */
    hotelId: string;

    /** Room type ID */
    roomTypeId: string;

    /** Rate plan ID */
    ratePlanId: number;

    /** Rate start date in ISO format with timezone */
    startDate: string;

    /** Rate end date in ISO format with timezone */
    endDate: string;

    /** Hotel code */
    hotelCode: string;

    /** Rate status */
    status: boolean;

    /** Price as string */
    price: string;

    /** Additional bed price (-1 indicates not available) */
    addBed: number;

    /** Price ID */
    priceId: number;

    /** Currency code */
    currencyCode: 'RMB' | string;
  }

  interface PrepayResult {
    /** Cancellation policy description */
    cancelDescription: string;

    /** Cancellation type (numeric code) */
    cancelType: number;

    /** Cancellation policy ladder */
    ladderParseList: CancellationLadder[];

    /** Cancellation tag (null in example) */
    cancelTag: null | string;
  }

  interface CancellationLadder {
    /** Policy start date */
    beginTime: string;

    /** Policy end date */
    endTime: string;

    /** Cut type (0=free, 1=non-refundable) */
    cutType: 0 | 1 | number;

    /** Cut value */
    cutValue: number;

    /** Amount */
    amount: number;

    /** Short description */
    shortDesc: string;

    /** Amount in RMB */
    amountRmb: string;

    /** Exchange rate */
    exchangeRate: string;
  }

  interface bookingDetailInfos {
    checkInDate: string;
    mealDesc: string;
    numberOfRooms: Number;
    price: string;
  }
}
