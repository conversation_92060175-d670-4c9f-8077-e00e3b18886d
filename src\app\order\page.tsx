import { cookies } from 'next/headers';

import { getOrderList } from '@/api/order';
import { type TabItem } from '@/components';
import { getServerSafeAreaHeight } from '@/server/utils';
import { tracker } from '@/utils/track';

import ServerNavbar from '../../components/navbar/ServerNavbar';
import { CookiesKeyEnum } from '../../enum';

import { OrderTabsClient } from './orderTabsClient';
import { RefreshableOrderList } from './refreshableOrderList';

interface OrderListPageProps {
  searchParams: Promise<{ tab?: string; page?: string }>;
}

/** 订单列表页面 - 纯服务端渲染 */
const OrderListPage = async ({ searchParams }: OrderListPageProps) => {
  const { tab = 'all' } = await searchParams;

  // 获取服务端 cookies
  const cookieStore = await cookies();
  const token = cookieStore.get(CookiesKeyEnum.AUTH_TOKEN)?.value || '';

  // 获取服务端安全区域高度
  const safeAreaHeightPx = await getServerSafeAreaHeight();

  // 计算navbar总高度（基础高度 + 安全区域高度）
  const navbarTotalHeight = 56 + safeAreaHeightPx;

  // 将tab字符串转换为订单状态
  const getOrderStatus = (tabKey: string): string => {
    switch (tabKey) {
      case 'pending_payment':
        return '0'; // 待付款
      case 'not_traveled':
        return '2'; // 未出行
      default:
        return ''; // 全部状态传空字符串
    }
  };

  // Tab 配置
  const tabs: TabItem[] = [
    { key: 'all', label: '全部' },
    { key: 'pending_payment', label: '待付款' },
    { key: 'not_traveled', label: '未出行' },
  ];

  // 服务端获取初始订单数据
  let initialOrders: Order.IOrderItem[] = [];
  try {
    const params: Order.IOrderListReq = {
      pageIndex: 1, // 始终获取第一页作为初始数据
      pageSize: 10,
      status: getOrderStatus(tab), // 根据当前tab设置正确的状态
      keyword: '',
    };

    const response = await getOrderList(params, token);
    if (response.isSuccess && response.data) {
      initialOrders = response.data;
    }
  } catch (err) {
    console.error('服务端获取订单列表失败:', err);
    // 错误处理交给客户端组件
  }

  // 页面曝光埋点
  tracker({
    eventType: 'pv',
    extra: {
      action: 'order_list_page_show',
      tab,
      orderStatus: getOrderStatus(tab),
      initialOrdersCount: initialOrders.length,
    },
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <ServerNavbar title="我的订单" fixed />

      {/* Tab 导航 - 固定定位 */}
      <div
        className="fixed left-0 right-0 z-40 bg-white border-gray-100"
        style={{ top: `${navbarTotalHeight}px` }}
      >
        <OrderTabsClient items={tabs} activeKey={tab} />
      </div>

      {/* 可刷新订单列表 - 添加顶部间距避免被固定tabs遮挡 */}
      <div>
        <RefreshableOrderList
          initialOrders={initialOrders}
          activeTab={tab}
          token={token}
          safeAreaHeightPx={safeAreaHeightPx}
        />
      </div>
    </div>
  );
};

export default OrderListPage;
