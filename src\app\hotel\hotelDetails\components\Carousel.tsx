'use client';
import { useMemo, useRef, useState } from 'react';

import { Swiper, SwiperSlide } from 'swiper/react';

import type { Swiper as SwiperCore } from 'swiper';

import 'swiper/css';

interface CarouselProps {
  hotelImages?: API.HotelDetails.IHotelImageCategory[];
}

interface ImageWithCategory {
  url: string;
  category: string;
  categoryIndex: number;
}

const Carousel = ({ hotelImages = [] }: CarouselProps) => {
  const swiperRef = useRef<SwiperCore | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // 过滤掉空的图片类别并处理图片数据
  const { validCategories, originalImages } = useMemo(() => {
    const categories = hotelImages.filter(
      category => category.imageList.length > 0
    );
    const images: ImageWithCategory[] = categories.flatMap(
      (category, categoryIndex) =>
        category.imageList.map(url => ({
          url,
          category: category.category,
          categoryIndex,
        }))
    );
    return { validCategories: categories, originalImages: images };
  }, [hotelImages]);

  const currentCategoryIndex =
    originalImages[currentImageIndex]?.categoryIndex ?? 0;

  // 点击类型切换到该类型的第一张图片
  const handleCategoryClick = (categoryIndex: number) => {
    const firstImageIndex = originalImages.findIndex(
      img => img.categoryIndex === categoryIndex
    );
    if (firstImageIndex !== -1) {
      // 使用 slideToLoop 以确保在循环模式下正确跳转
      swiperRef.current?.slideToLoop(firstImageIndex);
    }
  };

  // 如果没有图片，显示占位符
  if (originalImages.length === 0) {
    return (
      <div className="relative w-full h-80 bg-gray-200 flex items-center justify-center">
        <span className="text-gray-500">暂无图片</span>
      </div>
    );
  }

  return (
    <div className="relative w-full h-80 bg-black rounded-b-[40px] overflow-hidden">
      <Swiper
        className="w-full h-full"
        loop
        onSwiper={swiper => {
          swiperRef.current = swiper;
        }}
        onSlideChange={swiper => {
          // realIndex 在 loop 模式下会自动计算正确的索引
          setCurrentImageIndex(swiper.realIndex);
        }}
      >
        {originalImages.map((image, index) => (
          <SwiperSlide key={`${image.url}-${index}`}>
            <div className="w-full h-full relative">
              <img
                src={image.url}
                alt={`${image.category} ${index + 1}`}
                className="object-cover w-full h-full"
              />
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* 自定义控件 - 必须放在 Swiper 组件外部才能显示 */}
      <div className="absolute bottom-4 left-0 right-0 z-10 px-4 flex justify-between items-center pointer-events-none">
        {/* 左下角类型控制器 */}
        <div
          className="rounded-[14px] p-0.5 pointer-events-auto"
          style={{
            backdropFilter: 'blur(1px)',
            WebkitBackdropFilter: 'blur(1px)',
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
          }}
        >
          <div className="flex space-x-2">
            {validCategories.map((category, index) => (
              <button
                key={category.category}
                className={`px-2 py-0.5 rounded-[14px] text-sm font-medium transition-all ${
                  index === currentCategoryIndex
                    ? 'bg-white text-black'
                    : 'text-white hover:bg-white/10'
                }`}
                onClick={() => handleCategoryClick(index)}
              >
                {category.category}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Carousel;
