'use client';

import React, { useEffect, useRef, useState } from 'react';

import { toastManager, type ToastItemProps } from './manager';

import type { ToastOptions } from './types';

// 重新导出类型
export type { ToastInstance, ToastOptions } from './types';

const ToastItem: React.FC<ToastItemProps> = ({
  id,
  visible,
  type = 'info',
  position = 'middle',
  message = '',
  icon,
  iconSize = '36px',
  forbidClick = false,
  closeOnClick = false,
  closeOnClickOverlay = false,
  loadingType = 'circular',
  duration = 2000,
  className = '',
  customStyle = {},
  overlay = false,
  overlayClass = '',
  overlayStyle = {},
  iconOnly = false,
  onOpened,
  onClose,
  onDestroy,
  ..._rest
}) => {
  const [show, setShow] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const destroyTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleClose = React.useCallback(() => {
    setShow(false);
    onClose?.();

    // 清除之前的销毁定时器
    if (destroyTimerRef.current) {
      clearTimeout(destroyTimerRef.current);
    }

    destroyTimerRef.current = setTimeout(() => {
      onDestroy(id);
    }, 300); // 等待动画完成
  }, [id, onClose, onDestroy]);

  useEffect(() => {
    if (visible) {
      setShow(true);
      onOpened?.();

      if (duration > 0) {
        // 清除之前的定时器（如果存在）
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }

        timerRef.current = setTimeout(() => {
          handleClose();
        }, duration);
      }
    } else {
      // 当 visible 变为 false 时，立即清除定时器并关闭Toast
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      handleClose();
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (destroyTimerRef.current) {
        clearTimeout(destroyTimerRef.current);
        destroyTimerRef.current = null;
      }
    };
  }, [visible, duration, onOpened, handleClose]);

  const handleClick = () => {
    if (closeOnClick) {
      handleClose();
    }
  };

  const handleOverlayClick = () => {
    if (closeOnClickOverlay) {
      handleClose();
    }
  };

  const getIcon = () => {
    if (icon) return icon;

    switch (type) {
      case 'success':
        return (
          <svg
            width={iconSize}
            height={iconSize}
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M9 12l2 2 4-4"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
          </svg>
        );
      case 'fail':
        return (
          <svg
            width={iconSize}
            height={iconSize}
            viewBox="0 0 24 24"
            fill="none"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
            <path
              d="m15 9-6 6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="m9 9 6 6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case 'loading':
        return (
          <div
            className={`animate-spin ${loadingType === 'spinner' ? 'border-2 border-white border-t-transparent rounded-full' : ''}`}
            style={{ width: iconSize, height: iconSize }}
          >
            {loadingType === 'circular' ? (
              <svg
                width={iconSize}
                height={iconSize}
                viewBox="0 0 24 24"
                fill="none"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeDasharray="31.416"
                  strokeDashoffset="31.416"
                  className="animate-spin"
                />
              </svg>
            ) : null}
          </div>
        );
      default:
        return null;
    }
  };

  const getPositionClass = () => {
    switch (position) {
      case 'top':
        return 'top-[20%]';
      case 'bottom':
        return 'bottom-[20%]';
      default:
        return 'top-1/2 -translate-y-1/2';
    }
  };

  return (
    <>
      {overlay && (
        <div
          className={`fixed inset-0 z-[10000] ${overlayClass}`}
          style={{
            backgroundColor: iconOnly ? 'transparent' : 'rgba(0, 0, 0, 0.7)',
            pointerEvents: forbidClick ? 'auto' : 'none',
            ...overlayStyle,
          }}
          onClick={handleOverlayClick}
        />
      )}
      <div
        className={`fixed left-1/2 -translate-x-1/2 z-[10001] transition-all duration-300 ${getPositionClass()} ${
          show ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
        } ${className}`}
        onClick={handleClick}
        style={{
          pointerEvents: forbidClick ? 'none' : 'auto',
        }}
      >
        {iconOnly ? (
          // 只显示图标，不显示外框
          <div className="flex items-center justify-center">{getIcon()}</div>
        ) : (
          // 显示完整的Toast外框
          <div
            className={`
               min-w-[96px] px-4 py-3 rounded-lg text-white text-center
              ${type === 'info' || !getIcon() ? 'text-sm leading-5' : 'min-h-[88px] w-[88px] flex flex-col items-center justify-center'}
            `}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              fontSize: type === 'info' || !getIcon() ? '14px' : '14px',
              lineHeight: type === 'info' || !getIcon() ? '20px' : '20px',
              ...customStyle,
            }}
          >
            {getIcon() && <div className="mb-2 text-white">{getIcon()}</div>}
            {message && <div className="whitespace-pre-line">{message}</div>}
          </div>
        )}
      </div>
    </>
  );
};

// React Hook for managing toasts
export const useToast = () => {
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const updateCallback = () => forceUpdate({});
    toastManager.setUpdateCallback(updateCallback);

    return () => {
      toastManager.setUpdateCallback(() => {});
    };
  }, []);

  return toastManager;
};

// Toast component for rendering
export const ToastContainer: React.FC<{
  teleport?: HTMLElement | (() => HTMLElement);
}> = ({ teleport }) => {
  const toast = useToast();
  return toast.render(ToastItem, teleport);
};

// Main Toast API
export const Toast = Object.assign(
  (options: ToastOptions | string) => toastManager.show(options),
  {
    info: (options: ToastOptions | string) => toastManager.info(options),
    loading: (options: ToastOptions | string) => toastManager.loading(options),
    success: (options: ToastOptions | string) => toastManager.success(options),
    fail: (options: ToastOptions | string) => toastManager.fail(options),
    clear: (clearAll?: boolean) => toastManager.clear(clearAll),
    allowMultiple: () => toastManager.allowMultiple(),
    setDefaultOptions: (
      typeOrOptions: string | Partial<ToastOptions>,
      options?: Partial<ToastOptions>
    ) => toastManager.setDefaultOptions(typeOrOptions, options),
    resetDefaultOptions: (type?: string) =>
      toastManager.resetDefaultOptions(type),
  }
);

export default Toast;
