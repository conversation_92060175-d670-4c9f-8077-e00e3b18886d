'use client';

import dayjs from 'dayjs';

import { Navbar } from '@/components/navbar';

interface NavBarProps {
  /** 城市ID */
  cityId?: string;
  /** 城市名称 */
  cityName: string;
  /** 入住日期 */
  arrivalDate?: string;
  /** 离店日期 */
  departureDate?: string;
  /** 搜索关键词 */
  queryText?: string;
  /** 日期选择回调 */
  onDateSelect?: () => void;
  /** 搜索回调 */
  onSearch?: () => void;
  /** 清除搜索关键词回调 */
  onClearSearch?: () => void;
  /** 城市选择回调 */
  onCitySelect?: () => void;
}

const OwnNavBar = ({
  cityId: _cityId = '',
  cityName = '',
  arrivalDate = '',
  departureDate = '',
  queryText = '',
  onDateSelect,
  onSearch,
  onClearSearch,
  onCitySelect,
}: NavBarProps) => {
  // 格式化日期为 MM.DD 格式
  const formatDate = (dateString: string) => dayjs(dateString).format('MM.DD');

  // 城市选择
  const handleCitySelect = () => {
    onCitySelect?.();
  };

  // 日期选择
  const handleDateSelect = () => {
    onDateSelect?.();
  };

  // 搜索
  const handleSearch = () => {
    onSearch?.();
  };

  // 地图
  // const handleMapClick = () => {
  //   console.log('跳转到地图页面');
  //   // TODO: 跳转到地图页面
  // };

  // // 收藏
  // const handleFavoriteClick = () => {
  //   console.log('跳转到收藏页面');
  //   // TODO: 跳转到收藏页面
  // };

  return (
    <Navbar
      fixed
      center={{
        children: (
          <div className="flex-1 flex items-center bg-bg-light rounded-[27px] h-[54px] px-4 min-w-0 ml-3">
            {/* 城市选择器 */}
            <button
              onClick={handleCitySelect}
              className="flex items-center mr-2 flex-shrink-0"
            >
              <span className="text-[16px] font-medium text-text-primary">
                {cityName}
              </span>
            </button>
            <div className="w-[1px] h-4 bg-bg-gray mr-2 flex-shrink-0" />

            {/* 日期选择区 */}
            <button
              onClick={handleDateSelect}
              className="mr-3 text-xs flex-shrink-0"
            >
              <div className="flex items-center justify-center text-gray-900">
                <span className="mr-3">住</span>
                <span className=" text-left w-[38px]">
                  {formatDate(arrivalDate)}
                </span>
              </div>
              <div className="flex items-center justify-center text-gray-900 mt-0.5">
                <span className="mr-3">离</span>
                <span className=" text-left w-[38px]">
                  {formatDate(departureDate)}
                </span>
              </div>
            </button>

            {/* 分割线 */}
            <div className="w-[1px] h-4 bg-gray-200 mr-2 flex-shrink-0" />

            {/* 搜索输入区 */}
            <div className="flex-1 min-w-0 overflow-hidden">
              <div className="w-full flex items-center">
                {/* 搜索文本 */}
                {queryText ? (
                  <div className="flex items-center justify-between w-full min-w-0">
                    <span
                      onClick={handleSearch}
                      className="text-sm text-text-primary truncate block min-w-0 flex-1 cursor-pointer"
                    >
                      {queryText}
                    </span>
                    {/* 清除搜索按钮 */}
                    <button
                      onClick={e => {
                        e.stopPropagation(); // 阻止事件冒泡
                        onClearSearch?.();
                      }}
                      className="ml-2 w-4 h-4 flex items-center justify-center bg-text-muted rounded-full hover:bg-text-tertiary flex-shrink-0 transition-colors duration-200"
                    >
                      <svg
                        className="w-3 h-3 text-bg-card"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                ) : (
                  <span
                    onClick={handleSearch}
                    className="text-sm text-text-muted cursor-pointer"
                  >
                    位置/品牌/酒店
                  </span>
                )}
              </div>
            </div>
          </div>
        ),
      }}
    />
  );
};

export default OwnNavBar;
