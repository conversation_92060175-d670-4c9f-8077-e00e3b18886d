'use client';

import { useEffect } from 'react';

interface ScrollHighlightProps {
  /** 字母索引数组 */
  letterIndexes: string[];
}

export const ScrollHighlight: React.FC<ScrollHighlightProps> = ({
  letterIndexes,
}) => {
  useEffect(() => {
    const scrollContainer = document.querySelector('.city-scroll-container');
    if (!scrollContainer) return;

    const handleScroll = () => {
      const { scrollTop } = scrollContainer;

      // 移除所有高亮
      document.querySelectorAll('.index-button').forEach(btn => {
        btn.classList.remove('bg-primary-light', 'shadow-md');
      });

      // 检查热门城市区域
      const hotCitiesSection = document.getElementById('hot-cities');
      if (hotCitiesSection && scrollTop < hotCitiesSection.offsetHeight) {
        const hotButton = document.querySelector('[data-letter="热门"]');
        if (hotButton) {
          hotButton.classList.add('bg-primary-light', 'shadow-md');
        }
        return;
      }

      // 检查字母区域 - 找到当前最接近顶部的区域
      let activeElement = null;
      let minDistance = Infinity;

      letterIndexes.slice(1).forEach(letter => {
        const element = document.getElementById(`letter-${letter}`);
        if (element) {
          const elementTop = element.offsetTop;
          const distance = Math.abs(scrollTop - elementTop);

          if (scrollTop >= elementTop - 50 && distance < minDistance) {
            minDistance = distance;
            activeElement = letter;
          }
        }
      });

      if (activeElement) {
        const button = document.querySelector(
          `[data-letter="${activeElement}"]`
        );
        if (button) {
          button.classList.add('bg-primary-light', 'shadow-md');
        }
      }
    };

    // 初始检查
    handleScroll();

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => scrollContainer.removeEventListener('scroll', handleScroll);
  }, [letterIndexes]);

  return null; // 这个组件不渲染任何内容，只处理滚动逻辑
};
