'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'log' | 'warn' | 'error' | 'info';
  message: string;
  data?: any[];
}

interface LogPanelProps {
  /** 是否显示日志面板 */
  visible?: boolean;
  /** 最大日志条数 */
  maxLogs?: number;
  /** 面板位置 */
  position?: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left';
  /** 面板宽度 */
  width?: number;
  /** 面板高度 */
  height?: number;
}

/** 页面内日志显示组件 支持日志级别过滤、清空、复制、搜索等功能 */
export const LogPanel: React.FC<LogPanelProps> = ({
  visible = false,
  maxLogs = 100,
  position = 'bottom-right',
  width = 400,
  height = 300,
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filterLevel, setFilterLevel] = useState<
    'all' | 'log' | 'warn' | 'error' | 'info'
  >('all');
  const [searchText, setSearchText] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const logContainerRef = useRef<HTMLDivElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 初始化拖拽位置 - 直接固定在左上角
  useEffect(() => {
    setDragPosition({
      x: 10,
      y: 10,
    });
    console.log('LogPanel: 悬浮按钮初始化在左上角位置');
  }, []);

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (e: React.MouseEvent | React.TouchEvent) => {
      if (isMinimized) {
        setIsDragging(true);
        const rect = panelRef.current?.getBoundingClientRect();
        if (rect) {
          const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
          const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
          setDragOffset({
            x: clientX - rect.left,
            y: clientY - rect.top,
          });
        }
      }
    },
    [isMinimized]
  );

  // 处理悬浮按钮点击
  const handleFloatingButtonClick = useCallback(
    (_e: React.MouseEvent | React.TouchEvent) => {
      // 延迟执行，确保拖拽状态已经更新
      setTimeout(() => {
        if (!isDragging) {
          console.log('LogPanel: 悬浮按钮被点击，展开面板');
          setIsMinimized(false);
        }
      }, 10);
    },
    [isDragging]
  );

  // 处理拖拽移动
  const handleDragMove = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (isDragging && isMinimized) {
        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
        const newX = clientX - dragOffset.x;
        const newY = clientY - dragOffset.y;

        // 限制在屏幕范围内
        const maxX = window.innerWidth - 50;
        const maxY = window.innerHeight - 50;

        setDragPosition({
          x: Math.max(0, Math.min(newX, maxX)),
          y: Math.max(0, Math.min(newY, maxY)),
        });
      }
    },
    [isDragging, isMinimized, dragOffset]
  );

  // 处理拖拽结束
  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 添加全局鼠标和触摸事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleDragMove);
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('touchmove', handleDragMove, {
        passive: false,
      });
      document.addEventListener('touchend', handleDragEnd);
      return () => {
        document.removeEventListener('mousemove', handleDragMove);
        document.removeEventListener('mouseup', handleDragEnd);
        document.removeEventListener('touchmove', handleDragMove);
        document.removeEventListener('touchend', handleDragEnd);
      };
    }
  }, [isDragging, handleDragMove, handleDragEnd]);

  // 处理最小化状态变化
  const handleMinimizeToggle = useCallback(() => {
    const newMinimizedState = !isMinimized;
    setIsMinimized(newMinimizedState);
  }, [isMinimized]);

  // 处理展开状态变化
  const handleExpandToggle = useCallback(() => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
  }, [isExpanded]);

  // 处理放大状态变化
  const handleMaximizeToggle = useCallback(() => {
    const newMaximizedState = !isMaximized;
    setIsMaximized(newMaximizedState);
  }, [isMaximized]);

  // 添加日志条目
  const addLog = useCallback(
    (level: LogEntry['level'], message: string, data?: any[]) => {
      const newLog: LogEntry = {
        id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        timestamp: new Date(),
        level,
        message,
        data,
      };

      setLogs(prev => {
        const newLogs = [newLog, ...prev];
        return newLogs.slice(0, maxLogs);
      });
    },
    [maxLogs]
  );

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 复制日志
  const copyLogs = useCallback(async () => {
    const filteredLogs = logs.filter(log => {
      if (filterLevel !== 'all' && log.level !== filterLevel) return false;
      if (
        searchText &&
        !log.message.toLowerCase().includes(searchText.toLowerCase())
      )
        return false;
      return true;
    });

    const logText = filteredLogs
      .map(log => {
        const timestamp = log.timestamp.toLocaleTimeString();
        const dataStr = log.data?.length ? ` ${JSON.stringify(log.data)}` : '';
        return `[${timestamp}] [${log.level.toUpperCase()}] ${log.message}${dataStr}`;
      })
      .join('\n');

    try {
      // 优先使用现代Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(logText);
        addLog('info', '日志已复制到剪贴板');
      } else {
        // 降级到传统方法
        const textArea = document.createElement('textarea');
        textArea.value = logText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          addLog('info', '日志已复制到剪贴板');
        } else {
          throw new Error('复制失败');
        }
      }
    } catch (error) {
      console.error('复制失败:', error);
      addLog('error', '复制失败，请手动选择复制');

      // 最后的fallback：显示复制内容
      alert(`复制失败，请手动复制以下内容：\n\n${logText}`);
    }
  }, [logs, filterLevel, searchText, addLog]);

  // 获取响应式尺寸
  const getResponsiveSize = () => {
    if (isMobile) {
      return {
        width: Math.min(width, window.innerWidth - 20),
        height: Math.min(height, window.innerHeight * 0.6),
        expandedHeight: Math.min(600, window.innerHeight * 0.8),
      };
    }
    return {
      width,
      height,
      expandedHeight: 600,
    };
  };

  // 获取位置样式
  const getPositionStyle = () => {
    const { width: responsiveWidth, height: responsiveHeight } =
      getResponsiveSize();

    // 收起状态显示为可拖动的悬浮按钮
    if (isMinimized) {
      const buttonStyle = {
        width: isMobile ? '60px' : '50px',
        height: isMobile ? '60px' : '50px',
        position: 'fixed' as const,
        zIndex: 9999,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: '#fff',
        border: '2px solid #333',
        borderRadius: '50%',
        transition: isDragging ? 'none' : 'all 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: isDragging ? 'grabbing' : 'grab',
        fontSize: isMobile ? '16px' : '14px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
        left: `${dragPosition.x}px`,
        top: `${dragPosition.y}px`,
        userSelect: 'none' as const,
      };

      return buttonStyle;
    }

    // 展开状态显示完整面板
    const baseStyle = {
      width: isMaximized
        ? isMobile
          ? 'calc(100vw - 20px)'
          : '90vw'
        : isMobile
          ? `${responsiveWidth}px`
          : `${responsiveWidth}px`,
      height: isMaximized
        ? isMobile
          ? 'calc(100vh - 20px)'
          : '90vh'
        : `${responsiveHeight}px`,
      position: 'fixed' as const,
      zIndex: 9999,
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      color: '#fff',
      fontFamily: 'monospace',
      fontSize: isMobile ? '14px' : '12px',
      border: '1px solid #333',
      borderRadius: '4px',
      transition: 'all 0.3s ease',
      maxWidth: isMaximized ? 'none' : isMobile ? 'calc(100vw - 20px)' : 'none',
      maxHeight: isMaximized
        ? 'none'
        : isMobile
          ? 'calc(100vh - 20px)'
          : 'none',
    };

    // 移动端默认使用底部居中位置
    const mobilePosition = 'bottom-center';
    const actualPosition = isMobile ? mobilePosition : position;

    switch (actualPosition) {
      case 'top-right':
        return { ...baseStyle, top: '10px', right: '10px' };
      case 'bottom-right':
        return { ...baseStyle, bottom: '10px', right: '10px' };
      case 'bottom-left':
        return { ...baseStyle, bottom: '10px', left: '10px' };
      case 'top-left':
        return { ...baseStyle, top: '10px', left: '10px' };
      case 'bottom-center':
        return {
          ...baseStyle,
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: isMaximized
            ? isMobile
              ? 'calc(100vw - 20px)'
              : '90vw'
            : `${Math.min(responsiveWidth, window.innerWidth - 20)}px`,
        };
      default:
        return { ...baseStyle, bottom: '10px', right: '10px' };
    }
  };

  // 获取日志级别颜色
  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error':
        return '#ff6b6b';
      case 'warn':
        return '#ffd93d';
      case 'info':
        return '#6bcf7f';
      default:
        return '#fff';
    }
  };

  // 过滤日志
  const filteredLogs = logs.filter(log => {
    if (filterLevel !== 'all' && log.level !== filterLevel) return false;
    if (
      searchText &&
      !log.message.toLowerCase().includes(searchText.toLowerCase())
    )
      return false;
    return true;
  });

  // 自动滚动到底部
  useEffect(() => {
    if (logContainerRef.current && !isMinimized) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs, isMinimized]);

  // 重定向console方法
  useEffect(() => {
    if (!visible) return;

    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
    };

    // 重定向console方法
    console.log = (...args) => {
      originalConsole.log(...args);
      addLog('log', args[0]?.toString() || '', args.slice(1));
    };

    console.warn = (...args) => {
      originalConsole.warn(...args);
      addLog('warn', args[0]?.toString() || '', args.slice(1));
    };

    console.error = (...args) => {
      originalConsole.error(...args);
      addLog('error', args[0]?.toString() || '', args.slice(1));
    };

    console.info = (...args) => {
      originalConsole.info(...args);
      addLog('info', args[0]?.toString() || '', args.slice(1));
    };

    // 恢复原始console方法
    return () => {
      console.log = originalConsole.log;
      console.warn = originalConsole.warn;
      console.error = originalConsole.error;
      console.info = originalConsole.info;
    };
  }, [visible, addLog]);

  if (!visible) return null;

  const { expandedHeight } = getResponsiveSize();
  const buttonSize = isMobile ? '32px' : '24px';
  const buttonPadding = isMobile ? '6px 12px' : '2px 6px';
  const buttonFontSize = isMobile ? '12px' : '10px';

  return (
    <div ref={panelRef} style={getPositionStyle()}>
      {/* 收起状态显示悬浮按钮 */}
      {isMinimized ? (
        <div
          onMouseDown={handleDragStart}
          onTouchStart={handleDragStart}
          onClick={handleFloatingButtonClick}
          onTouchEnd={handleFloatingButtonClick}
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: '4px',
            touchAction: 'none',
            cursor: 'pointer',
          }}
        >
          <div style={{ fontSize: isMobile ? '20px' : '16px' }}>🐛</div>
          <div
            style={{
              fontSize: isMobile ? '10px' : '8px',
              textAlign: 'center',
              lineHeight: '1.2',
            }}
          >
            调试
          </div>
        </div>
      ) : (
        <>
          {/* 标题栏 */}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: isMobile ? '12px 16px' : '8px 12px',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderBottom: '1px solid #333',
              cursor: 'pointer',
              minHeight: buttonSize,
            }}
            onClick={handleMinimizeToggle}
          >
            <div
              style={{
                fontWeight: 'bold',
                fontSize: isMobile ? '14px' : '12px',
                flex: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              调试日志 ({filteredLogs.length}/{logs.length})
              {isMaximized && (
                <span
                  style={{
                    marginLeft: '8px',
                    fontSize: '10px',
                    color: '#888',
                    fontStyle: 'italic',
                  }}
                >
                  [已放大]
                </span>
              )}
            </div>
            <div
              style={{
                display: 'flex',
                gap: isMobile ? '8px' : '4px',
                flexShrink: 0,
              }}
            >
              <button
                onClick={e => {
                  e.stopPropagation();
                  handleMaximizeToggle();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#fff',
                  cursor: 'pointer',
                  padding: buttonPadding,
                  fontSize: buttonFontSize,
                  minWidth: buttonSize,
                  minHeight: buttonSize,
                  borderRadius: '4px',
                  transition: 'background-color 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onFocus={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onBlur={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                {isMaximized ? '缩小' : '放大'}
              </button>
              <button
                onClick={e => {
                  e.stopPropagation();
                  handleExpandToggle();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#fff',
                  cursor: 'pointer',
                  padding: buttonPadding,
                  fontSize: buttonFontSize,
                  minWidth: buttonSize,
                  minHeight: buttonSize,
                  borderRadius: '4px',
                  transition: 'background-color 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onFocus={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onBlur={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                {isExpanded ? '收起' : '展开'}
              </button>
              <button
                onClick={e => {
                  e.stopPropagation();
                  clearLogs();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#fff',
                  cursor: 'pointer',
                  padding: buttonPadding,
                  fontSize: buttonFontSize,
                  minWidth: buttonSize,
                  minHeight: buttonSize,
                  borderRadius: '4px',
                  transition: 'background-color 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onFocus={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onBlur={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                清空
              </button>
              <button
                onClick={e => {
                  e.stopPropagation();
                  void copyLogs();
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#fff',
                  cursor: 'pointer',
                  padding: buttonPadding,
                  fontSize: buttonFontSize,
                  minWidth: buttonSize,
                  minHeight: buttonSize,
                  borderRadius: '4px',
                  transition: 'background-color 0.2s',
                }}
                onMouseOver={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onMouseOut={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onFocus={e => {
                  e.currentTarget.style.backgroundColor =
                    'rgba(255, 255, 255, 0.1)';
                }}
                onBlur={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                复制
              </button>
            </div>
          </div>

          {/* 控制栏 */}
          <div
            style={{
              display: 'flex',
              gap: isMobile ? '12px' : '8px',
              padding: isMobile ? '12px 16px' : '8px 12px',
              borderBottom: '1px solid #333',
              flexDirection: isMobile ? 'column' : 'row',
            }}
          >
            {/* 日志级别过滤 */}
            <select
              value={filterLevel}
              onChange={e => setFilterLevel(e.target.value as any)}
              style={{
                background: '#333',
                color: '#fff',
                border: '1px solid #555',
                borderRadius: '4px',
                padding: isMobile ? '8px 12px' : '2px 6px',
                fontSize: isMobile ? '14px' : '10px',
                minHeight: isMobile ? '40px' : 'auto',
                flex: isMobile ? 'none' : 'none',
              }}
            >
              <option value="all">全部</option>
              <option value="log">Log</option>
              <option value="info">Info</option>
              <option value="warn">Warn</option>
              <option value="error">Error</option>
            </select>

            {/* 搜索框 */}
            <input
              type="text"
              placeholder="搜索日志..."
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{
                background: '#333',
                color: '#fff',
                border: '1px solid #555',
                borderRadius: '4px',
                padding: isMobile ? '8px 12px' : '2px 6px',
                fontSize: isMobile ? '14px' : '10px',
                flex: 1,
                minHeight: isMobile ? '40px' : 'auto',
              }}
            />
          </div>

          {/* 日志内容 */}
          <div
            ref={logContainerRef}
            style={{
              height: isExpanded
                ? `${expandedHeight}px`
                : `${getResponsiveSize().height - (isMobile ? 120 : 80)}px`,
              overflowY: 'auto',
              padding: isMobile ? '12px 16px' : '8px 12px',
              lineHeight: '1.4',
            }}
          >
            {filteredLogs.length === 0 ? (
              <div
                style={{
                  color: '#888',
                  textAlign: 'center',
                  padding: isMobile ? '40px 20px' : '20px',
                  fontSize: isMobile ? '14px' : '12px',
                }}
              >
                暂无日志
              </div>
            ) : (
              filteredLogs.map(log => (
                <div
                  key={log.id}
                  style={{
                    marginBottom: isMobile ? '8px' : '4px',
                    padding: isMobile ? '8px 0' : '4px 0',
                    borderBottom: '1px solid #333',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: isMobile ? '12px' : '8px',
                      flexDirection: isMobile ? 'column' : 'row',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: isMobile ? '12px' : '8px',
                        flexWrap: 'wrap',
                      }}
                    >
                      <span
                        style={{
                          color: getLevelColor(log.level),
                          fontWeight: 'bold',
                          minWidth: isMobile ? '50px' : '40px',
                          fontSize: isMobile ? '12px' : '10px',
                        }}
                      >
                        [{log.level.toUpperCase()}]
                      </span>
                      <span
                        style={{
                          color: '#888',
                          fontSize: isMobile ? '12px' : '10px',
                          minWidth: isMobile ? '80px' : '60px',
                        }}
                      >
                        {log.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <span
                      style={{
                        flex: 1,
                        wordBreak: 'break-word',
                        fontSize: isMobile ? '14px' : '12px',
                        lineHeight: '1.5',
                      }}
                    >
                      {log.message}
                    </span>
                  </div>
                  {log.data && log.data.length > 0 && (
                    <div
                      style={{
                        marginLeft: isMobile ? '0' : '108px',
                        marginTop: isMobile ? '8px' : '2px',
                      }}
                    >
                      <pre
                        style={{
                          color: '#aaa',
                          fontSize: isMobile ? '12px' : '10px',
                          margin: 0,
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-word',
                          backgroundColor: 'rgba(255, 255, 255, 0.05)',
                          padding: isMobile ? '8px' : '4px',
                          borderRadius: '4px',
                          overflow: 'auto',
                        }}
                      >
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default LogPanel;
