import { cookies } from 'next/headers';

import { CookiesKeyEnum } from '@/enum';
import { makeStore } from '@/store';
import { saleRoomInfo, setKeyValue } from '@/store/hotel/order';

import ServerNavbar from '../../../components/navbar/ServerNavbar';

import Agreement from './template/agreement';
import BookingPop from './template/bookingPop';
import BottomBar from './template/bottomBar';
import PolicyInfo from './template/policyInfo';
import TouristsInfo from './template/touristsInfo';

export default async function OrderPage({
  searchParams,
}: {
  searchParams: Promise<API.HotelOrderParams.OrderPageProps>;
}) {
  // 获取链接参数
  const params = await searchParams;
  const { roomKey, roomId, ratePlanId } = params;

  // 获取服务端 cookies
  const cookieStore = await cookies();
  const token = cookieStore.get(CookiesKeyEnum.AUTH_TOKEN)?.value || '';

  const store = makeStore();

  store.dispatch(
    setKeyValue({
      roomId,
      roomKey,
      ratePlanId,
      token,
    })
  );
  await store.dispatch(saleRoomInfo());

  const {
    hotelOrder: { roomInfoDetail },
  } = store.getState();

  console.log(roomInfoDetail, 'roomInfoDetail');

  if (Object.keys(roomInfoDetail).length) {
    return (
      <div className="w-screen h-screen bg-[#F9F9F9] flex flex-col ">
        {/* 头部navbar */}
        <ServerNavbar title={roomInfoDetail?.hotelName} />

        {/* 主体部分 */}
        <div className="flex-1 overflow-y-auto scroll-smooth pb-[100px] [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
          <PolicyInfo data={roomInfoDetail} />
          <TouristsInfo data={roomInfoDetail} />
          <Agreement />
        </div>

        {/* 底部支付按钮 */}
        <BottomBar data={roomInfoDetail} />
        {/* 酒店不可预定弹窗 */}
        <BookingPop />
      </div>
    );
  } else {
    return null;
  }
}
