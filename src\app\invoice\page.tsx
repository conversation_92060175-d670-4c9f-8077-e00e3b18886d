import { cookies } from 'next/headers';

import { getInvoiceInfo } from '@/api/hotel/orderDetail';
import ServerNavbar from '@/components/navbar/ServerNavbar';
import { CookiesKeyEnum } from '@/enum';

import HotelInvoice from './template/hotel';

export default async function OrderDetailPage({
  searchParams,
}: {
  searchParams: Promise<API.HotelOrderDetails.OrderDetailProps>;
}) {
  // 获取链接参数
  const params = await searchParams;
  const { orderNo } = params;

  // 获取服务端 cookies
  const cookieStore = await cookies();
  const token = cookieStore.get(CookiesKeyEnum.AUTH_TOKEN)?.value || '';

  try {
    const res = await getInvoiceInfo({ orderNo, token });

    if (!res) {
      throw new Error('No payload received from getOrderDetail');
    }

    const { code, message, data } = res;

    return (
      <div className="w-screen h-screen bg-[#F9F9F9]">
        <ServerNavbar fixed title="申请开票" />
        {code === 200 && data ? (
          <HotelInvoice data={res?.data} />
        ) : (
          <div className="h-full flex items-center justify-center ">
            {message}
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('Failed to fetch order details:', error);
    throw error;
  }
}
