import { type NextRequest, NextResponse } from 'next/server';

import { CookiesKeyEnum, DreameURLParamEnum } from '@/enum';

export const setupDreameAuth = async (
  request: NextRequest,
  response: NextResponse
) => {
  const url = request.nextUrl;
  const { searchParams } = request.nextUrl;
  if (url.pathname === '/') {
    const DmToken = searchParams.get(DreameURLParamEnum.DM_SESSION_ID) ?? '';
    /** Url debug token */
    const debugToken = searchParams.get(CookiesKeyEnum.AUTH_TOKEN) ?? '';
    const newUrl = url.clone();

    if (newUrl.searchParams.has(DreameURLParamEnum.DM_SESSION_ID)) {
      newUrl.searchParams.delete(DreameURLParamEnum.DM_SESSION_ID);
    }

    // 只有当URL确实被修改时才重定向
    if (newUrl.toString() !== url.toString()) {
      response = NextResponse.redirect(newUrl);
    }

    if (DmToken) {
      response.cookies.set(CookiesKeyEnum.DM_TOKEN, DmToken, {
        path: '/', // 对整个网站有效
        sameSite: 'lax', // 推荐的安全设置
        secure: process.env.NODE_ENV === 'production', // 生产环境启用HTTPS
        httpOnly: false, // 允许客户端JavaScript访问
      });
    }
    if (debugToken) {
      response.cookies.set(CookiesKeyEnum.AUTH_TOKEN, debugToken, {
        path: '/', // 对整个网站有效
        sameSite: 'lax', // 推荐的安全设置
        secure: process.env.NODE_ENV === 'production', // 生产环境启用HTTPS
        httpOnly: false, // 允许客户端JavaScript访问
      });
    }
  }
  return response;
};
