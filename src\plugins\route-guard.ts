import { type NextRequest, NextResponse } from 'next/server';

import { getServerToken } from '@/server/utils/index';

const WhiteList = [
  '/',
  '/login',
  '/hotel/baseInfo',
  '/agreements',
  '/test',
  '/exception/401',
  '/customTour',
];

export const setupRouteGuard = async (
  request: NextRequest,
  response: NextResponse
) => {
  const url = request.nextUrl;
  const token = await getServerToken();
  if (token) {
    // 如果已登录则熔断
    return response;
  } else {
    // 检查是否在白名单中，支持精确匹配和子路径匹配
    const isWhitelisted = WhiteList.some(
      path => url.pathname === path || url.pathname.startsWith(`${path}/`)
    );

    if (isWhitelisted) {
      return response;
    }
    // 参数异常，无法静默授权,跳转到登录页
    return NextResponse.redirect(new URL('/login', request.url));
  }
};
