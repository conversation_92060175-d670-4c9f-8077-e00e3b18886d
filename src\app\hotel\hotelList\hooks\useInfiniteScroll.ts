import { useEffect, useRef } from 'react';

/** 无限滚动Hook 使用Intersection Observer监听底部元素，触发加载更多 */
export function useInfiniteScroll(
  loadMore: () => void,
  deps: {
    hasMore: boolean;
    isLoading: boolean;
    isLoadingMore: boolean;
    isRefreshing: boolean;
  }
) {
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const { hasMore, isLoading, isLoadingMore, isRefreshing } = deps;

  // 使用 Intersection Observer 监听底部元素
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !isLoading &&
          !isLoadingMore &&
          !isRefreshing
        ) {
          void loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoading, isLoadingMore, isRefreshing, loadMore]);

  return {
    loadMoreRef,
  };
}
