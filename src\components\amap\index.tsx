'use client';
import { useEffect, useRef, useState } from 'react';

import { staticBaseUrl } from '@/config';
import { useAMap } from '@/hooks/useAMap';
import '@amap/amap-jsapi-types';

interface MapPoint {
  lat: string;
  lng: string;
  label: string;
}

export interface MapPreviewProps {
  /** Marker点 */
  points?: MapPoint[];
  /** 显示位置信息 */
  showLocation?: boolean;
  /** 用户位置信息 */
  location?: ILocation;
}

const AMapComponent = ({
  points = [],
  showLocation = false,
  location = { latitude: '', longitude: '' },
}: MapPreviewProps) => {
  const { AMapRef, loadAMap } = useAMap();
  const mapRef = useRef<Nullable<AMap.Map>>(null);
  const markersRef = useRef<AMap.Marker[]>([]);
  const locationMarkRef = useRef<Nullable<AMap.Marker>>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        await loadAMap({ plugins: ['AMap.Geolocation'] });
        if (!AMapRef.current) return;

        mapRef.current = new AMapRef.current.Map('map-container', {
          zoom: 11,
          center:
            points.length > 0
              ? [Number(points[0].lng), Number(points[0].lat)]
              : [116.397455, 39.909187],
        });

        if (showLocation) {
          if (location.latitude && location.longitude) {
            // 有位置信息则显示位置信息
            locationMarkRef.current = new AMapRef.current.Marker({
              position: [Number(location.longitude), Number(location.latitude)],
              anchor: 'bottom-center',
              icon: new AMapRef.current.Icon({
                image: `${staticBaseUrl}/location-icon.png`,
                imageSize: new AMapRef.current.Size(25, 25),
              }),
              offset: new AMapRef.current.Pixel(0, 0),
            });
            locationMarkRef.current?.setMap(mapRef.current);
          } else {
            // 没有位置信息则获取定位数据
            const geolocation = new (AMapRef.current as any).Geolocation({
              enableHighAccuracy: true,
              timeout: 10000,
              position: 'RB',
              zoomToAccuracy: true,
            });
            mapRef.current?.addControl(geolocation);
          }
          mapRef.current.setFitView();
        }
        setIsMapReady(true);
      } catch (error) {
        console.error('Failed to load AMap:', error);
      }
    };

    void init();

    return () => {
      mapRef.current?.destroy();
      mapRef.current = null;
      markersRef.current.forEach(marker => marker.setMap(null));
      markersRef.current = [];
      locationMarkRef.current?.setMap(null);
      setIsMapReady(false);
    };
  }, []);

  useEffect(() => {
    if (!isMapReady || !mapRef.current || !AMapRef.current) return;
    // 清除旧的marker
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];
    // 添加新marker
    markersRef.current = points.map(point => {
      const marker = new AMapRef.current!.Marker({
        position: [Number(point.lng), Number(point.lat)],
        anchor: 'bottom-center',
        offset: new AMapRef.current!.Pixel(0, 0),
      });
      marker.setLabel({
        direction: 'top',
        offset: new AMapRef.current!.Pixel(0, -5),
        content: `<div>${point.label}</div>`,
      });
      marker.setMap(mapRef.current!);
      return marker;
    });
    mapRef.current.setFitView();
  }, [points, isMapReady]);

  return (
    <div className="h-full w-full overflow-hidden">
      <style>{`#map-container .amap-marker-label {
        border-color: transparent;
      }`}</style>
      <div id="map-container" className="h-[calc(100%+18px)] w-full" />;
    </div>
  );
};

export default AMapComponent;
