interface RoomCardSkeletonProps {
  /** 显示的骨架屏房间卡片数量 */
  count?: number;
}

const RoomCardSkeleton = ({ count = 3 }: RoomCardSkeletonProps) => (
  <div className="bg-gray-50">
    {Array.from({ length: count }).map((_, roomIndex) => (
      <div
        key={roomIndex}
        className="bg-white mx-2 mb-2 rounded-[24px] overflow-hidden p-1"
      >
        {/* 房型头部信息骨架 */}
        <div className="flex mb-2">
          {/* 房间图片骨架 */}
          <div className="w-[96px] h-[96px] rounded-[20px] bg-gray-200 flex-shrink-0 mr-3" />

          <div className="flex-1">
            {/* 房间名称骨架 */}
            <div className="h-5 bg-gray-200 rounded my-1 w-3/4" />

            {/* 描述骨架 */}
            <div className="h-3 bg-gray-200 rounded mb-1 w-full" />

            {/* 标签骨架 */}
            <div className="flex w-full h-6 gap-2 py-0.5">
              <div className="h-4 bg-gray-200 rounded w-12 flex-shrink-0" />
              <div className="h-4 bg-gray-200 rounded w-16 flex-shrink-0" />
              <div className="h-4 bg-gray-200 rounded w-14 flex-shrink-0" />
            </div>
          </div>
        </div>

        {/* 价格选项列表骨架 - 每个房间显示2-3个价格选项 */}
        {Array.from({ length: 2 + (roomIndex % 2) }).map((_, priceIndex) => (
          <div
            key={priceIndex}
            className="p-2 bg-bg-light rounded-[20px] mb-1 last:mb-0"
          >
            <div className="flex justify-between items-end">
              <div className="flex-1">
                {/* 早餐和取消政策骨架 */}
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-4 bg-bg-gray rounded w-16" />
                  <div className="w-[1px] h-[14px] bg-bg-gray" />
                  <div className="h-4 bg-bg-gray rounded w-24" />
                </div>

                {/* 房型信息骨架 */}
                <div className="h-3 bg-bg-gray rounded mb-3 w-4/5" />

                {/* 政策标签骨架 */}
                <div className="flex gap-2">
                  <div className="h-6 bg-bg-gray rounded w-12" />
                  <div className="h-6 bg-bg-gray rounded w-16" />
                </div>

                {/* 价格和预订按钮骨架 */}
                <div className="flex float-right items-center ml-4">
                  <div className="h-3 bg-gray-200 rounded w-4 mt-1 mr-1" />
                  <div className="h-8 bg-gray-200 rounded w-12 mr-2" />
                  <div className="bg-gray-200 h-[48px] w-[48px] rounded-lg" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    ))}
  </div>
);

export default RoomCardSkeleton;
