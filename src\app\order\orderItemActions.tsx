'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { PaymentSheet } from '@/components';
import { tracker } from '@/utils/track';

interface OrderItemActionsProps {
  /** 订单ID */
  orderId: string;
  /** 订单金额 */
  orderAmount: string;
  /** 是否显示支付按钮 */
  showPayButton?: boolean;
  /** 商品名称 */
  goodsName: string;
  // 酒店id
  hotelId: string;
  /** 最后支付时间 */
  lastPayTime: number;
}

/** 订单项操作按钮组件 - 客户端交互 */
export const OrderItemActions: React.FC<OrderItemActionsProps> = ({
  orderId,
  orderAmount,
  hotelId,
  showPayButton = true,
  goodsName,
  lastPayTime,
}) => {
  const router = useRouter();
  const [showPaymentSheet, setShowPaymentSheet] = useState(false);

  // 处理再次预订
  const handleReorder = () => {
    // 再次预订按钮点击埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_reorder_click',
        orderNo: orderId,
        hotelId,
        goodsName,
        orderAmount,
        lastPayTime,
      },
    });
    // 跳转到订单详情页面
    router.push(`/hotel/hotelDetails?hotelId=${hotelId}`);
  };

  // 处理去支付点击
  const handlePayClick = () => {
    // 去支付按钮点击埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_pay_click',
        orderNo: orderId,
        hotelId,
        goodsName,
        orderAmount,
        lastPayTime,
      },
    });
    setShowPaymentSheet(true);
  };
  // 处理确认支付完成
  const handlePaymentConfirm = async () => {
    // 关闭支付弹框
    setShowPaymentSheet(false);
    // 跳转到订单详情页面
    return new Promise<void>(resolve => {
      router.push(`/hotel/orderDetail?orderNo=${orderId}`);
      // 模拟跳转需要一点时间
      setTimeout(resolve, 500);
    });
  };

  return (
    <>
      <div className="flex items-center">
        {/* 再次预订按钮 */}
        <button
          onClick={handleReorder}
          className="bg-[#F7F7F7] rounded-[60px] w-[101px] h-[36px] text-[#11111E] font-bold text-[14px] flex items-center justify-center hover:bg-[#EEEEEE] transition-colors"
        >
          再次预订
        </button>

        {/* 去支付按钮 */}
        {showPayButton && (
          <button
            onClick={handlePayClick}
            className="ml-[4px] rounded-[60px] w-[101px] h-[36px] text-[#11111E] text-[14px] font-bold bg-[#E8DEC1] flex items-center justify-center hover:bg-[#DDD4B8] transition-colors"
          >
            去支付
          </button>
        )}
      </div>

      {/* 支付方式选择弹框 */}
      <PaymentSheet
        lastPayTime={lastPayTime}
        visible={showPaymentSheet}
        onClose={() => setShowPaymentSheet(false)}
        orderId={orderId}
        goodsName={goodsName}
        orderAmount={orderAmount}
        onPaymentConfirm={handlePaymentConfirm}
      />
    </>
  );
};

export default OrderItemActions;
