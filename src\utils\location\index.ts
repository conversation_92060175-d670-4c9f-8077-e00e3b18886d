import Native from '@/utils/native';
import { tracker, tryCatchTracker } from '@/utils/track';

/**
 * Dreame App获取定位
 *
 * @returns
 */
export const getLocationByDreame = async (): Promise<
  App.Service.IResponse<ILocation & Pick<IGeoInfo, 'coordinateType'>>
> => {
  const { success, data, isTimeout } = await Native.getLocationWithMessage();
  const isSuccess = Boolean(success && data?.lat && (data.lng || data.lon));
  const longitude = data?.lng || data?.lon || '';

  tracker({
    eventType: 'info',
    extra: {
      action: 'dreame_app_location_bridge_result',
      success,
      data,
      isTimeout,
    },
  });
  return {
    isSuccess,
    code: isSuccess ? 200 : 0,
    message: isSuccess
      ? ''
      : isTimeout
        ? '获取定位超时，请重试'
        : '获取系统定位失败请重试，请检查定位权限是否授权',
    data: {
      latitude: data?.lat ?? '',
      longitude,
      coordinateType: 'WGS-84',
    },
  };
};

/**
 * 浏览器定位
 *
 * @param options
 * @returns
 */
export const getLocationByBrowser = (
  options: PositionOptions = {
    enableHighAccuracy: true, // 请求高精度
    timeout: 10000, // 超时时间(毫秒)
    maximumAge: 0, // 不接受缓存位置
  }
): Promise<
  App.Service.IResponse<ILocation & Pick<IGeoInfo, 'coordinateType'>>
> =>
  new Promise(resolve => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const { latitude, longitude } = position.coords;
          resolve({
            isSuccess: true,
            code: 200,
            message: '',
            data: {
              latitude,
              longitude,
              coordinateType: 'WGS-84',
            },
          });
        },
        _error => {
          tryCatchTracker(_error, {
            scene: 'browser_location_exception',
          });
          resolve({
            isSuccess: false,
            code: 0,
            message: _error.message,
            data: {
              longitude: '',
              latitude: '',
              coordinateType: 'WGS-84',
            },
          });
          // switch (error.code) {
          //   case error.PERMISSION_DENIED:
          //     alert('用户拒绝了定位请求');
          //     break;
          //   case error.POSITION_UNAVAILABLE:
          //     alert('位置信息不可用');
          //     break;
          //   case error.TIMEOUT:
          //     alert('获取位置请求超时');
          //     break;
          //   default:
          //     alert('未知错误');
          // }
        },
        options
      );
    } else {
      tracker({
        eventType: 'info',
        extra: {
          action: 'browser_location_result',
          message: 'navigator.geolocation 不存在，浏览器不支持定位',
        },
      });
      resolve({
        isSuccess: false,
        code: 0,
        message: '浏览器不支持定位',
        data: {
          longitude: '',
          latitude: '',
          coordinateType: 'WGS-84',
        },
      });
    }
  });

/** 高德sdk定位 */
export const getLocationByAmapSdk = (
  AMap: any
): Promise<
  App.Service.IResponse<ILocation & Pick<IGeoInfo, 'coordinateType'>>
> =>
  // eslint-disable-next-line @typescript-eslint/no-misused-promises, no-async-promise-executor
  new Promise(async resolve => {
    try {
      if (!AMap) {
        resolve({
          isSuccess: false,
          code: 0,
          message: '定位失败',
          data: {
            longitude: '',
            latitude: '',
            coordinateType: 'GCJ-02',
          },
        });
        return;
      }

      const geolocation = new AMap.Geolocation({
        enableHighAccuracy: true,
        timeout: 10000,
        position: 'RB',
        zoomToAccuracy: true,
      });

      geolocation.getCurrentPosition((status: string, result: any) => {
        if (status === 'complete') {
          resolve({
            isSuccess: true,
            code: 200,
            message: '',
            data: {
              latitude: result.position.lat,
              longitude: result.position.lng,
              coordinateType: 'GCJ-02',
            },
          });
        } else {
          tracker({
            eventType: 'info',
            extra: {
              action: 'amap_sdk_exception',
              status,
              result,
            },
          });
          resolve({
            isSuccess: false,
            code: 0,
            message: '无法获取位置信息，请手动选择城市',
            data: {
              longitude: '',
              latitude: '',
              coordinateType: 'GCJ-02',
            },
          });
        }
      });
    } catch (_error) {
      tryCatchTracker(_error, {
        scene: 'location_amap_sdk',
      });
      resolve({
        isSuccess: false,
        code: 0,
        message: '无法获取位置信息，请手动选择城市',
        data: {
          longitude: '',
          latitude: '',
          coordinateType: 'GCJ-02',
        },
      });
    }
  });
