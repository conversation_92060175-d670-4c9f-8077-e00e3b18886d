'use client';

import { useCallback, useEffect, useMemo } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { useUser } from '@/context';

import { login, sendCode } from '../../api/login/index';
import { Toast } from '../../components';
import { tracker, tryCatchTracker } from '../../utils/track';

import {
  AGREEMENT_TEXTS,
  AGREEMENT_TYPES,
  BUTTON_TEXTS,
  COUNTDOWN_INITIAL_SECONDS,
  ERROR_MESSAGES,
  FORM_FIELDS,
  LOGIN_TYPE,
  PLACEHOLDER_TEXTS,
  SMS_CODE_LENGTH,
  SMS_TYPE,
  SUCCESS_MESSAGES,
} from './const';
import { useApiCall } from './hooks/useApiCall';
import { useCountdown } from './hooks/useCountdown';
import { useLoginForm } from './hooks/useLoginForm';

const LoginForm = () => {
  const router = useRouter();
  const { updateToken } = useUser();

  // 使用自定义hooks
  const form = useLoginForm();
  const countdown = useCountdown({
    initialSeconds: COUNTDOWN_INITIAL_SECONDS,
  });
  const sendCodeApi = useApiCall(sendCode);
  const loginApi = useApiCall(login);

  // 页面曝光埋点
  useEffect(() => {
    tracker({
      eventType: 'pv',
      path: window.location.pathname,
      extra: { page: 'login' },
    });
  }, []);

  // 发送验证码
  const handleSendCode = useCallback(async () => {
    // 验证手机号
    if (!form.validateField(FORM_FIELDS.PHONE)) {
      // 显示验证错误提示
      Toast({ message: form.errors.phone || '请输入正确的手机号' });
      return;
    }

    // 检查是否正在倒计时
    if (countdown.isRunning) {
      return;
    }

    // 发送验证码按钮点击埋点
    tracker({
      eventType: 'click',
      extra: { action: 'send_code', mobile: form.phone },
    });

    try {
      const response = await sendCodeApi.execute({
        mobile: form.phone,
        smsType: SMS_TYPE,
      });

      if (response?.isSuccess) {
        // 发送验证码成功埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'send_code_success',
            mobile: form.phone,
          },
        });
        Toast({
          message: response.message || SUCCESS_MESSAGES.SEND_CODE_SUCCESS,
        });
        countdown.start();
        return;
      }
      // 发送验证码失败埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'send_code_fail',
          mobile: form.phone,
          message: response?.message || ERROR_MESSAGES.SEND_CODE_FAILED,
        },
      });
      Toast({
        message: response?.message || ERROR_MESSAGES.SEND_CODE_FAILED,
      });
    } catch (_error) {
      // 发送验证码异常埋点
      tryCatchTracker(_error, {
        scene: 'login_send_code',
        mobile: form.phone,
      });
      Toast({ message: ERROR_MESSAGES.SEND_CODE_FAILED });
    }
  }, [form, countdown, sendCodeApi]);

  // 处理登录
  const handleLogin = useCallback(async () => {
    // 验证所有字段
    const validationResult = form.validateAll();
    if (!validationResult.isValid) {
      // 显示第一个验证错误提示
      Toast({ message: validationResult.firstError });
      return;
    }

    // 登录按钮点击埋点
    tracker({
      eventType: 'click',
      extra: { action: 'login', mobile: form.phone },
    });

    try {
      const response = await loginApi.execute({
        mobile: form.phone,
        smsCode: form.smsCode,
        loginType: LOGIN_TYPE,
      });

      if (response?.isSuccess) {
        // 如果接口返回了token，设置到cookie中
        if (response.data?.token) {
          updateToken(response.data.token);
        }

        // 登录成功埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'login_success',
            mobile: form.phone,
          },
        });
        Toast({ message: SUCCESS_MESSAGES.LOGIN_SUCCESS });
        router.replace('/');
        return;
      }
      // 登录失败埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'login_fail',
          mobile: form.phone,
          message: response?.message || ERROR_MESSAGES.LOGIN_FAILED,
        },
      });
      Toast({ message: response?.message || ERROR_MESSAGES.LOGIN_FAILED });
    } catch (_error) {
      // 登录异常埋点
      tryCatchTracker(_error, {
        scene: 'login',
        mobile: form.phone,
      });
      Toast({ message: ERROR_MESSAGES.LOGIN_FAILED });
    }
  }, [form, loginApi, router]);

  // 处理协议勾选
  const handleAgreementToggle = useCallback(() => {
    const newAgreed = !form.isAgreed;
    form.setField(FORM_FIELDS.AGREED, newAgreed);

    // 协议勾选埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'toggle_agreement',
        checked: newAgreed,
      },
    });
  }, [form]);

  // 处理协议跳转埋点
  const handleAgreementLinkClick = useCallback((type: string) => {
    // 协议跳转埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'jump_agreement',
        type,
      },
    });
  }, []);

  // 计算发送验证码按钮文本
  const sendCodeButtonText = useMemo(() => {
    if (countdown.countdown > 0) {
      return `${countdown.countdown}${BUTTON_TEXTS.SEND_CODE_COUNTDOWN}`;
    }
    return BUTTON_TEXTS.SEND_CODE;
  }, [countdown.countdown]);

  // 计算发送验证码按钮是否禁用
  const isSendCodeDisabled = useMemo(
    () => sendCodeApi.loading || countdown.isRunning,
    [sendCodeApi.loading, countdown.isRunning]
  );

  // 计算登录按钮是否禁用
  const isLoginDisabled = useMemo(
    () => loginApi.loading || form.isSubmitting,
    [loginApi.loading, form.isSubmitting]
  );

  return (
    <>
      {/* 表单区域 */}
      <div className="mb-[24px]">
        {/* 手机号输入 */}
        <div className="h-[56px] px-[24px] flex items-center justify-between bg-bg-card rounded-[20px] mb-[4px]">
          <span className="text-[16px] text-text-primary w-[50px] m-[16px]">
            +86
          </span>
          <input
            type="number"
            className="text-[16px] text-text-primary bg-transparent flex-1"
            placeholder={PLACEHOLDER_TEXTS.PHONE}
            value={form.phone}
            onChange={e => form.setField(FORM_FIELDS.PHONE, e.target.value)}
            disabled={loginApi.loading}
          />
        </div>

        {/* 验证码输入 */}
        <div className="h-[56px] px-[24px] flex items-center bg-bg-card rounded-[20px]">
          <input
            type="text"
            maxLength={SMS_CODE_LENGTH}
            className="text-[16px] text-text-primary bg-transparent flex-1 min-w-0"
            placeholder={PLACEHOLDER_TEXTS.SMS_CODE}
            value={form.smsCode}
            onChange={e => form.setField(FORM_FIELDS.SMS_CODE, e.target.value)}
            disabled={loginApi.loading}
          />
          <span
            className={`text-[14px] text-text-primary cursor-pointer hover:opacity-80 transition-opacity duration-200 whitespace-nowrap flex-shrink-0 ml-[12px] ${
              isSendCodeDisabled ? 'text-text-muted' : ''
            }`}
            onClick={handleSendCode}
          >
            {sendCodeButtonText}
          </span>
        </div>
      </div>

      {/* 登录按钮 */}
      <div className="mt-[20px]">
        <span
          className={`h-[60px] flex items-center justify-center bg-btn-primary-bg rounded-[60px] text-[16px] text-btn-primary-text cursor-pointer hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200 ${
            isLoginDisabled ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={isLoginDisabled ? undefined : handleLogin}
        >
          {loginApi.loading ? '登录中...' : BUTTON_TEXTS.LOGIN}
        </span>
      </div>

      {/* 协议区域 */}
      <div className="mt-[36px] flex items-baseline">
        <span
          className="flex items-center justify-center relative top-[1px]"
          onClick={handleAgreementToggle}
        >
          {form.isAgreed ? (
            <i className="iconfont icon-a-Property1gouxuanxuanzhong text-btn-primary-bg w-[18px] h-[18px] mr-[4px]" />
          ) : (
            <i className="iconfont icon-a-Property1gouxuanweixuanzhongyuanquan text-bg-card w-[18px] h-[18px] mr-[4px]" />
          )}
        </span>
        <div className="text-[14px] text-bg-card">
          <span>{AGREEMENT_TEXTS.AGREED_TEXT}</span>
          <Link
            href={`/agreements/${AGREEMENT_TYPES.USER}`}
            className="text-[#568DED] cursor-pointer"
            onClick={() => handleAgreementLinkClick(AGREEMENT_TYPES.USER)}
          >
            {AGREEMENT_TEXTS.USER_SERVICE}
          </Link>
          、
          <Link
            href={`/agreements/${AGREEMENT_TYPES.PROTECT}`}
            className="text-[#568DED] cursor-pointer"
            onClick={() => handleAgreementLinkClick(AGREEMENT_TYPES.PROTECT)}
          >
            {AGREEMENT_TEXTS.PRIVACY}
          </Link>
        </div>
      </div>
    </>
  );
};

export default LoginForm;
