declare namespace Home {
  interface IBusinessItem {
    /** 选中业务logo */
    activeIcon: string;
    /** 反选业务logo */
    inactiveIcon: string;
    /** 业务名称 */
    name: string;
    /** 业务标识 */
    type: 'flight' | 'train' | 'hotel' | 'ticket';
  }
  /** 推荐酒店模型 */
  interface IRecommendHotel extends ILocation, ICity {
    /** 酒店id */
    outId: string;
    /** 酒店名称 */
    hotelName: string;
    /** 类型 */
    category: number;
    /** 类型描述 */
    categoryDesc: string;
    /** 星级 */
    starRate: number;
    /** 酒店地址 */
    address: string;
    /** 酒店主图 */
    mainImage: string;
    /** 距离 */
    distance: string;
    /** 评分 */
    serviceScore: string;
    /** 评分描述 */
    serviceScoreDesc: string;
    /** 酒店描述 */
    hotelDesc: string;
    /** 最低价格 */
    minPrice: number;
    /** 币种 */
    currency: string;
  }

  type GetRecommendHotelParams = ILocation &
    API.Common.CommonSearchParams &
    Pick<ICity, 'cityName'> & {
      /** 国内/国际标识 1:国内 2:国际 */
      domesticIntl: 1 | 2;
      /** 详见HotelBusinessTypeEnum */
      businessType: number;
    };

  /** 机票登录授权响应 */
  interface IFlightAuthResponse {
    /** 机票跳转url */
    flightUrl: string;
  }
}
