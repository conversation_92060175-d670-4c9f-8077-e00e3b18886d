'use client';

import { useEffect, useRef, useState } from 'react';

import { Loading } from 'react-vant';

import { getPaymentTypeList, invocationPayment } from '@/api/pay';
import { ActionSheet, Toast } from '@/components';
import Dialog from '@/components/dialog';
import { tracker, tryCatchTracker } from '@/utils/track';

import { Native } from '../../utils';

interface PaymentSheetProps {
  /** 是否显示弹框 */
  visible: boolean;
  /** 关闭弹框回调 */
  onClose: () => void;
  /** 订单ID */
  orderId: string;
  /** 订单金额 */
  orderAmount: string;
  /** 商品名称 */
  goodsName: string;
  /** 支付成功回调 */
  onPaymentSuccess?: () => void;
  /** 支付确认回调 */
  onPaymentConfirm?: () => void | Promise<void>;
  /** 支付剩余时间（毫秒，可选） */
  lastPayTime?: number;
}

/** 支付方式选择弹框组件 */
export const PaymentSheet: React.FC<PaymentSheetProps> = ({
  visible,
  onClose,
  orderId,
  orderAmount,
  goodsName,
  onPaymentSuccess,
  onPaymentConfirm,
  lastPayTime,
}) => {
  // 选中的支付方式
  const [selectedPayment, setSelectedPayment] = useState<string>('');
  // 支付方式列表
  const [paymentMethods, setPaymentMethods] = useState<Pay.IPaymentType[]>([]);
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [methodsLoading, setMethodsLoading] = useState(false);
  // 是否显示支付确认弹框
  const [showPaymentConfirm, setShowPaymentConfirm] = useState(false);
  // 是否已跳转到第三方支付
  const [hasRedirectedToThirdParty, setHasRedirectedToThirdParty] =
    useState(false);
  // 导航跳转loading状态
  const [showNavigationLoading, setShowNavigationLoading] = useState(false);

  // iframe相关状态
  const [showIframe, setShowIframe] = useState(false);
  const [iframeUrl, setIframeUrl] = useState('');
  const [iframeLoading, setIframeLoading] = useState(false);

  // 30分钟倒计时（从29:59开始）
  const DEFAULT_COUNTDOWN_SECONDS = 30 * 60 - 1; // 1799秒
  // lastPayTime优先，单位毫秒转秒，异常值视为0
  const initialSeconds =
    lastPayTime && typeof lastPayTime === 'number' && lastPayTime > 0
      ? Math.floor(lastPayTime / 1000)
      : lastPayTime === 0
        ? 0
        : DEFAULT_COUNTDOWN_SECONDS;
  const [secondsLeft, setSecondsLeft] = useState(initialSeconds);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 格式化为"mm分ss秒"
  const formatCountdown = (secs: number) => {
    const mm = String(Math.floor(secs / 60)).padStart(2, '0');
    const ss = String(secs % 60).padStart(2, '0');
    return `${mm}分${ss}秒`;
  };

  // 检测是否为安卓Web环境
  const isAndroidWeb = () =>
    Native.isAndroidDevice() && !Native.isInNativeApp();
  // 获取支付方式列表
  const fetchPaymentMethods = async () => {
    try {
      // 获取支付方式列表开始埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'payment_methods_fetch_start',
          orderNo: orderId,
          orderAmount,
        },
      });

      const response = await getPaymentTypeList();

      if (response.code === 200 && response.data) {
        // 获取支付方式列表成功埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'payment_methods_fetch_success',
            orderNo: orderId,
            methodsCount: response.data.length,
            methods: response.data.map(m => m.payType),
            defaultMethod:
              response.data.length > 0 ? response.data[0].payType : null,
          },
        });

        setPaymentMethods(response.data);
        // 默认选择第一个支付方式
        if (response.data.length > 0) {
          setSelectedPayment(response.data[0].payType);
        }
      } else {
        // 获取支付方式列表失败埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'payment_methods_fetch_fail',
            orderNo: orderId,
            responseCode: response?.code,
            message: response?.message,
          },
        });
      }
    } catch (error) {
      // 获取支付方式列表异常埋点
      tryCatchTracker(error, {
        scene: 'payment_methods_fetch',
        orderNo: orderId,
        orderAmount,
      });
      console.error('获取支付方式失败:', error);
    } finally {
      setMethodsLoading(false);
    }
  };

  // 当弹窗显示时获取支付方式
  useEffect(() => {
    if (visible) {
      // 支付弹框显示埋点
      tracker({
        eventType: 'pv',
        extra: {
          action: 'payment_sheet_show',
          orderNo: orderId,
          orderAmount,
          goodsName: goodsName ?? '',
        },
      });

      setMethodsLoading(true);
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      fetchPaymentMethods();
    }
  }, [visible]);

  // 处理lastPayTime为0或负数，直接关闭弹框并刷新页面
  useEffect(() => {
    if (visible && (!lastPayTime || lastPayTime <= 0)) return; // 兼容未传lastPayTime
    if (visible && lastPayTime === 0) {
      onClose();
      window.location.reload();
    }
  }, [visible, lastPayTime, onClose]);

  // 倒计时逻辑
  useEffect(() => {
    if (visible) {
      setSecondsLeft(initialSeconds);
      if (initialSeconds <= 0) {
        onClose();
        window.location.reload();
        return;
      }
      timerRef.current = setInterval(() => {
        setSecondsLeft(prev => {
          if (prev <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (timerRef.current) clearInterval(timerRef.current);
    }
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, initialSeconds]);

  // 监听secondsLeft归零，自动关闭弹框并刷新页面
  useEffect(() => {
    if (visible && secondsLeft === 0) {
      onClose();
      window.location.reload();
    }
  }, [visible, secondsLeft, onClose]);

  // 监听页面可见性变化，检测从第三方支付页面返回
  useEffect(() => {
    const handleVisibilityChange = () => {
      // 当页面重新变为可见且已经跳转过第三方支付时，显示支付确认弹框
      if (!document.hidden && hasRedirectedToThirdParty) {
        // 从第三方支付返回埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'payment_return_from_third_party',
            payType: selectedPayment,
            orderNo: orderId,
          },
        });

        setShowPaymentConfirm(true);
        setHasRedirectedToThirdParty(false); // 重置状态
        // 关闭iframe
        setShowIframe(false);
        setIframeUrl('');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [hasRedirectedToThirdParty, selectedPayment, orderId]);

  // 处理支付方式选择
  const handlePaymentMethodSelect = (paymentType: string) => {
    // 支付方式选择埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'payment_method_select',
        payType: paymentType,
        orderNo: orderId,
        previousPayType: selectedPayment,
      },
    });

    setSelectedPayment(paymentType);
  };

  // 处理支付确认弹框的关闭
  const handlePaymentConfirmClose = () => {
    // 支付确认弹框关闭埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'payment_confirm_dialog_close',
        orderNo: orderId,
        payType: selectedPayment,
      },
    });

    setShowPaymentConfirm(false);
    onClose(); // 关闭整个支付弹框
  };

  // 处理支付确认 - 跳转到订单详情页面
  const handlePaymentConfirmAction = async () => {
    // 支付确认操作埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'payment_confirm_action',
        orderNo: orderId,
        payType: selectedPayment,
      },
    });

    // 立即关闭支付确认弹框
    setShowPaymentConfirm(false);
    // 显示导航loading
    setShowNavigationLoading(true);

    try {
      // 调用父组件的确认回调
      if (onPaymentConfirm) {
        await onPaymentConfirm();
      }
    } catch (error) {
      // 支付确认跳转失败埋点
      tryCatchTracker(error, {
        scene: 'payment_confirm_navigation',
        orderNo: orderId,
        payType: selectedPayment,
      });
      console.error('支付确认跳转失败:', error);
    } finally {
      setShowNavigationLoading(false);
    }
  };

  // 处理立即支付
  const handlePayNow = async () => {
    if (!selectedPayment) {
      // 未选择支付方式埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'payment_no_method_selected',
          orderNo: orderId,
          orderAmount,
        },
      });
      return;
    }

    // 立即支付按钮点击埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'payment_pay_now_click',
        payType: selectedPayment,
        orderNo: orderId,
        orderAmount,
        goodsName: goodsName ?? '',
      },
    });

    setLoading(true);

    try {
      // 支付API调用开始埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'payment_api_start',
          payType: selectedPayment,
          orderNo: orderId,
          orderAmount,
          goodsName: goodsName ?? '',
        },
      });

      // 调用支付API
      const response = await invocationPayment({
        payType: selectedPayment,
        orderNo: orderId,
        orderPayMoney: orderAmount,
        goodsName: goodsName ?? '',
      });

      if (response.code === 200) {
        // 支付API调用成功埋点
        tracker({
          eventType: 'click',
          extra: {
            action: 'payment_api_success',
            payType: selectedPayment,
            orderNo: orderId,
            orderAmount,
            hasGatewayUrl: !!response.data?.gatewayUrl,
          },
        });

        // 检查是否有payload字段，如果有则跳转到指定地址
        if (response.data?.gatewayUrl) {
          // 第三方支付跳转埋点
          tracker({
            eventType: 'click',
            extra: {
              action: 'payment_redirect_to_third_party',
              payType: selectedPayment,
              orderNo: orderId,
              gatewayUrl: response.data.gatewayUrl,
              isInNativeApp: Native.isInNativeApp(),
            },
          });

          // 标记已跳转到第三方支付
          setHasRedirectedToThirdParty(true);

          // 只在安卓Web环境下且为微信支付时使用iframe
          if (isAndroidWeb() && selectedPayment === 'WECHAT') {
            // 微信支付iframe跳转埋点
            tracker({
              eventType: 'click',
              extra: {
                action: 'payment_wechat_iframe_jump',
                orderNo: orderId,
                gatewayUrl: response.data.gatewayUrl,
                platform: 'android_web',
              },
            });
            setIframeUrl(response.data.gatewayUrl);
            setShowIframe(true);
            return;
          }
          // 其他web跳转情况 ios的支付宝微信，和安卓的支付宝
          if (!Native.isInNativeApp()) {
            window.location.replace(response.data.gatewayUrl);
            return;
          }
          // 追觅app跳转
          // 跳转到payload指定的地址，用于拉起支付宝或微信App
          if (selectedPayment === 'WECHAT') {
            // 微信支付Native跳转埋点
            tracker({
              eventType: 'click',
              extra: {
                action: 'payment_wechat_native_jump',
                orderNo: orderId,
                gatewayUrl: response.data.gatewayUrl,
              },
            });
            Native.jumpOuterPage(response.data.gatewayUrl);
            return;
          }
          window.location.replace(response.data.gatewayUrl);
          return; // 跳转后不需要执行后续逻辑
        }

        // 直接支付成功埋点（无需第三方跳转）
        tracker({
          eventType: 'click',
          extra: {
            action: 'payment_direct_success',
            payType: selectedPayment,
            orderNo: orderId,
            orderAmount,
          },
        });

        // 如果没有payload字段，执行原有的成功回调逻辑
        onPaymentSuccess?.();
        onClose();
        return;
      }

      // 支付API调用失败埋点
      tracker({
        eventType: 'click',
        extra: {
          action: 'payment_api_fail',
          payType: selectedPayment,
          orderNo: orderId,
          orderAmount,
          responseCode: response?.code,
          message: response?.message,
        },
      });

      const { message } = response || {};
      if (message) {
        Toast({
          message,
        });
      }
    } catch (error) {
      // 支付API调用异常埋点
      tryCatchTracker(error, {
        scene: 'payment_api_call',
        payType: selectedPayment,
        orderNo: orderId,
        orderAmount,
        goodsName: goodsName ?? '',
      });

      Toast({
        message: String(error),
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取支付方式图标
  const getPaymentIcon = (payType: string) => {
    switch (payType) {
      case 'ALIPAY':
        return (
          <div className="w-[24px] h-[24px] bg-alipay rounded-[4px] flex items-center justify-center">
            <span className="text-white text-[12px] font-bold">支</span>
          </div>
        );
      case 'WECHAT':
        return (
          <div className="w-[24px] h-[24px] bg-wechat rounded-[4px] flex items-center justify-center">
            <span className="text-white text-[12px] font-bold">微</span>
          </div>
        );
      case 'UNIONPAY':
        return (
          <div className="w-[24px] h-[24px] bg-unionpay rounded-[4px] flex items-center justify-center">
            <span className="text-white text-[12px] font-bold">云</span>
          </div>
        );
      default:
        return (
          <div className="w-[24px] h-[24px] bg-[#999999] rounded-[4px] flex items-center justify-center">
            <span className="text-white text-[12px] font-bold">付</span>
          </div>
        );
    }
  };

  // iframe错误处理 - 兜底跳转
  const handleIframeFallback = (gatewayUrl: string) => {
    // iframe错误埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'payment_iframe_fallback',
        orderNo: orderId,
        gatewayUrl,
        platform: 'android_web',
      },
    });
    setShowIframe(false);
    setIframeUrl('');
    setIframeLoading(false);
    window.location.replace(gatewayUrl);
  };

  // iframe加载处理
  const handleIframeLoad = () => {
    setIframeLoading(false);
  };

  // iframe错误处理
  const handleIframeError = () => {
    setIframeLoading(false);
    handleIframeFallback(iframeUrl);
  };

  // 监听iframe加载超时兜底
  useEffect(() => {
    if (showIframe && iframeUrl) {
      setIframeLoading(true);
      const timeout = setTimeout(() => {
        if (showIframe && iframeUrl) {
          handleIframeFallback(iframeUrl);
        }
      }, 30000); // 8秒超时兜底
      return () => clearTimeout(timeout);
    }
  }, [showIframe, iframeUrl]);

  if (methodsLoading) {
    return (
      <ActionSheet
        visible={visible}
        title="选择支付方式"
        showCancel={false}
        onClose={onClose}
      >
        <div className="flex items-center justify-center py-8">
          <Loading size="24px" />
          <span className="ml-2 text-text-tertiary text-[14px]">
            加载支付方式中...
          </span>
        </div>
      </ActionSheet>
    );
  }

  return (
    <>
      {/* 支付方式选择弹框 - 当显示支付确认弹框或iframe时隐藏 */}
      <ActionSheet
        visible={visible && !showPaymentConfirm && !showIframe}
        showCancel={false}
        onClose={onClose}
      >
        <div className="px-[12px] pb-[24px] pt-[12px]">
          {/* 右上角X按钮 */}
          <button
            className="absolute top-[1px] font-normal text-[30px] right-[22px] flex items-center justify-center text-[#33333E] focus:outline-none z-10"
            onClick={onClose}
            aria-label="关闭"
          >
            <i className="iconfont icon-a-Property1chachaquxiaoguanbi !text-[15px] text-[#000] mt-[10px]" />
          </button>

          {/* 标题 */}
          <div className="text-center mb-[21px]">
            <h3 className="text-text-primary text-[16px] font-bold">
              选择支付方式
            </h3>
            <p className="text-text-tertiary text-[12px] mt-[4px]">
              请在
              <span className="text-warning">
                {formatCountdown(secondsLeft)}
              </span>
              内完成支付
            </p>
          </div>
          {/* 支付方式列表 */}
          <div className="space-y-[12px] mb-[24px]">
            {paymentMethods.map(method => (
              <div
                key={method.payType}
                className="flex bg-[#F9F9F9] items-center justify-between px-[15px] h-[52px] rounded-[20px] cursor-pointer transition-all duration-200"
                onClick={() => handlePaymentMethodSelect(method.payType)}
              >
                <div className="flex items-center">
                  <div className=" mr-[8px] flex items-center justify-center">
                    {method.payIcon ? (
                      <img
                        src={method.payIcon}
                        alt={method.payName}
                        className="w-[20px] h-[20px]"
                      />
                    ) : (
                      getPaymentIcon(method.payType)
                    )}
                  </div>
                  <div className="text-left">
                    <div className="text-text-primary text-[14px] font-medium">
                      {method.payName}
                    </div>
                  </div>
                </div>
                <div className="w-[18px] h-[18px] border-[1px] border-primary rounded-full flex items-center justify-center transition-all duration-200">
                  {selectedPayment === method.payType ? (
                    <i className="iconfont icon-a-Property1gouxuanxuanzhong !text-[20px] text-primary" />
                  ) : null}
                </div>
              </div>
            ))}
          </div>

          {/* 立即支付按钮 */}
          <button
            className="w-full h-[50px] bg-btn-primary-bg text-btn-primary-text text-[16px] font-bold rounded-[60px] disabled:opacity-50 hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200 flex items-center justify-center"
            onClick={handlePayNow}
            disabled={loading || !selectedPayment}
          >
            {loading ? (
              <>
                <Loading size="16px" className="mr-2" />
                处理中...
              </>
            ) : (
              <>
                立即支付 <span className="ml-[10px]">￥{orderAmount}</span>
              </>
            )}
          </button>
        </div>
      </ActionSheet>

      {/* iframe支付页面 */}
      {showIframe && (
        <div className="fixed inset-0 z-[9999] bg-white">
          {/* 顶部导航栏 */}
          <div className="flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200">
            <button
              onClick={() => {
                setShowIframe(false);
                setIframeUrl('');
                setHasRedirectedToThirdParty(false);
              }}
              className="flex items-center text-[#33333E]"
            >
              <i className="iconfont icon-a-Property1fanhui !text-[20px] mr-2" />
              <span className="text-[16px]">返回</span>
            </button>
            <span className="text-[16px] font-medium">微信支付</span>
            <div className="w-8" />
          </div>

          {/* iframe容器 - 不可见但能正常跳转 */}
          <iframe
            src={iframeUrl}
            className="absolute left-[-9999px] w-[1px] h-[1px] opacity-0 pointer-events-none"
            title="微信支付"
            allow="payment"
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
          />

          {/* iframe loading效果 */}
          {iframeLoading && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]">
              <div className="bg-white rounded-lg p-6 flex flex-col items-center">
                <Loading size="32px" />
              </div>
            </div>
          )}
        </div>
      )}

      {/* 支付确认Dialog弹框 */}
      <Dialog
        visible={showPaymentConfirm}
        title="您是否已经支付"
        maskClosable
        onCancel={handlePaymentConfirmClose}
        footer={
          <div className="w-full flex items-center justify-between">
            {/* 未支付按钮 */}
            <button
              className="w-full h-[50px] mr-[8px] bg-[#F7F7F7] text-[#11111E] text-[16px] rounded-[60px] transition-all duration-200 hover:bg-[#EEEEEE] active:bg-[#E5E5E5]"
              onClick={handlePaymentConfirmAction}
            >
              未支付
            </button>

            {/* 已支付按钮 */}
            <button
              className="w-full h-[50px] bg-[#E8DEC1] text-[#11111E] text-[16px] rounded-[60px] transition-all duration-200 hover:bg-[#DDD4B8] active:bg-[#D2C7A5]"
              onClick={handlePaymentConfirmAction}
            >
              已支付
            </button>
          </div>
        }
      />

      {/* 全屏导航loading */}
      {showNavigationLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg p-6 flex flex-col items-center">
            <Loading size="32px" />
            <span className="mt-3 text-[#11111E] text-[16px]">跳转中...</span>
          </div>
        </div>
      )}
    </>
  );
};
