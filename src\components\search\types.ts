/** 搜索组件相关的TypeScript接口定义 */
/** 搜索参数接口 - 除了pageSize和pageIndex，其他都是可选的 */
export interface SearchParams {
  /** 搜索关键词 */
  keyword?: string;
  /** 城市ID */
  cityId?: string;
  /** 城市名称 */
  cityName?: string;
  /** 入住日期 */
  arrivalDate?: string;
  /** 离店日期 */
  departureDate?: string;
}

/** 搜索组件Props接口 */
export interface SearchComponentProps {
  /** 是否显示搜索组件 */
  visible: boolean;
  /** 关闭搜索组件回调 */
  onClose: () => void;
  /** 初始城市数据（可选，如果不提供会自动获取） */
  initialCityData?: Search.IGetCityInfoRes | null;
  /** 搜索参数（可选） */
  searchParams?: SearchParams;
  /** 城市选择回调 - 返回选中的城市数据 */
  onCitySelect?: (_city: Search.ICityInfo) => void;
  /** 搜索结果选择回调 - 返回选中的搜索结果数据 */
  onSearchResultSelect?: (_result: Search.ISearchResultItem) => void;
  /** 是否显示城市选择区域（默认true） */
  showCitySection?: boolean;
  /** 是否启用搜索功能（默认true） */
  enableSearch?: boolean;
  /** 初始搜索关键词 */
  initialKeyword?: string;
  /** 是否直接进入搜索模式（默认false） */
  directSearch?: boolean;
  /** 搜索框占位符文本 */
  placeholder?: string;
  /** ActionSheet标题 */
  title?: string;
  /** 当前城市信息（可选） */
  currentCity: Pick<
    IGeoInfo,
    'address' | 'cityName' | 'cityId' | 'districtName'
  > & {
    /** 是否是用户定位 */
    isUserLocation: boolean;
  };
  LocationComp: React.FC;
}

/** 搜索头部组件Props接口 */
export interface SearchHeaderProps {
  /** 搜索关键词 */
  keyword: string;
  /** 搜索关键词变化回调 */
  onKeywordChange: (_keyword: string) => void;
  /** 搜索回调 */
  onSearch: (_keyword: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否禁用搜索 */
  disabled?: boolean;
  /** 关闭回调 */
  onClose?: () => void;
}

/** 城市选择组件Props接口 */
export interface CitySectionProps {
  /** 城市数据 */
  cityData: Search.IGetCityInfoRes;
  /** 城市选择回调 */
  onCitySelect: (_city: Search.ICityInfo) => void;
  /** 当前城市信息（可选） */
  currentCity: {
    cityId: string;
    cityName: string;
    address?: string;
    districtName?: string;
    isUserLocation: boolean;
  };
  LocationComp: React.FC;
  onClose?: () => void;
  onHotelHistoryClick?: (_item: Search.ISearchResultItem) => void;
}

/** 搜索结果组件Props接口 */
export interface HotelSectionProps {
  /** 搜索结果 */
  searchResults: Search.ISearchResultItem[];
  /** 搜索参数 */
  searchParams: SearchParams;
  /** 当前搜索关键词 */
  keyword?: string;
  /** 加载状态 */
  loading?: boolean;
  /** 加载更多状态 */
  loadingMore?: boolean;
  /** 是否还有更多数据 */
  hasMore?: boolean;
  /** 加载更多回调 */
  onLoadMore?: () => Promise<void>;
  /** 搜索结果选择回调 */
  onResultSelect?: (_result: Search.ISearchResultItem) => void;
}
