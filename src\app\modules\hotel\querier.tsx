'use client';
import { useCallback, useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';

import dayjs from 'dayjs';
import { Button } from 'react-vant';

import DateDisplay from '@/components/dateDisplay';
import { HotelDatePicker } from '@/components/datePicker';
import SearchComponent from '@/components/search';
import { useHotel } from '@/context';
import { useLocation } from '@/hooks/useLocation/index';

import { tracker } from '../../../utils/track';

const HotelQuerier = () => {
  const {
    hotelDates: dates,
    keyword,
    cityInfo,
    updateHotelCityInfo,
    updateKeyword,
    updateHotelDates,
  } = useHotel();
  const [showDatePicker, setShowPicker] = useState(false);
  const router = useRouter();
  const handleQueryClick = useCallback(() => {
    router.push('/hotel/hotelList');
  }, [router]);

  const { getLocation, geoInfo, LocationComponent, loading } = useLocation({});

  const handleLocationClick = useCallback(async () => {
    await getLocation();

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_location_click',
      },
    });
  }, []);

  const handleDateChange = useCallback((dateList: string[]) => {
    updateHotelDates(dateList as [string, string]);
  }, []);

  const handleShowPicker = useCallback(() => {
    setShowPicker(true);

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_user_show_datepicker',
      },
    });
  }, []);

  const handleClosePicker = useCallback(() => {
    setShowPicker(false);

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_user_close_datepicker',
      },
    });
  }, []);

  const totalDay = useMemo(
    () => dayjs(dates[1]).diff(dayjs(dates[0]), 'day'),
    [dates]
  );

  // 城市选择
  /** 判断搜索是否携带cityid */
  const [isIncludeCityId, setIncludeCityId] = useState(true);
  const [showSearch, setShowSearch] = useState(false);
  /** 唤起城市选择 */
  const handleOpenCityPicker = useCallback((flag: boolean) => {
    setIncludeCityId(flag);
    setShowSearch(true);

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_click_city',
        open: flag,
      },
    });
  }, []);
  /** 关闭搜索组件 */
  const handleCloseSearch = useCallback(() => {
    setShowSearch(false);

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_click_close_search_comp',
      },
    });
  }, []);
  /** 城市修改 */
  const handleCityChange = useCallback((cityData: Search.ICityInfo) => {
    updateHotelCityInfo({
      cityId: cityData.cityId,
      cityName: cityData.cityName,
    });

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_user_update_city_data',
        ...cityData,
      },
    });
  }, []);
  /** 关键字搜索 */
  const handleResultSelect = useCallback(
    (data: Search.ISearchResultItem) => {
      if (cityInfo.cityId !== data.cityId) {
        updateHotelCityInfo({
          cityId: data.cityId,
          cityName: data.cityName,
        });
      }
      updateKeyword(data.itemName);
      if (data.type === 5) {
        // 选中酒店跳转酒店列表页
        router.push('/hotel/hotelList');
      }

      tracker({
        eventType: 'click',
        extra: {
          action: 'home_hotel_user_select_result',
          ...data,
        },
      });
    },
    [cityInfo]
  );

  /** 清除搜索关键字 */
  const handleClearKeyword = useCallback(() => {
    updateKeyword('');

    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_user_clear_keyword',
      },
    });
  }, []);
  return (
    <>
      <div className="location-and-keyword mb-[12px] flex items-center">
        <div className="w-[calc(50%-16px)] flex items-center justify-between h-[24px] shrink-0">
          <span
            className="leading-[24px] text-[18px] font-bold flex-1 overflow-hidden whitespace-nowrap text-ellipsis"
            onClick={() => handleOpenCityPicker(false)}
          >
            {loading ? '正在定位' : cityInfo.cityName}
          </span>
          <i
            className="iconfont icon-a-Property1dingweidangqianweizhi p-[5px]"
            onClick={handleLocationClick}
          />
        </div>
        <span className="border-l border-[#F0F0F0] h-[16px] mx-[15px]" />
        <div className="w-[calc(50%-16px)] flex items-center shrink-0">
          <div
            className="text-[14px] leading-[20px] flex-1 overflow-hidden whitespace-nowrap text-ellipsis"
            onClick={() => handleOpenCityPicker(true)}
          >
            {keyword ? (
              keyword
            ) : (
              <span className="text-[#CCCCCE] pl-[28px]">位置/品牌/酒店</span>
            )}
          </div>
          {keyword && (
            <div
              className="flex items-center justify-center bg-[#F3F3F3] h-[16px] w-[16px] rounded-[50%]"
              onClick={handleClearKeyword}
            >
              <i
                className="iconfont icon-a-Property1chachaquxiaoguanbi text-[8px]"
                style={{ transform: 'scale(50%)' }}
              />
            </div>
          )}
        </div>
      </div>

      <div className="py-[12px] flex items-center justify-between border-t border-b border-[#F3F3F3]">
        <span onClick={handleShowPicker}>
          <DateDisplay date={dates[0]} weekdayPosition="suffix" />
        </span>
        <div className="relative" onClick={handleShowPicker}>
          <div className="absolute w-[71px] h-0 border-t border-[#CCCCCE] top-0 bottom-0 z-0 m-auto ml-[50%] -translate-x-[50%]" />
          <div className="text-[10px] px-[8px] py-[3px] leading-[14px] font-bold bg-white relative border border-[#CCCCCE] rounded-[10px]">
            共{totalDay}晚
          </div>
        </div>
        <span onClick={handleShowPicker}>
          <DateDisplay date={dates[1]} />
        </span>
      </div>

      <div className="mt-[16px]">
        <Button
          className="search-trigger w-full !bg-btn-primary-bg !h-[56px] color-[#11111E] !text-[18px] !font-bold"
          round
          onClick={handleQueryClick}
        >
          查询
        </Button>
      </div>

      <HotelDatePicker
        visible={showDatePicker}
        value={dates}
        onSelect={handleDateChange}
        onClose={handleClosePicker}
      />

      <SearchComponent
        visible={showSearch}
        onClose={handleCloseSearch}
        searchParams={{
          keyword,
          ...(isIncludeCityId ? cityInfo : {}),
          arrivalDate: dates[0],
          departureDate: dates[1],
        }}
        currentCity={{
          cityId: geoInfo.cityId,
          cityName: geoInfo.cityName,
          districtName: geoInfo.districtName,
          address: geoInfo.address,
          isUserLocation: Boolean(geoInfo.lastUpdatedTime),
        }}
        LocationComp={LocationComponent}
        onCitySelect={handleCityChange}
        onSearchResultSelect={handleResultSelect}
      />
    </>
  );
};

export default HotelQuerier;
