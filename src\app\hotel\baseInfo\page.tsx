import { headers } from 'next/headers';

import { getHotelBaseInfo } from '@/api/baseInfo';
import ServerNavbar from '@/components/navbar/ServerNavbar';
import {
  getServerSafeAreaHeight,
  getServerToken,
  isInDreameApp,
} from '@/server/utils';
import { Native } from '@/utils';

import ClientHotelBaseInfo from './ClientHotelBaseInfo';
import TopTabBar from './TopTabBar';

// 异常页面
const errorPage = () => (
  <div className="w-screen min-h-screen flex flex-col items-center justify-center ">
    <img
      src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/Empty.png"
      alt=""
      style={{ width: '60%', height: 'auto' }}
    />
    <div className="text-xl font-bold text-gray-500 mt-4">暂无数据</div>
  </div>
);

export default async function HotelBaseInfoPage({
  searchParams,
}: {
  searchParams: Promise<API.HotelBaseInfo.IHotelBaseInfoPageProps>;
}) {
  const { hotelId } = await searchParams;

  if (!hotelId) return errorPage();

  const isDreameApp = await isInDreameApp();
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';
  const isOwnApp = Native.isOwnApp(userAgent);
  const token = await getServerToken();

  // 获取服务端安全区域高度
  const safeAreaHeightPx = await getServerSafeAreaHeight();

  // 计算navbar总高度（基础高度 + 安全区域高度）
  const navbarTotalHeight = 56 + safeAreaHeightPx;

  try {
    const result = await getHotelBaseInfo({ hotelId, token });
    const { code, data } = result;

    if (code !== 200) return errorPage();

    // 服务端构建navItems
    const { facilityModel, detailPolicyInfo } = data.data;
    const navItems = [
      { id: facilityModel.morFacList[0].anchorId, title: '设施服务' },
      ...detailPolicyInfo.map(item => ({
        id: item.anchorId,
        title: item.firstTitle.title,
      })),
    ];

    return (
      <div className="min-h-screen bg-[#f3f3f3]">
        {/* 导航栏 */}
        <ServerNavbar
          title="酒店信息"
          left={{
            showBack: true,
          }}
          fixed
        />
        {/* backUrl: `/hotel/hotelDetails?hotelId=${hotelId}`, */}

        {/* TopTabBar - 固定定位 */}
        <div
          className="fixed left-0 right-0 z-40 bg-white"
          style={{ top: `${isOwnApp ? 0 : navbarTotalHeight}px` }}
        >
          <TopTabBar items={navItems} />
        </div>

        {/* 内容区域 */}
        <ClientHotelBaseInfo
          data={data.data}
          navbarTotalHeight={navbarTotalHeight}
          isDreameApp={isDreameApp ?? false}
        />
      </div>
    );
  } catch (error) {
    console.error('请求失败:', error);
    return errorPage();
  }
}
