import { useRouter } from 'next/navigation';

interface BaseInfoProps {
  hotelData: API.HotelDetails.IHotelDetailsFetchResponseData;
}
const boxClass = 'bg-white rounded-2xl px-3 py-4 flex flex-col gap-2';
const title1Class = 'text-[16px] font-[500] text-black';
const title2Class = 'text-[14px] font-[500] text-black mt-1';
const contentClass = 'text-[14px]  text-text-secondary mt-1';
const allPolicyClass =
  'text-[14px] text-primary mt-1 text-center relative after:content-[""]  after:inline-block after:w-[8px] after:mb-[2px] after:h-[8px] after:border-r-[1px] after:border-b-[1px] after:border-primary after:transform after:rotate-[-45deg] after:align-middle bg-transparent border-none cursor-pointer';

// 解析URL中anchor参数的值
const getAnchorFromUrl = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get('anchor');
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

export default function BaseInfo({ hotelData }: BaseInfoProps) {
  const router = useRouter();

  // 获取设施列表，最多展示8个
  const facilities = hotelData.hotelStaticInfo.facilities?.list?.slice(0, 8);

  const toHotelBaseInfo = (jumpUrl?: string) => () => {
    if (jumpUrl) {
      const anchor = getAnchorFromUrl(jumpUrl);
      // 使用解析到的anchor值进行跳转
      router.push(
        `/hotel/baseInfo?hotelId=${hotelData.hotelStaticInfo.hotelId}&anchor=${anchor}`
      );
    } else {
      // 默认跳转
      router.push(
        `/hotel/baseInfo?hotelId=${hotelData.hotelStaticInfo.hotelId}`
      );
    }
  };

  return (
    <div className="flex flex-col gap-2 mx-2 mb-10">
      <div className={boxClass}>
        <div className={title1Class}>
          {hotelData.hotelStaticInfo.policy.title}
        </div>
        {hotelData.hotelStaticInfo.policy.policys.map((item, index) => (
          <div key={index}>
            <div className={title2Class}>{item.policyTitle}</div>
            <div className={contentClass}>{item.description}</div>
          </div>
        ))}
        <button
          className={allPolicyClass}
          onClick={toHotelBaseInfo(hotelData.hotelStaticInfo.policy.jumpUrl)}
        >
          全部酒店政策
        </button>
      </div>
      <div className={boxClass}>
        <div className={title1Class}>
          {hotelData.hotelStaticInfo.facilities?.title}
        </div>
        <div className="grid grid-cols-2 gap-x-4 gap-y-3 mt-2">
          {facilities?.map((item, index) => (
            <div key={index} className="flex items-center gap-2 mb-4">
              <img
                src={item.iconUrl}
                alt={item.title}
                className="flex-shrink-0 w-[20px] h-[20px]"
              />
              <div className={title2Class}>{item.title}</div>
            </div>
          ))}
        </div>
        <button
          className={allPolicyClass}
          onClick={toHotelBaseInfo(
            hotelData.hotelStaticInfo.facilities?.jumpUrl
          )}
        >
          全部酒店设施
        </button>
      </div>
      {/* <div className={boxClass}>
        <div className={title1Class}>
          {hotelData.hotelStaticInfo.priceDescription.title}
        </div>
        <div className={contentClass}>
          {hotelData.hotelStaticInfo.priceDescription.description}
        </div>
        <button className={allPolicyClass} onClick={toHotelBaseInfo()}>
          查看全部
        </button>
      </div> */}
    </div>
  );
}
