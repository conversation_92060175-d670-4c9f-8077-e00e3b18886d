import { useCallback } from 'react';

import { useRouter } from 'next/navigation';

import { useApp } from '@/context';
import { MixBridge } from '@/utils/mixbridge';

const BaseInfo = ({
  hotelData,
}: {
  hotelData: API.HotelDetails.IHotelDetailsFetchResponseData;
}) => {
  const { hotelStaticInfo } = hotelData;
  const router = useRouter();
  const { geoInfo } = useApp();

  // 唤起三方地图导航
  const handleOpenMap = useCallback(() => {
    const { lat, lon, hotelName } = hotelStaticInfo;
    MixBridge.openMap({
      name: hotelName,
      location: {
        latitude: geoInfo.lat,
        longitude: geoInfo.lng,
      },
      position: {
        latitude: lat,
        longitude: lon,
      },
    });
  }, [hotelStaticInfo.lat, hotelStaticInfo.lon]);
  // 处理设施/服务点击
  const handleFacilityClick = () => {
    router.push(
      `/hotel/baseInfo?hotelId=${hotelStaticInfo.hotelId}&anchor=more-facilities`
    );
  };

  return (
    <div className="p-2 bg-white gap-2 flex flex-col">
      {/* 酒店名称 */}
      {hotelStaticInfo.hotelName && (
        <h1
          className="font-medium text-xl px-2 truncate"
          style={{
            fontWeight: 500,
            fontSize: '20px',
            color: '#11111E',
          }}
        >
          {hotelStaticInfo.hotelName}
        </h1>
      )}

      {/* 主要信息区域 - 左右布局 */}
      {/* 左侧：装修时间和标签（上下布局）+ 亮点设施 */}
      <div className="flex items-center space-x-6 flex-1 px-2">
        {/* 装修时间和标签 - 上下结构，占据剩余宽度 */}
        <div className="flex flex-col space-y-1 flex-1 min-w-0">
          {/* 装修时间 */}
          {hotelStaticInfo.decorateDateDesc && (
            <div
              className="font-medium"
              style={{
                fontWeight: 500,
                fontSize: '12px',
                color: '#11111E',
              }}
            >
              {hotelStaticInfo.decorateDateDesc}
            </div>
          )}

          {/* 酒店标签 - 单行溢出隐藏，限制显示数量 */}
          {hotelStaticInfo.hotelTag && hotelStaticInfo.hotelTag.length > 0 && (
            <div className="flex gap-1 overflow-hidden whitespace-nowrap">
              {hotelStaticInfo.hotelTag.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="flex-shrink-0"
                  style={{
                    fontWeight: 400,
                    fontSize: '12px',
                    color: '#66666E',
                  }}
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* 设施/服务 - 固定宽度，可点击 */}
        {hotelStaticInfo.hotFacilityUrl && (
          <button
            className="cursor-pointer flex-shrink-0 flex items-center border-0 bg-transparent p-0"
            onClick={handleFacilityClick}
            style={{
              fontWeight: 400,
              fontSize: '12px',
              color: '#33333E',
            }}
          >
            <span>设施/服务</span>
            <img
              src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotelDetails/rightIcon.png"
              alt="查看更多"
              className="flex-shrink-0 w-[12px] h-[12px]"
            />
          </button>
        )}
      </div>

      {/* 位置信息区域 - 左右布局，添加背景和圆角 */}
      <div
        className="flex items-center justify-between p-1"
        style={{
          background: '#F7F7F7',
          borderRadius: '16px',
        }}
      >
        {/* 左侧：距离和位置信息（上下布局） */}
        <div className="flex flex-col space-y-1 flex-1 ml-1">
          {/* 距离信息 */}
          {(hotelStaticInfo.distance || hotelStaticInfo.businessZoneName) && (
            <div
              style={{
                fontWeight: 500,
                fontSize: '12px',
                color: '#11111E',
              }}
            >
              {hotelStaticInfo.distance && hotelStaticInfo.distance}
              {hotelStaticInfo.distance &&
                hotelStaticInfo.businessZoneName &&
                ' · '}
              {hotelStaticInfo.businessZoneName &&
                `近${hotelStaticInfo.businessZoneName}`}
            </div>
          )}

          {/* 酒店地址 */}
          {hotelStaticInfo.hotelAddress && (
            <div
              style={{
                fontWeight: 400,
                fontSize: '12px',
                color: '#11111E',
              }}
            >
              {hotelStaticInfo.hotelAddress}
            </div>
          )}
        </div>

        {/* 右侧：地图图标 */}
        <div className="ml-4" onClick={handleOpenMap}>
          <img
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotelDetails/mapIcon.png"
            alt="地图"
            className="object-contain w-[40px] h-[40px]"
          />
        </div>
      </div>
    </div>
  );
};

export default BaseInfo;
