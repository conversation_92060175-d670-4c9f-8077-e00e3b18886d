'use client';
import { useCallback } from 'react';

import { useRouter } from 'next/navigation';

import { staticBaseUrl } from '@/config';

import { tracker } from '../utils/track';

const ClientGoMMyOrder = () => {
  const router = useRouter();
  const handleClick = useCallback(() => {
    tracker({
      eventType: 'click',
      extra: {
        action: 'home_click_to_order',
      },
    });
    router.push('/order');
  }, []);

  return (
    <div className="flex flex-col items-center" onClick={handleClick}>
      <img
        src={`${staticBaseUrl}/home/<USER>
        alt=""
        className="w-[32px] h-[32px]"
      />
      <span className="text-[10px] text-white leading-[14px] mt-[4px]">
        我的订单
      </span>
    </div>
  );
};
export default ClientGoMMyOrder;
