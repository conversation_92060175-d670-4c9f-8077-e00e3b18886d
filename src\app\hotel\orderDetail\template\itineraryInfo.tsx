'use client';
import ActionSheet from '@/components/actionSheet';
import { getMonthAndDay, maskPhone } from '@/utils/tools';
import React, { useState } from 'react';

interface ItineraryInfoProps {
  [key: string]: any;
}
const ItineraryInfo: React.FC<ItineraryInfoProps> = data => {
  const {
    checkInDate,
    checkOutDate,
    checkInDesc,
    checkOutDesc,
    nightCount,
    roomTypeName,
    saleRoomDesc,
    packageInfo,
    contactName,
    contactPhone,
  } = data;

  const checkInObj = getMonthAndDay(checkInDate);
  const checkOutObj = getMonthAndDay(checkOutDate);

  const { foodDetails = [], enjoyDetails = [] } = packageInfo || {};

  const [visible, setVisible] = useState(false);
  const [currentDetails, setCurrentDetails] =
    useState<API.HotelOrderDetailsApi.PackageItem | null>(null);
  const handleDetilPop = (details: API.HotelOrderDetailsApi.PackageItem) => {
    setCurrentDetails(details);
    setVisible(true);
  };

  return (
    <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[12px]">
      <p className="text-[16px] text-[#11111E] font-medium">您的行程安排</p>
      <div className="flex items-start mt-[12px]">
        <div>
          <p className="text-[12px] text-[#11111E] leading-[18px]">
            <span className="text-[20px] font-bold leading-[24px]">
              {checkInObj.month}
            </span>
            <span>月</span>
            <span className="text-[20px] font-bold leading-[24px]">
              {checkInObj.day}
            </span>
            <span>日</span>
            <span className="ml-[2px]">周日</span>
          </p>
          <p className="mt-[4px] text-[12px] text-[#99999E] leading-[18px]">
            {checkInDesc}
          </p>
        </div>

        <div className="flex items-center mx-[12px] mt-[6px]">
          <div className="w-[11px] h-[1px] bg-[#33333E]" />
          <div className="px-[6px] h-[16px] bg-[#33333E] rounded-[20px] text-[10px] text-[#FFFFFF] leading-[16px]">
            共{nightCount}晚
          </div>
          <div className="w-[11px] h-[1px] bg-[#33333E]" />
        </div>

        <div>
          <p className="text-[12px] text-[#11111E] leading-[18px]">
            <span className="text-[20px] font-bold leading-[24px]">
              {checkOutObj.month}
            </span>
            <span>月</span>
            <span className="text-[20px] font-bold leading-[24px]">
              {checkOutObj.day}
            </span>
            <span>日</span>
            <span className="ml-[2px]">周二</span>
          </p>
          <p className="mt-[4px] text-[12px] text-[#99999E] leading-[18px]">
            {checkOutDesc}
          </p>
        </div>
      </div>

      <div className="mt-[12px]">
        <p className="text-[14px] text-[#11111E] font-medium">{roomTypeName}</p>
        <p className="mt-[4px] text-[12px] text-[#11111E]">{saleRoomDesc}</p>
      </div>

      {foodDetails?.length > 0 || enjoyDetails?.length > 0 ? (
        <div className="mt-[12px]">
          <p className="text-[14px] text-[#11111E] font-medium">套餐信息</p>

          {/* 餐 */}
          {foodDetails?.map((item: any, index: number) => (
            <div
              className="flex items-center justify-between px-[8px] py-[12px] bg-[#F7F7F7] rounded-[8px] mt-[4px]"
              key={index}
              onClick={() => handleDetilPop(item)}
            >
              <p className="text-[12px] text-[#11111E]">{item.packageName}</p>
              <img
                className="w-[12px] h-[12px]"
                src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/right_black_icon.png"
                alt=""
              />
            </div>
          ))}

          {/* 享 */}
          {enjoyDetails?.map((item: any, index: number) => (
            <div
              className="flex items-center justify-between px-[8px] py-[12px] bg-[#F7F7F7] rounded-[8px] mt-[4px]"
              key={index}
              onClick={() => handleDetilPop(item)}
            >
              <p className="text-[12px] text-[#11111E]">{item.packageName}</p>
              <img
                className="w-[12px] h-[12px]"
                src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/right_black_icon.png"
                alt=""
              />
            </div>
          ))}

          {currentDetails ? (
            <ActionSheet
              visible={visible}
              title={currentDetails?.title}
              showCancel={false}
              onClose={() => setVisible(false)}
            >
              <div className="px-[16px] pb-[30px] max-h-[60vh] overflow-y-auto [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
                {currentDetails.imageUrl ? (
                  <div className="w-[100%] h-[237px] rounded-[24px] overflow-hidden bg-[#F00]">
                    <img
                      className="w-[100%] h-[237px]"
                      src={currentDetails.imageUrl}
                      alt=""
                    />
                  </div>
                ) : null}
                {currentDetails?.packageName && (
                  <p className="mt-[12px] text-[14px] text-[#11111E] font-medium">
                    {currentDetails.packageName}
                  </p>
                )}
                {currentDetails.packageDetails ? (
                  <div className="mt-[16px]">
                    {currentDetails.packageDetails?.map(
                      (item: any, index: number) => (
                        <div className="mt-[8px] flex" key={index}>
                          <p className="text-[14px] text-[#66666E] w-[89px]">
                            {item.title}
                          </p>
                          <p className="text-[14px] #000000 flex-1">
                            {item.description}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                ) : null}
              </div>
            </ActionSheet>
          ) : null}
        </div>
      ) : null}

      <div className="mt-[12px]">
        {contactName ? (
          <div className="flex">
            <p className="text-[14px] text-[#66666E] w-[81px]">入住人</p>
            <p className="text-[14px] text-[#000000] flex-1">{contactName}</p>
          </div>
        ) : null}
        {contactPhone ? (
          <div className="flex mt-[8px]">
            <p className="text-[14px] text-[#66666E] w-[81px]">联系手机</p>
            <p className="text-[14px] text-[#000000] flex-1">
              {maskPhone(contactPhone)}
            </p>
          </div>
        ) : null}

        {/* 本期无  */}
        {/* <div className="flex mt-[8px]">
          <p className="text-[14px] text-[#66666E] w-[81px]">预计到店</p>
          <p className="text-[14px] text-[#000000] flex-1">5月1日14:00之前，如需提前到店请联系商家(不影响酒店留房)</p>
        </div> */}
      </div>
    </div>
  );
};

export default ItineraryInfo;
