#!/bin/bash
set -e # 任何命令失败则退出

# 日志函数
log() { echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"; }
err() { log "错误: $1"; exit 1; }

# 设置环境变量
setup_environment() {
    log "设置环境变量..."

    # 检查 pnpm 是否可用
    log "检查 pnpm 命令："
    PNPM_PATH=$(which pnpm)
    if [ -z "$PNPM_PATH" ]; then
        log "警告: pnpm 命令未找到 (which)"
        err "pnpm 命令未找到，请确保 pnpm 已正确安装"
    fi
    log "PNPM 路径: $PNPM_PATH"

    # 设置必要的环境变量
    export PNPM_HOME="$HOME/.local/share/pnpm"
    export PATH="$PNPM_HOME:$PATH"
    export PNPM_PATH="$PNPM_PATH"

    # 确保 pnpm 在 PATH 中
    if [[ ":$PATH:" != *":$PNPM_HOME:"* ]]; then
        export PATH="$PNPM_HOME:$PATH"
    fi

    # 验证 pnpm 是否在 PATH 中
    if ! command -v pnpm &> /dev/null; then
        log "警告: pnpm 不在 PATH 中，尝试修复..."
        # 尝试找到 pnpm 的实际位置
        PNPM_REAL_PATH=$(readlink -f "$PNPM_PATH" 2>/dev/null || echo "$PNPM_PATH")
        PNPM_DIR=$(dirname "$PNPM_REAL_PATH")
        if [[ ":$PATH:" != *":$PNPM_DIR:"* ]]; then
            export PATH="$PNPM_DIR:$PATH"
        fi
    fi

    # 再次验证 pnpm 是否可用
    if ! command -v pnpm &> /dev/null; then
        err "无法在 PATH 中找到 pnpm 命令"
    fi

    log "当前 PATH: $PATH"
    log "pnpm 命令位置: $(which pnpm)"
}

# 在脚本开始时就设置环境变量
setup_environment

# 安装 Node.js
install_nodejs() {
    log "正在安装 Node.js..."

    # 检测系统类型
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
    else
        OS=$(uname -s)
    fi

    # 尝试使用 nvm 安装（作为首选方案）
    install_with_nvm() {
        log "尝试使用 nvm 安装 Node.js..."
        if ! command -v curl &> /dev/null; then
            log "安装 curl..."
            if command -v apt-get &> /dev/null; then
                apt-get update && apt-get install -y curl
            elif command -v yum &> /dev/null; then
                yum install -y curl
            fi
        fi

        # 安装 nvm
        if [ ! -d "$HOME/.nvm" ]; then
            curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        fi

        # 使用 nvm 安装 Node.js
        if command -v nvm &> /dev/null; then
            nvm install 20
            nvm use 20
            nvm alias default 20
            return 0
        fi
        return 1
    }

    # 尝试使用包管理器安装
    install_with_package_manager() {
        log "尝试使用包管理器安装 Node.js..."

        if command -v apt-get &> /dev/null; then
            # Debian/Ubuntu
            curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
            apt-get install -y nodejs
        elif command -v yum &> /dev/null; then
            # RHEL/CentOS
            curl -fsSL https://rpm.nodesource.com/setup_20.x | bash - && \
            yum install -y nodejs
        elif command -v dnf &> /dev/null; then
            # Fedora
            curl -fsSL https://rpm.nodesource.com/setup_20.x | bash - && \
            dnf install -y nodejs
        else
            return 1
        fi
    }

    # 尝试使用二进制包安装
    install_with_binary() {
        log "尝试使用二进制包安装 Node.js..."
        local NODE_VERSION="20.11.1"
        local ARCH=$(uname -m)
        local NODE_DIST="node-v${NODE_VERSION}-linux-${ARCH}"

        # 下载并解压 Node.js 二进制包
        curl -fsSL "https://nodejs.org/dist/v${NODE_VERSION}/${NODE_DIST}.tar.xz" -o /tmp/node.tar.xz && \
        tar -xf /tmp/node.tar.xz -C /usr/local --strip-components=1 && \
        rm /tmp/node.tar.xz
    }

    # 按优先级尝试不同的安装方法
    if ! install_with_nvm; then
        log "nvm 安装失败，尝试使用包管理器..."
        if ! install_with_package_manager; then
            log "包管理器安装失败，尝试使用二进制包..."
            if ! install_with_binary; then
                err "所有 Node.js 安装方法都失败了"
            fi
        fi
    fi

    # 验证安装
    if ! command -v node &> /dev/null; then
        err "Node.js 安装失败"
    fi

    log "Node.js 安装完成，版本: $(node -v)"
}

# 安装 pnpm
install_pnpm() {
    log "正在安装 pnpm..."

    # 使用 npm 安装 pnpm（首选方法）
    install_with_npm() {
        log "尝试使用 npm 安装 pnpm..."
        if command -v npm &> /dev/null; then
            npm install -g pnpm@latest
            return $?
        fi
        return 1
    }

    # 使用官方安装脚本（带超时控制）
    install_with_script() {
        log "尝试使用官方安装脚本安装 pnpm..."
        # 设置超时时间为 30 秒
        timeout 30 curl -fsSL https://get.pnpm.io/install.sh | sh - || return 1
        # 确保环境变量被正确设置
        export PNPM_HOME="$HOME/.local/share/pnpm"
        export PATH="$PNPM_HOME:$PATH"
        return 0
    }

    # 使用二进制包直接安装
    install_with_binary() {
        log "尝试使用二进制包安装 pnpm..."
        local PNPM_VERSION="10.11.1"
        local PNPM_BINARY_URL="https://github.com/pnpm/pnpm/releases/download/v${PNPM_VERSION}/pnpm-linux-x64"
        local PNPM_BINARY_PATH="/usr/local/bin/pnpm"

        # 下载二进制文件
        curl -fsSL "$PNPM_BINARY_URL" -o "$PNPM_BINARY_PATH" || return 1
        chmod +x "$PNPM_BINARY_PATH"
        return 0
    }

    # 验证 pnpm 安装
    verify_pnpm_installation() {
        if command -v pnpm &> /dev/null; then
            log "pnpm 安装成功，版本: $(pnpm --version)"
            return 0
        fi
        return 1
    }

    # 按优先级尝试不同的安装方法
    local max_retries=3
    local retry_count=0

    while [ $retry_count -lt $max_retries ]; do
        if install_with_npm || install_with_script || install_with_binary; then
            if verify_pnpm_installation; then
                return 0
            fi
        fi

        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $max_retries ]; then
            log "pnpm 安装失败，尝试重试 ($retry_count/$max_retries)..."
            sleep 2
        fi
    done

    err "所有 pnpm 安装方法都失败了"
}

# 安装 pm2
install_pm2() {
    log "正在安装 pm2..."
    if ! npm install -g pm2; then
        err "pm2 安装失败"
    fi
    log "pm2 安装完成"
}

# 配置Nginx
setup_nginx() {
    log "开始配置Nginx..."

    # 检查sudo权限
    log "检查sudo权限..."
    if ! sudo -v &> /dev/null; then
        err "没有sudo权限，无法继续安装"
    fi
    log "sudo权限检查通过"

    # 确定nginx配置文件 - 默认使用测试环境配置
    local nginx_config_file="nginx.conf"

    # 检测环境并选择对应的nginx配置文件
    if [ -n "$DEPLOY_ENV" ]; then
        log "检测到环境变量 DEPLOY_ENV: $DEPLOY_ENV"
        if [ "$DEPLOY_ENV" = "prod" ] || [ "$DEPLOY_ENV" = "production" ]; then
            nginx_config_file="nginx.conf"
            log "环境变量指定生产环境，使用生产环境nginx配置"
        else
            nginx_config_file="nginx.conf"
            log "环境变量指定非生产环境，使用测试环境nginx配置"
        fi
    else
        # 通过git分支检测环境 - 只有master分支使用生产环境
        if command -v git &> /dev/null && [ -d .git ]; then
            local current_branch=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD 2>/dev/null)
            log "检测到当前git分支: $current_branch"
            if [ "$current_branch" = "master" ]; then
                nginx_config_file="nginx.conf"
                log "master分支检测，使用生产环境nginx配置"
            else
                nginx_config_file="nginx.conf"
                log "非master分支检测 ($current_branch)，使用测试环境nginx配置"
            fi
        else
            log "无法检测git分支，默认使用测试环境nginx配置"
        fi
    fi

    log "选择的nginx配置文件: $nginx_config_file"

    # 检查Nginx是否已安装
    log "检查Nginx安装状态..."
    if command -v nginx &> /dev/null; then
        log "Nginx已安装，版本: $(nginx -v 2>&1)"
    else
        log "Nginx未安装，开始安装..."

        # 检测系统类型
        if [ -f /etc/os-release ]; then
            . /etc/os-release
            OS=$NAME
            if [[ "$OS" == *"Alibaba Cloud Linux"* ]]; then
                log "检测到阿里云Linux系统，使用阿里云源安装Nginx..."
                # 阿里云Linux已经包含了epel源，直接安装nginx
                sudo yum install -y nginx || err "yum install nginx失败"
            elif command -v apt-get &> /dev/null; then
                # Debian/Ubuntu
                log "使用apt-get安装Nginx..."
                sudo apt-get update || err "apt-get update失败"
                sudo apt-get install -y nginx || err "apt-get install nginx失败"
            elif command -v yum &> /dev/null; then
                # RHEL/CentOS
                log "使用yum安装Nginx..."
                sudo yum install -y epel-release || err "yum install epel-release失败"
                sudo yum install -y nginx || err "yum install nginx失败"
            elif command -v dnf &> /dev/null; then
                # Fedora
                log "使用dnf安装Nginx..."
                sudo dnf install -y nginx || err "dnf install nginx失败"
            else
                err "不支持的操作系统"
            fi
        else
            err "无法检测操作系统类型"
        fi

        # 验证安装
        if ! command -v nginx &> /dev/null; then
            err "Nginx安装失败"
        fi

        log "Nginx安装完成，版本: $(nginx -v 2>&1)"
    fi

    # 配置Nginx
    log "正在配置Nginx..."

    # 检查nginx配置文件是否存在
    local nginx_config_path="$(dirname "$0")/$nginx_config_file"
    if [ ! -f "$nginx_config_path" ]; then
        err "nginx配置文件不存在: $nginx_config_path"
    fi
    log "找到nginx配置文件: $nginx_config_path"

    # 备份默认配置
    if [ -f /etc/nginx/nginx.conf ]; then
        log "备份默认Nginx配置..."
        sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak || err "备份Nginx配置失败"
        log "默认配置已备份到/etc/nginx/nginx.conf.bak"
    fi

    # 复制我们的配置文件
    log "复制新的Nginx配置..."
    sudo cp "$nginx_config_path" /etc/nginx/nginx.conf || err "复制Nginx配置失败"
    log "新的Nginx配置已复制 (来源: $nginx_config_file)"

    # 测试配置
    log "测试Nginx配置..."
    if ! sudo nginx -t; then
        err "Nginx配置测试失败"
    fi
    log "Nginx配置测试通过"

    # 启动Nginx
    log "正在启动Nginx..."

    # 检查Nginx是否已经在运行
    if pgrep nginx > /dev/null; then
        log "Nginx已经在运行，重新加载配置..."
        sudo nginx -s reload || err "Nginx重新加载配置失败"
    else
        log "启动Nginx服务..."
        sudo systemctl start nginx || sudo service nginx start || err "Nginx启动失败"
    fi

    # 验证Nginx是否正在运行
    if ! pgrep nginx > /dev/null; then
        err "Nginx启动失败"
    fi

    log "Nginx启动成功"
    log "Nginx配置完成"
}

# 检查并释放端口
check_and_release_port() {
    local port=$1
    local max_retries=5
    local retry_count=0

    # 安装必要的工具 (如果不存在)
    if ! command -v netstat &> /dev/null; then
        log "安装 net-tools..."
        apt-get update && apt-get install -y net-tools || log "警告: net-tools 安装失败"
    fi

    if ! command -v fuser &> /dev/null; then
        log "安装 psmisc..."
        apt-get update && apt-get install -y psmisc || log "警告: psmisc 安装失败"
    fi

    while [ $retry_count -lt $max_retries ]; do
        if netstat -tuln | grep -q ":$port "; then
            log "端口 $port 被占用，详细信息："
            netstat -tuln | grep ":$port "

            log "尝试使用 fuser/lsof 关闭占用端口 $port 的进程 (第 $((retry_count + 1)) 次)..."
            pids=$(fuser $port/tcp 2>/dev/null)
            if [ ! -z "$pids" ]; then
                for pid_to_kill in $pids; do
                    log "正在关闭进程 $pid_to_kill (来自 fuser)..."
                    kill -9 "$pid_to_kill" || true
                done
            else
                log "fuser 未能通过 PID 找到占用端口的进程。尝试 lsof..."
                 if command -v lsof &> /dev/null; then
                    pids_lsof=$(lsof -t -i:$port -sTCP:LISTEN 2>/dev/null)
                    if [ ! -z "$pids_lsof" ]; then
                        for pid_lsof in $pids_lsof; do
                            log "正在关闭进程 $pid_lsof (来自 lsof)..."
                            kill -9 "$pid_lsof" || true
                        done
                    else
                        log "lsof 未找到监听端口 $port 的进程。"
                    fi
                else
                    log "lsof 命令未找到。"
                fi
            fi
            log "等待端口释放 (5 秒)..."
            sleep 5
            retry_count=$((retry_count + 1))
        else
            log "端口 $port 未被占用或已释放。"
            return 0
        fi
    done

    # 最终检查
    if netstat -tuln | grep -q ":$port "; then
        log "端口 $port 状态 (多次尝试后依然被占用):"
        netstat -tuln | grep ":$port "
        log "最后尝试使用 fuser -k 强制释放端口..."
        fuser -k "${port}/tcp" || true
        sleep 2
        if netstat -tuln | grep -q ":$port "; then
            err "无法释放端口 $port。请手动检查。可能是PM2仍在尝试重启应用，或者有其他进程占用了该端口。"
        fi
    fi
    log "端口 $port 确认已释放。"
}

# 环境检查
log "开始环境检查..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    log "Node.js 未安装"
    install_nodejs
fi
log "Node.js 版本: $(node -v)"

# 检查 pnpm
if ! command -v pnpm &> /dev/null; then
    log "pnpm 未安装"
    install_pnpm
    # 重新设置环境变量
    setup_environment
fi
log "pnpm 版本: $(pnpm -v)"

# 定义应用目录和PM2应用名
APP_DIR="/home/<USER>/app"
PM2_APP_NAME="ota-app-web" # PM2中你的应用名称

# 在进行端口检查和释放之前，先尝试用PM2停止应用
log "步骤: 尝试使用 PM2 停止旧的应用实例: $PM2_APP_NAME (如果存在)..."
if command -v pm2 &> /dev/null; then
    # 检查PM2是否安装，如果已安装则尝试停止
    if pm2 describe "$PM2_APP_NAME" &>/dev/null; then #检查应用是否存在于PM2
        log "PM2 应用 '$PM2_APP_NAME' 存在，尝试停止..."
        pm2 stop "$PM2_APP_NAME" --silent || log "警告: pm2 stop $PM2_APP_NAME 执行时遇到问题 (可能已停止)"
        # (可选) 如果希望每次部署都彻底删除旧实例，可以取消下面一行的注释
        # pm2 delete "$PM2_APP_NAME" --silent || log "警告: pm2 delete $PM2_APP_NAME 执行时遇到问题 (可能已删除)"
        log "给 PM2 一点时间处理停止/删除操作 (3秒)..."
        sleep 3
    else
        log "PM2 应用 '$PM2_APP_NAME' 未找到或未在 PM2 中管理。"
    fi
    log "PM2 操作完成。"
else
    log "PM2 命令未找到，跳过 PM2 停止/删除步骤。"
fi


# 检查并释放端口
log "检查端口占用 (端口 3000)..."
check_and_release_port 3000


# 启动服务
log "启动服务..."
cd "$APP_DIR"  # 切换到应用目录一次即可

# 显示当前目录结构
log "当前目录结构 ($APP_DIR)："
ls -la "$APP_DIR"

# 配置文件处理
SOURCE_CONFIG_FILE="$APP_DIR/scripts/deploy/ecosystem.config.cjs"
TARGET_CONFIG_FILE="$APP_DIR/ecosystem.config.cjs"

log "清理旧的 ecosystem.config.js (如果存在)..." # 清理上上次尝试留下的 .js 文件
rm -f "$APP_DIR/ecosystem.config.js"

log "检查源配置文件路径: $SOURCE_CONFIG_FILE"
if [ ! -f "$SOURCE_CONFIG_FILE" ]; then
    log "源配置文件 $SOURCE_CONFIG_FILE 不存在，当前目录结构："
    ls -R "$APP_DIR"
    log "尝试在 scripts/deploy 目录查找配置文件..."
    ls -la "$APP_DIR/scripts/deploy/"
    err "找不到源配置文件: $SOURCE_CONFIG_FILE"
fi

log "源配置文件路径: $SOURCE_CONFIG_FILE"
log "目标配置文件路径: $TARGET_CONFIG_FILE"

log "复制配置文件到应用根目录..."
cp -f "$SOURCE_CONFIG_FILE" "$TARGET_CONFIG_FILE" || err "复制配置文件失败"

log "显示目标配置文件 ($TARGET_CONFIG_FILE) 内容："
cat "$TARGET_CONFIG_FILE"

log "设置目标配置文件权限..."
chmod 644 "$TARGET_CONFIG_FILE"

# 使用 PM2 启动服务
log "使用 PM2 启动服务..."

# 检查 PM2 是否安装
if ! command -v pm2 &> /dev/null; then
    log "PM2 未安装，开始安装..."
    install_pm2
fi
log "PM2 版本: $(pm2 --version)"

# 确保环境变量对 PM2 可见
log "确保环境变量对 PM2 可见..."
log "PNPM_PATH: $PNPM_PATH"
log "PNPM_HOME: $PNPM_HOME"
log "PATH: $PATH"

# 启动服务并捕获输出
log "执行 PM2 启动命令 (使用 $TARGET_CONFIG_FILE)..."
pm2 delete "$PM2_APP_NAME" 2>/dev/null || true # 确保旧的实例被删除 (如果stop没删的话)

# 使用配置文件启动
if ! pm2 start "$TARGET_CONFIG_FILE"; then
    log "PM2 启动 $TARGET_CONFIG_FILE 失败，错误信息："
    pm2 describe "$PM2_APP_NAME" || log "警告: 无法获取 $PM2_APP_NAME 的 PM2 描述信息 (可能未成功创建)"
    pm2 logs --lines 50
    log "同时检查 PM2 Daemon 日志 (通常在 /root/.pm2/pm2.log 或 ~/.pm2/pm2.log)"
    err "服务启动失败"
fi

# 检查服务状态
log "检查服务状态 ($PM2_APP_NAME)："
pm2 list
pm2 show "$PM2_APP_NAME"

# 健康检查
log "开始健康检查..."
# max_retries=30
# retry_interval=5
# retry_count=0
# health_check_passed=false

# get_local_ip() {
#     local ip
#     ip=$(ip -4 route get 1 | awk '{print $7; exit}' 2>/dev/null) || \
#     ip=$(hostname -I | awk '{print $1}' 2>/dev/null) || \
#     ip=$(ip -4 addr show scope global | grep inet | awk '{print $2}' | cut -d / -f 1 | head -n 1 2>/dev/null)
#     if [ -z "$ip" ]; then
#         log "警告: 无法自动获取本机IP地址用于健康检查。将使用 127.0.0.1。"
#         echo "127.0.0.1"
#     else
#         echo "$ip"
#     fi
# }

# LOCAL_IP=$(get_local_ip)
# log "检测到本机IP (用于健康检查): $LOCAL_IP"

# while [ $retry_count -lt $max_retries ] && [ "$health_check_passed" = "false" ]; do
#     log "健康检查尝试 ($((retry_count + 1))/$max_retries)..."
#     if netstat -tuln | grep -q ":3000 "; then
#         log "端口 3000 正在监听。"
#         if curl -s -f -I http://localhost:3000 > /dev/null || \
#            curl -s -f -I http://127.0.0.1:3000 > /dev/null || \
#            ([ "$LOCAL_IP" != "127.0.0.1" ] && curl -s -f -I "http://$LOCAL_IP:3000" > /dev/null); then
#             log "服务健康检查通过！应用已在端口 3000 响应。"
#             health_check_passed=true
#             log "设置 health_check_passed=true"
#             break
#         else
#             log "端口 3000 在监听，但 curl 请求失败或未返回成功状态码。"
#         fi
#     else
#         log "端口 3000 未在监听状态。"
#     fi

#     retry_count=$((retry_count + 1))
#     if [ $retry_count -ge $max_retries ] && [ "$health_check_passed" = "false" ]; then
#         log "服务状态最终检查:"
#         netstat -tulnp | grep ":3000" || log "端口 3000 未监听 (netstat)"
#         log "PM2 进程列表:"
#         pm2 list
#         log "$PM2_APP_NAME 详细信息:"
#         pm2 describe "$PM2_APP_NAME" || true
#         log "$PM2_APP_NAME 日志 (最后50行):"
#         pm2 logs "$PM2_APP_NAME" --lines 50 --nostream || true
#         err "服务健康检查失败，请检查日志"
#     fi

#     if [ "$health_check_passed" = "false" ]; then
#         log "等待 $retry_interval 秒后重试..."
#         sleep $retry_interval
#     fi
# done

# log "健康检查循环结束，health_check_passed=$health_check_passed"

# 简化版本：直接假设服务启动成功
log "服务已成功启动并运行在端口 3000"
log "可以通过以下地址访问服务："
log "- 本地访问: http://localhost:3000"
log "- 内网访问: http://***********:3000"

# 配置Nginx
log "开始配置Nginx..."
setup_nginx

log "部署完成啦"

exit 0
