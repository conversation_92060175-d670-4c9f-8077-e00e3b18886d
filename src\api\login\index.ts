import { request } from '@/utils/request';

/**
 * 发送验证码
 *
 * @param params 发送验证码请求参数
 * @returns Promise<App.Service.IResponse<API.Login.ISendCodeResponse>>
 */
export const sendCode = (
  params: API.Login.ISendCodeRequest
): Promise<App.Service.IResponse<API.Login.ISendCodeResponse>> =>
  request<API.Login.ISendCodeResponse>('/hotel/user/auth/sendCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
  });

/**
 * 用户登录
 *
 * @param params 登录请求参数
 * @returns Promise<App.Service.IResponse<API.Login.ILoginResponse>>
 */
export const login = (
  params: API.Login.ILoginRequest
): Promise<App.Service.IResponse<API.Login.ILoginResponse>> =>
  request<API.Login.ILoginResponse>('/hotel/user/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
  });
