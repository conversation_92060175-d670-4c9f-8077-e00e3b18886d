'use client';

import { useRouter } from 'next/navigation';

import NavBar from '@/components/navbar';
import { getImageUrl } from '@/utils';

interface HotelDetailsNavBarProps {
  /** 是否已滚动状态 */
  isScrolled: boolean;
  /** 酒店数据 */
  hotelData: API.HotelDetails.IHotelDetailsFetchResponseData;
  /** 自定义返回处理函数，不提供则使用router.back() */
  onBack?: () => void;
}

export const HotelDetailsNavBar: React.FC<HotelDetailsNavBarProps> = ({
  isScrolled,
  hotelData,
  onBack,
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.back();
    }
  };

  return (
    <NavBar
      title={isScrolled ? hotelData.hotelStaticInfo.hotelName : undefined}
      fixed
      autoSpacer={false}
      backgroundColor={isScrolled ? 'white' : 'transparent'}
      className={`transition-all duration-300 ${isScrolled ? 'shadow-sm' : ''}`}
      style={{
        color: isScrolled ? 'var(--text-primary)' : 'var(--bg-card)',
      }}
      left={{
        children: (
          <button
            onClick={handleBack}
            className="flex items-center justify-center w-[18px] h-[18px]"
            aria-label="返回"
          >
            <img
              src={getImageUrl('arrow.png')}
              alt="返回"
              className="w-full h-full"
              style={{
                filter: isScrolled
                  ? 'brightness(0)'
                  : 'brightness(0) invert(1)',
              }}
            />
          </button>
        ),
      }}
      center={{
        className: 'mx-10',
      }}
      right={
        hotelData?.hotelStaticInfo?.telNo
          ? {
              children: (
                <a
                  href={`tel:${hotelData.hotelStaticInfo.telNo}`}
                  className="flex items-center justify-center w-5 h-5"
                  aria-label="拨打电话"
                >
                  <img
                    src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotelDetails/phone-icon.png"
                    alt="phone"
                    className="w-full h-full"
                    style={{
                      filter: isScrolled
                        ? 'brightness(0)'
                        : 'brightness(0) invert(1)',
                    }}
                  />
                </a>
              ),
            }
          : undefined
      }
    />
  );
};

export default HotelDetailsNavBar;
