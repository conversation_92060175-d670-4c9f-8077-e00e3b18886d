import type { FC, ReactNode } from 'react';

export interface DialogProps {
  visible: boolean;
  title?: string;
  children?: ReactNode;
  footer?: ReactNode;
  confirmText?: string;
  cancelText?: string;
  hiddenConfirmBtm?: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
  maskClosable?: boolean;
}

const Dialog: FC<DialogProps> = ({
  visible,
  title,
  children,
  confirmText = '确认',
  cancelText = '取消',
  hiddenConfirmBtm = false,
  onConfirm,
  onCancel,
  maskClosable = true,
  footer,
}) => {
  if (!visible) return null;

  const handleMaskClick = () => {
    if (maskClosable && onCancel) onCancel();
  };

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* 遮罩层 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-40"
        onClick={handleMaskClick}
      />
      {/* 弹框内容 */}
      <div className="relative bg-white rounded-xl shadow-lg w-[80vw] max-w-[340px] p-6 z-10">
        {title && (
          <div className="text-lg font-bold mb-3 text-center">{title}</div>
        )}
        <div className="text-base text-center mb-6">{children}</div>
        <div className="flex gap-4">
          {footer ? (
            footer
          ) : (
            <>
              <button
                className="flex-1 py-2  text-text-primary"
                onClick={onCancel}
              >
                {cancelText}
              </button>
              {hiddenConfirmBtm ? null : (
                <button
                  className="flex-1 py-2 rounded-lg text-primary-light"
                  onClick={onConfirm}
                >
                  {confirmText}
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dialog;
