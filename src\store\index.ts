// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import hotelOrderReducer from './hotel/order';
import hotelOrderDettailReducer from './hotel/orderDetail';

// 创建一个新的 store 实例（避免服务端数据污染）
export const makeStore = () => {
  return configureStore({
    reducer: {
      hotelOrder: hotelOrderReducer,
      hotelOrderDetail: hotelOrderDettailReducer,
    },
  });
};

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];
