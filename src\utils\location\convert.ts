/**
 * WGS-84 转 GCJ-02 坐标转换函数 精度问题： 1、算法转换与官方API转换可能存在微小差异（通常<5米）
 * 2、对精度要求高的场景建议使用官方API； 收费问题： LBS基础服务商用服务日配额900万次，超出额外收费；
 * 收费详情https://lbs.amap.com/upgrade#price
 */

// 类型定义
interface Coordinate {
  lng: number;
  lat: number;
}

// 常量定义
const EARTH_RADIUS = 6378245.0; // 地球长半轴
// eslint-disable-next-line no-loss-of-precision
const EARTH_ECCENTRICITY = 0.00669342162296594323; // 地球偏心率平方
const { PI } = Math;

/** 判断坐标是否在中国境外 */
const isOutOfChina = (lng: number, lat: number): boolean =>
  lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;

/** 纬度转换计算 */
const transformLat = (x: number, y: number): number => {
  let result =
    -100.0 +
    2.0 * x +
    3.0 * y +
    0.2 * y * y +
    0.1 * x * y +
    0.2 * Math.sqrt(Math.abs(x));
  result +=
    ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) /
    3.0;
  result +=
    ((20.0 * Math.sin(y * PI) + 40.0 * Math.sin((y / 3.0) * PI)) * 2.0) / 3.0;
  result +=
    ((160.0 * Math.sin((y / 12.0) * PI) + 320 * Math.sin((y * PI) / 30.0)) *
      2.0) /
    3.0;
  return result;
};

/** 经度转换计算 */
const transformLng = (x: number, y: number): number => {
  let result =
    300.0 +
    x +
    2.0 * y +
    0.1 * x * x +
    0.1 * x * y +
    0.1 * Math.sqrt(Math.abs(x));
  result +=
    ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) /
    3.0;
  result +=
    ((20.0 * Math.sin(x * PI) + 40.0 * Math.sin((x / 3.0) * PI)) * 2.0) / 3.0;
  result +=
    ((150.0 * Math.sin((x / 12.0) * PI) + 300.0 * Math.sin((x / 30.0) * PI)) *
      2.0) /
    3.0;
  return result;
};

/**
 * WGS-84 转 GCJ-02 坐标转换函数 TIPS: 精度问题： 1、算法转换与官方API转换可能存在微小差异（通常<5米）
 * 2、对精度要求高的场景建议使用官方API；收费问题：LBS基础服务商用服务日配额900万次，超出额外收费；收费详情https://lbs.amap.com/upgrade#price
 */
export const wgs84ToGcj02 = (wgsCoord: Coordinate): Coordinate => {
  const { lng, lat } = wgsCoord;

  // 中国境外坐标不转换
  if (isOutOfChina(lng, lat)) {
    return { lng, lat };
  }

  // 计算转换量
  let dLat = transformLat(lng - 105.0, lat - 35.0);
  let dLng = transformLng(lng - 105.0, lat - 35.0);

  // 弧度计算
  const radLat = (lat / 180.0) * PI;
  const magic = Math.sin(radLat);
  const sqrtMagic = Math.sqrt(1 - EARTH_ECCENTRICITY * magic * magic);

  // 最终转换
  dLat =
    (dLat * 180.0) /
    (((EARTH_RADIUS * (1 - EARTH_ECCENTRICITY)) /
      (sqrtMagic * sqrtMagic * sqrtMagic)) *
      PI);
  dLng = (dLng * 180.0) / ((EARTH_RADIUS / sqrtMagic) * Math.cos(radLat) * PI);

  return {
    lng: lng + dLng,
    lat: lat + dLat,
  };
};

/** 批量转换坐标 */
export const batchConvertCoordinates = (
  coordinates: Coordinate[]
): Coordinate[] => coordinates.map(wgs84ToGcj02);
