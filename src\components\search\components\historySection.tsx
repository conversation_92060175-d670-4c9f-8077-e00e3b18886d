import React from 'react';

interface HistorySectionProps {
  history: Search.ISearchResultItem[];
  onClear: () => void;
  onItemClick: (_item: Search.ISearchResultItem) => void;
  className?: string;
}

const HistorySection: React.FC<HistorySectionProps> = ({
  history,
  onClear,
  onItemClick,
  className = '',
}) => {
  if (!history || history.length === 0) return null;
  return (
    <div
      id="history-cities"
      className={`px-4 py-4 bg-white ${className}`}
      style={{ maxWidth: '400px', margin: '0 auto' }}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-[#11111E]" style={{ fontSize: '14px' }}>
          历史记录
        </h3>
        <button onClick={onClear} className="text-xs text-[#66666E]">
          清空
        </button>
      </div>
      <div
        className="grid gap-1"
        style={{
          gridTemplateColumns: 'repeat(4, 1fr)',
          maxWidth: 400,
          margin: '0 auto',
        }}
      >
        {history.map(item => {
          // 判断文案长度，如果超过4个字符，使用3列布局
          const isLongText = item.itemName.length > 4;
          return (
            <button
              key={`${item.type}-${item.itemName}-${item.cityId}`}
              onClick={() => onItemClick(item)}
              className="bg-bg-light text-text-primary text-center hover:bg-bg-gray transition-colors rounded-[12px] h-[36px] overflow-hidden text-ellipsis whitespace-nowrap"
              style={{
                fontSize: '14px',
                borderRadius: '12px',
                minWidth: 0,
                gridColumn: isLongText ? 'span 2' : 'span 1', // 长文案占2列，相当于3列布局
                paddingLeft: isLongText ? '10px' : undefined, // 长文案增加左侧内边距
              }}
              title={item.itemName}
            >
              {item.itemName}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default HistorySection;
