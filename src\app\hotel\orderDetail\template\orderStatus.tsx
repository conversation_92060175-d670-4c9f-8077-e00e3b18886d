'use client';
import React, { useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useDispatch } from 'react-redux';

import { PaymentSheet, Toast } from '@/components';
import type { AppDispatch } from '@/store';
import { cancelOrder, getOrderDetail } from '@/store/hotel/orderDetail';

interface OrderStatusProps {
  [key: string]: any;
}

// PENDING(0, "待支付"),
// HAD_PAID(1, "已支付,待确认"),
// CONFIRM(2, "已确认,待入住"),
// CHECK_IN(3, "已入住"),
// CHECK_OUT(4, "已离店"),
// CANCELLED(5, "已取消"),
// CLOSED(6, "酒店拒绝订单"),
// FINISHED(7, "已完成");

const icon = [
  {
    url: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/top_check_dm_icon.png',
    class: 'w-[32px] h-[32px] mr-[10px]',
  },
  {
    url: 'https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/orderDetail/top_load_dm_icon.png',
    class: 'w-[32px] h-[32px] mr-[10px] animate-spin',
  },
];

const OrderStatus: React.FC<OrderStatusProps> = data => {
  const {
    showOrderStatus,
    orderStatus,
    payStatusGoodDesc,
    orderNo,
    hotelName,
    totalAmount,
    lastPayTime,
  } = data;
  const icon_obj = icon[orderStatus === 1 ? 1 : 0];
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const handlePaymentConfirm = async () => {
    setVisible(false);
    router.refresh();
  };

  const [visible, setVisible] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const pay = () => {
    setVisible(true);
  };

  const cancel = async () => {
    if (cancelLoading) return;
    try {
      setCancelLoading(true);
      const res = await dispatch(cancelOrder({ orderNo }));
      if (res?.payload === 200) {
        dispatch(getOrderDetail({ orderNo })).catch(error => {
          console.error('Failed to fetch order details:', error);
        });
      } else {
        Toast(res?.payload || '取消订单失败');
      }
      setCancelLoading(false);
    } catch (error) {
      console.error('Failed to fetch order details:', error);
      setCancelLoading(false);
      Toast('取消失败');
      throw error;
    }
  };

  const goInvoice = () => {
    window.location.href = `/invoice?orderNo=${orderNo}`;
  };

  const handlePaymentSuccess = () => {
    setVisible(false);
  };

  // 30分钟倒计时（从29:59开始）
  const DEFAULT_COUNTDOWN_SECONDS = 30 * 60 - 1; // 1799秒
  // lastPayTime优先，单位毫秒转秒，异常值视为0
  const initialSeconds =
    lastPayTime && typeof lastPayTime === 'number' && lastPayTime > 0
      ? Math.floor(lastPayTime / 1000)
      : lastPayTime === 0
        ? 0
        : DEFAULT_COUNTDOWN_SECONDS;
  const [secondsLeft, setSecondsLeft] = useState(initialSeconds);
  const timerDetailRef = useRef<NodeJS.Timeout | null>(null);
  // 格式化为"mm分ss秒"
  const formatCountdown = (secs: number) => {
    const mm = String(Math.floor(secs / 60)).padStart(2, '0');
    const ss = String(secs % 60).padStart(2, '0');
    return `${mm}分${ss}秒`;
  };

  // 倒计时逻辑
  useEffect(() => {
    setSecondsLeft(initialSeconds);
    if (initialSeconds <= 0) {
      dispatch(getOrderDetail({ orderNo })).catch(error => {
        console.error('Failed to fetch order details:', error);
      });
      return;
    }
    timerDetailRef.current = setInterval(() => {
      setSecondsLeft(prev => {
        if (prev <= 1) {
          if (timerDetailRef.current) {
            clearInterval(timerDetailRef.current);
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    return () => {
      if (timerDetailRef.current) clearInterval(timerDetailRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialSeconds]);

  // 监听secondsLeft归零，自动关闭弹框并刷新页面
  useEffect(() => {
    if (secondsLeft === 0) {
      dispatch(getOrderDetail({ orderNo })).catch(error => {
        console.error('Failed to fetch order details:', error);
      });
      return;
    }
  }, [secondsLeft]);

  return (
    <div className="pt-[16px] pb-[12px] pl-[16px] pr-[12px] bg-bg-card">
      <div className="flex">
        <img className={icon_obj.class} src={icon_obj.url} alt="" />
        <div>
          <p className="text-[24px] text-text-primary font-medium leading-[34px]">
            {showOrderStatus}
          </p>
          {orderStatus === 0 && secondsLeft > 0 ? (
            <p className="text-[12px] text-text-primary">
              请在
              <span className="text-[#FB7A1E]">
                {formatCountdown(secondsLeft)}
              </span>
              内完成支付
            </p>
          ) : payStatusGoodDesc ? (
            <p className="text-[12px] text-text-primary">{payStatusGoodDesc}</p>
          ) : null}
        </div>
      </div>
      <div className="flex items-center justify-end mt-[12px]">
        {/* 开发票 */}
        {orderStatus === 4 ? (
          <div
            className="h-[36px] px-[16px] bg-bg-gray rounded-[60px] leading-[36px] text-[14px] text-text-primary ml-[8px] cursor-pointer hover:bg-bg-light transition-colors duration-200"
            onClick={goInvoice}
          >
            开发票
          </div>
        ) : null}

        {/* 取消订单 */}
        {orderStatus === 0 ||
        orderStatus === 1 ||
        orderStatus === 2 ||
        orderStatus === 6 ? (
          <div
            className={`h-[36px] px-[16px] bg-[#F7F7F7] rounded-[60px] leading-[36px] text-[14px] text-black ml-[8px] ${cancelLoading && 'opacity-30'}`}
            onClick={cancel}
          >
            {cancelLoading ? '取消中···' : '取消订单'}
          </div>
        ) : null}

        {/* 支付 */}
        {orderStatus === 0 ? (
          <div
            className="h-[36px] px-[16px] bg-btn-primary-bg rounded-[60px] leading-[36px] text-[14px] text-btn-primary-text ml-[8px] hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200 cursor-pointer"
            onClick={pay}
          >
            去支付
          </div>
        ) : null}
      </div>
      <PaymentSheet
        visible={visible}
        onClose={() => setVisible(false)}
        lastPayTime={Math.floor(secondsLeft * 1000)}
        orderId={orderNo}
        goodsName={hotelName}
        orderAmount={totalAmount}
        onPaymentSuccess={handlePaymentSuccess}
        onPaymentConfirm={handlePaymentConfirm}
      />
    </div>
  );
};

export default OrderStatus;
