import type { ThemeConfig } from './index';

// 追觅主题配置
const dreameTheme: ThemeConfig = {
  // 主色系
  primary: '#8C6533',
  primaryLight: '#E8DEC1',
  primaryBg: '#F5EDD6',

  // 按钮配置
  button: {
    primary: {
      background: '#E8DEC1',
      text: '#8C6533',
      hover: '#DDD4B8',
      active: '#D2C7A5',
    },
    secondary: {
      background: '#F7F7F7',
      text: '#8C6533',
      hover: '#EEEEEE',
      active: '#E5E5E5',
    },
  },

  // 文字色系
  text: {
    primary: '#11111E',
    secondary: '#33333E',
    tertiary: '#66666E',
    muted: '#99999E',
  },

  // 背景色系
  background: {
    page: '#f9fafb',
    card: '#ffffff',
    gray: '#F7F7F7',
    light: '#F9F9F9',
  },

  // 状态色系
  status: {
    success: '#07C160',
    warning: '#FF7A00',
    error: '#ff6b6b',
    info: '#1677FF',
  },

  // 支付色系
  payment: {
    alipay: '#1677FF',
    wechat: '#07C160',
    unionpay: '#E21918',
  },
};

export default dreameTheme;
