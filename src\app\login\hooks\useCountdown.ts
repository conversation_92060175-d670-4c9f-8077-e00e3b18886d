import { useCallback, useEffect, useRef, useState } from 'react';

/** 倒计时Hook参数 */
interface UseCountdownOptions {
  /** 初始倒计时秒数 */
  initialSeconds?: number;
  /** 倒计时结束回调 */
  onComplete?: () => void;
  /** 是否自动开始 */
  autoStart?: boolean;
}

/** 倒计时Hook返回值 */
interface UseCountdownReturn {
  /** 当前倒计时秒数 */
  countdown: number;
  /** 是否正在倒计时 */
  isRunning: boolean;
  /** 开始倒计时 */
  start: (_seconds?: number) => void;
  /** 停止倒计时 */
  stop: () => void;
  /** 重置倒计时 */
  reset: () => void;
  /** 暂停倒计时 */
  pause: () => void;
  /** 恢复倒计时 */
  resume: () => void;
}

/**
 * 倒计时Hook
 *
 * @param options 倒计时配置选项
 * @returns 倒计时状态和控制方法
 */
export function useCountdown(
  options: UseCountdownOptions = {}
): UseCountdownReturn {
  const { initialSeconds = 59, onComplete, autoStart = false } = options;

  const [countdown, setCountdown] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number>(0);
  const remainingTimeRef = useRef<number>(0);

  /** 清除定时器 */
  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  /** 开始倒计时 */
  const start = useCallback(
    (_seconds?: number) => {
      clearTimer();

      const targetSeconds = _seconds ?? initialSeconds;
      setCountdown(targetSeconds);
      setIsRunning(true);
      setIsPaused(false);
      remainingTimeRef.current = targetSeconds;
      startTimeRef.current = Date.now();

      timerRef.current = setInterval(() => {
        setCountdown(prev => {
          const newValue = prev - 1;
          remainingTimeRef.current = newValue;

          if (newValue <= 0) {
            clearTimer();
            setIsRunning(false);
            onComplete?.();
            return 0;
          }

          return newValue;
        });
      }, 1000);
    },
    [initialSeconds, onComplete, clearTimer]
  );

  /** 停止倒计时 */
  const stop = useCallback(() => {
    clearTimer();
    setCountdown(0);
    setIsRunning(false);
    setIsPaused(false);
    remainingTimeRef.current = 0;
  }, [clearTimer]);

  /** 重置倒计时 */
  const reset = useCallback(() => {
    clearTimer();
    setCountdown(0);
    setIsRunning(false);
    setIsPaused(false);
    remainingTimeRef.current = 0;
  }, [clearTimer]);

  /** 暂停倒计时 */
  const pause = useCallback(() => {
    if (isRunning && !isPaused) {
      clearTimer();
      setIsPaused(true);
    }
  }, [isRunning, isPaused, clearTimer]);

  /** 恢复倒计时 */
  const resume = useCallback(() => {
    if (isRunning && isPaused && remainingTimeRef.current > 0) {
      setIsPaused(false);
      start(remainingTimeRef.current);
    }
  }, [isRunning, isPaused, start]);

  /** 组件卸载时清理定时器 */
  useEffect(() => {
    if (autoStart) {
      start();
    }

    return () => {
      clearTimer();
    };
  }, [autoStart, start, clearTimer]);

  return {
    countdown,
    isRunning: isRunning && !isPaused,
    start,
    stop,
    reset,
    pause,
    resume,
  };
}
