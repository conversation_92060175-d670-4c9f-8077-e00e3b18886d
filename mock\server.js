import fs from 'fs';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

import cors from 'cors';
import express from 'express';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = 4000;
const mockDir = __dirname; // Mock data directory

// 启用CORS, 允许所有来源
app.use(cors());

// 解析JSON请求体
app.use(express.json());

// 读取mock数据
const readMockData = filepath => {
  // 修改参数名为 filepath
  try {
    const data = fs.readFileSync(filepath, 'utf8'); // 直接使用完整路径
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading mock data from ${filepath}:`, error);
    return null;
  }
};

// 递归查找并创建路由
const createRoutes = directory => {
  fs.readdirSync(directory, { withFileTypes: true }).forEach(dirent => {
    const fullPath = path.join(directory, dirent.name);
    if (dirent.isDirectory()) {
      // 如果是目录，递归调用
      createRoutes(fullPath);
    } else if (dirent.isFile() && dirent.name.endsWith('.json')) {
      // 如果是 .json 文件，创建路由
      const relativePath = path.relative(mockDir, fullPath); // 获取相对于 mockDir 的路径
      const routePathWithoutExtension = path.join(
        '/api',
        relativePath.replace(/\.json$/, '')
      ); // 构建路由路径，替换 .json
      const routePath = routePathWithoutExtension.replace(/\\/g, '/'); // 将 Windows 路径分隔符替换为 '/'

      // 创建处理函数
      const handleRequest = (req, res) => {
        console.warn(`Received ${req.method} request for ${routePath}`);

        let mockData = readMockData(fullPath);

        // 特殊处理订单列表的分页和筛选
        if (routePath === '/api/hotel/order/orderList' && mockData) {
          const { pageIndex = 1, pageSize = 10, status = '' } = req.body;

          // 根据状态筛选数据
          let filteredData = mockData;
          if (status !== '' && status !== undefined && status !== null) {
            // 根据状态值筛选订单
            // '0': 待付款, '2': 未出行
            const statusNumber = parseInt(status, 10);
            filteredData = mockData.filter(
              order => order.orderStatus === statusNumber
            );
          }

          // 分页处理
          const startIndex = (pageIndex - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const paginatedData = filteredData.slice(startIndex, endIndex);

          console.warn(
            `分页请求: status=${status}, pageIndex=${pageIndex}, pageSize=${pageSize}, 筛选后${filteredData.length}条, 返回${paginatedData.length}条数据`
          );

          mockData = paginatedData;
        }

        // 特殊处理搜索关键词接口
        if (
          routePath === '/api/hotel/search/keywords/select/v1' &&
          mockData &&
          req.body
        ) {
          const { keyword = '', pageIndex = 1, pageSize = 20 } = req.body;

          console.warn(
            `搜索关键词: ${keyword}, pageIndex=${pageIndex}, pageSize=${pageSize}`
          );

          // 根据关键词筛选数据
          let filteredData = mockData.records || [];
          if (keyword.trim()) {
            filteredData = filteredData.filter(
              item =>
                item.itemName.includes(keyword) ||
                item.typeName.includes(keyword) ||
                item.cityName.includes(keyword) ||
                item.districtName.includes(keyword) ||
                item.businessZoneName.includes(keyword)
            );
          }

          // 分页处理
          const startIndex = (pageIndex - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const paginatedData = filteredData.slice(startIndex, endIndex);

          console.warn(
            `搜索结果: 筛选后${filteredData.length}条, 返回${paginatedData.length}条数据`
          );

          mockData = { records: paginatedData };
        }

        const data = {
          code: 200,
          message: 'Success',
          data: mockData,
          isSuccess: true,
        };

        if (mockData !== null) {
          res.json(data);
        } else {
          res.status(500).json({ error: 'Failed to read mock data' });
        }
      };

      // 同时支持GET和POST请求
      app.post(routePath, handleRequest);
      app.get(routePath, handleRequest);

      console.warn(`Created GET/POST route: ${routePath}`);
    }
  });
};

// 开始创建路由
createRoutes(mockDir);

// 错误处理中间件
app.use((err, req, res) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something broke!' });
});

// 启动服务器
app.listen(port, () => {
  console.warn(`Mock server is running at http://localhost:${port}`);
});
