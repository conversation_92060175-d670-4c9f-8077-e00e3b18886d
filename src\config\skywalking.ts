/** SkyWalking 配置文件 用于阿里云链路追踪服务 */

// 导出初始化函数，在服务器启动时调用
export function initSkyWalking() {
  // 只在服务端运行时环境初始化 SkyWalking
  if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
    try {
      // 检查必要的环境变量，确保不为 null 或空字符串
      const serviceName = process.env.SW_AGENT_NAME;
      const collectorAddress =
        process.env.SW_AGENT_REPORTER_GRPC_BACKEND_SERVICE;
      const authorization = process.env.SW_AGENT_REPORTER_GRPC_AUTHENTICATION;

      if (
        serviceName &&
        serviceName.trim() !== '' &&
        collectorAddress &&
        collectorAddress.trim() !== '' &&
        authorization &&
        authorization.trim() !== ''
      ) {
        const { default: agent } = require('skywalking-backend-js');

        agent.start({
          serviceName: serviceName.trim(),
          collectorAddress: collectorAddress.trim(),
          authorization: authorization.trim(),
        });

        console.log(`[SkyWalking] 已启动链路追踪服务: ${serviceName}`);
      } else {
        console.warn('[SkyWalking] 缺少必要的环境变量，跳过初始化');
      }
    } catch (error) {
      console.error('[SkyWalking] 初始化失败:', error);
    }
  }
}
