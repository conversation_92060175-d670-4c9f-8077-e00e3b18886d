/** Native JSBridge 类型定义 */

// 全局类型声明
declare global {
  interface Window {
    flutter_inappwebview?: {
      callHandler?: (_channel: string, _message: string) => void;
      _callHandler?: (
        _channel: string,
        _callback: any,
        _message: string
      ) => void;
    };
    // 原生回调函数
    onAppMessage?: (_response: NativeCallbackResponse) => void;
    // 新增的回调函数
    onSharedStorage?: (_response: NativeCallbackResponse) => void;
    // 自有壳的 App 接口
    App?: IApp;
  }

  /** 自有壳的 App 接口定义 */
  interface IApp {
    closePage: (_isClose: boolean) => void;
    jumpPage: (_url: string, _customJump?: (_url: string) => void) => void;
    setPageTitle: (_title: string) => void;
    getUserToken: () => string;
    getAppVersion: () => string;
    goBack: () => void;
    isInApp: () => boolean;
  }

  // 定义消息类型
  interface NativeMessage {
    type: string;
    data: any;
    requestId?: string;
  }

  // 定义原生回调响应类型
  interface NativeCallbackResponse {
    type: string;
    data: any;
    requestId: string;
  }

  // 定义原生调用选项
  interface NativeCallOptions {
    /** 超时时间（毫秒） */
    timeout?: number;
  }

  // 定义原生调用结果
  interface NativeCallResult<T = any> {
    /** 是否成功 */
    success: boolean;
    /** 返回的数据 */
    data?: T;
    /** 错误信息 */
    error?: string;
    /** 是否超时 */
    isTimeout?: boolean;
  }

  // 定义位置信息接口
  interface LocationData {
    lat: number;
    lng?: number;
    lon?: number;
  }

  // 定义共享存储操作类型
  type SharedStorageOperation = 'set' | 'get' | 'remove';

  // 定义地图坐标接口
  interface MapCoordinates {
    lat: number;
    lng: number;
  }

  // 定义地图数据接口
  interface MapData {
    wgs84: MapCoordinates;
    gcj02: MapCoordinates;
  }

  // 定义共享存储数据接口
  interface SharedStorageData {
    key: string;
    value?: string;
  }

  // 定义共享存储请求接口
  interface SharedStorageRequest {
    type: SharedStorageOperation;
    data: SharedStorageData;
  }

  // 定义设备平台类型
  type DevicePlatform = 'android' | 'ios' | 'unknown';

  // 定义App壳类型
  type AppShellType = 'dreame' | 'own' | 'browser' | 'unknown';

  // 定义用户设备信息接口
  interface UserDeviceInfo {
    /** 设备平台 */
    platform: DevicePlatform;
    /** 应用版本号 */
    version: string;
    /** 用户ID */
    uid: string;
    /** 设备唯一标识 */
    uuid: string;
    /** 安全区域高度 */
    safeAreaHeight: string;
    /** 是否在App壳内 */
    isInAppWebView: boolean;
    /** 原始UA字符串 */
    originalUA: string;
  }

  // 定义Native对象接口
  interface Native {
    // === 环境检测方法 ===

    /** 检查是否在追觅App壳内 */
    isDreameApp: () => boolean;

    /** 检查是否在自有App壳内 */
    isOwnApp: () => boolean;

    /** 检查是否在任何App壳内 */
    isInNativeApp: () => boolean;

    /** 获取当前App壳类型 */
    getAppShellType: () => AppShellType;

    // === 通用方法（兼容两种壳） ===

    /**
     * 关闭当前页面
     *
     * @param _isClose 是否关闭页面（自有壳参数）
     */
    closePage: (_isClose?: boolean) => void;

    /**
     * 跳转页面
     *
     * @param _url 目标URL
     * @param _customJump 自定义跳转方法（自有壳参数）
     */
    jumpPage: (_url: string, _customJump?: (_url: string) => void) => void;

    /**
     * 设置页面标题
     *
     * @param _title 页面标题
     */
    setPageTitle: (_title: string) => void;

    /** 返回上一页 */
    goBack: () => void;

    /** 获取用户Token */
    getUserToken: () => string;

    // === 追觅专用方法 ===

    /**
     * 获取位置信息
     *
     * @param _options 配置选项
     */
    getLocation: (
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult<LocationData>>;

    /**
     * 获取联系人
     *
     * @param _options 配置选项
     */
    getContact: (_options?: NativeCallOptions) => Promise<NativeCallResult>;

    /**
     * 获取地图信息
     *
     * @param _options 配置选项
     */
    getMap: (_options?: NativeCallOptions) => Promise<NativeCallResult>;

    /**
     * 跳转到地图
     *
     * @param _location 位置坐标
     */
    openMap: (_location: LocationData) => void;

    // === 新增的追觅专用方法 ===

    /**
     * 获取位置信息（带消息）
     *
     * @param _options 配置选项
     */
    getLocationWithMessage: (
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult<LocationData>>;

    /**
     * 获取联系人（带消息）
     *
     * @param _message 请求消息
     * @param _options 配置选项
     */
    getContactWithMessage: (
      _message: string,
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult>;

    /**
     * 跳转到地图
     *
     * @param _mapData 地图数据（包含wgs84和gcj02坐标）
     */
    jumpToMap: (_mapData: MapData) => void;

    /**
     * 设置共享存储
     *
     * @param _key 存储键
     * @param _value 存储值
     * @param _options 配置选项
     */
    setSharedStorage: (
      _key: string,
      _value: string,
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult>;

    /**
     * 获取共享存储
     *
     * @param _key 存储键
     * @param _options 配置选项
     */
    getSharedStorage: (
      _key: string,
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult<string>>;

    /**
     * 删除共享存储
     *
     * @param _key 存储键
     * @param _options 配置选项
     */
    removeSharedStorage: (
      _key: string,
      _options?: NativeCallOptions
    ) => Promise<NativeCallResult>;

    // === UserAgent 解析方法 ===

    /** 解析 UserAgent 获取设备信息 */
    getUserAgent: () => UserDeviceInfo;

    /** 获取设备信息 */
    getDeviceInfo: () => UserDeviceInfo;

    /** 检查是否在App内 */
    getIsInApp: () => boolean;

    /** 获取平台信息 */
    getPlatform: () => string;

    /** 获取应用版本 */
    getAppVersion: () => string;

    /** 获取用户ID */
    getUserId: () => string;

    /** 获取设备UUID */
    getDeviceUUID: () => string;

    /** 获取安全区域高度 */
    getSafeAreaHeight: (_userAgent?: string) => number;

    /**
     * 打开WebView
     *
     * @param _url 要打开的URL
     */
    openWebView: (_url: string) => void;

    /** 关闭当前WebView */
    closeWebView: () => void;

    /**
     * 通用方法：发送自定义消息
     *
     * @param _type 消息类型
     * @param _data 消息数据
     */
    sendCustomMessage: (_type: string, _data?: any) => void;

    /** 获取用户设备信息 解析UserAgent中的设备和用户信息 */
    getUserDeviceInfo: () => UserDeviceInfo;

    /** 判断是否在追觅App壳内 通过检查UserAgent中是否包含_DreameHome_标识 */
    isDreameApp: () => boolean;

    /** 判断是否为iOS平台 */
    isIOS: () => boolean;

    /** 判断是否为Android平台 */
    isAndroid: () => boolean;
  }
}

// 空导出，让 TypeScript 将此文件识别为模块
export {};
