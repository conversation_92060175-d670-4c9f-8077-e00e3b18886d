import { useCallback, useReducer } from 'react';

import { validatePhone } from '../../../utils/regex';

/** 表单字段类型 */
interface FormFields {
  phone: string;
  smsCode: string;
  isAgreed: boolean;
}

/** 表单字段值类型 */
type FormFieldValue = string | boolean;

/** 表单状态类型 */
interface FormState extends FormFields {
  errors: {
    phone?: string;
    smsCode?: string;
    isAgreed?: string;
  };
  isSubmitting: boolean;
}

/** 表单动作类型 */
type FormAction =
  | { type: 'SET_FIELD'; field: keyof FormFields; value: FormFieldValue }
  | { type: 'SET_ERROR'; field: keyof FormFields; error: string }
  | { type: 'CLEAR_ERROR'; field: keyof FormFields }
  | { type: 'SET_SUBMITTING'; isSubmitting: boolean }
  | { type: 'RESET' };

/** 初始状态 */
const initialState: FormState = {
  phone: '',
  smsCode: '',
  isAgreed: false,
  errors: {},
  isSubmitting: false,
};

/** 表单reducer */
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_FIELD':
      return {
        ...state,
        [action.field]: action.value,
        errors: {
          ...state.errors,
          [action.field]: undefined, // 清除该字段的错误
        },
      };
    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.field]: action.error,
        },
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.field]: undefined,
        },
      };
    case 'SET_SUBMITTING':
      return {
        ...state,
        isSubmitting: action.isSubmitting,
      };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

/** 表单验证规则 */
const validationRules = {
  phone: (value: string) => {
    if (!value.trim()) return '请输入手机号';
    if (!validatePhone(value)) return '请填写正确的手机号';
    return null;
  },
  smsCode: (value: string) => {
    if (!value.trim()) return '请输入验证码';
    if (value.length !== 6) return '验证码为6位数字';
    return null;
  },
  isAgreed: (value: boolean) => {
    if (!value) return '请先同意用户协议';
    return null;
  },
};

/** 验证器类型 */
type ValidatorFunction = (_value: string | boolean) => string | null;

/** 登录表单状态管理Hook */
export function useLoginForm() {
  const [state, dispatch] = useReducer(formReducer, initialState);

  /** 设置表单字段值 */
  const setField = useCallback(
    (field: keyof FormFields, _value: FormFieldValue) => {
      dispatch({ type: 'SET_FIELD', field, value: _value });
    },
    []
  );

  /** 设置错误信息 */
  const setError = useCallback((field: keyof FormFields, error: string) => {
    dispatch({ type: 'SET_ERROR', field, error });
  }, []);

  /** 清除错误信息 */
  const clearError = useCallback((field: keyof FormFields) => {
    dispatch({ type: 'CLEAR_ERROR', field });
  }, []);

  /** 设置提交状态 */
  const setSubmitting = useCallback((isSubmitting: boolean) => {
    dispatch({ type: 'SET_SUBMITTING', isSubmitting });
  }, []);

  /** 重置表单 */
  const reset = useCallback(() => {
    dispatch({ type: 'RESET' });
  }, []);

  /** 验证单个字段 */
  const validateField = useCallback(
    (field: keyof FormFields) => {
      const value = state[field];
      const validator = validationRules[field] as ValidatorFunction;
      const error = validator(value);

      if (error) {
        setError(field, error);
        return false;
      } else {
        clearError(field);
        return true;
      }
    },
    [state, setError, clearError]
  );

  /** 验证所有字段 */
  const validateAll = useCallback(() => {
    const fields: Array<keyof FormFields> = ['phone', 'smsCode', 'isAgreed'];
    let hasError = false;
    let firstError = '';

    // 先验证所有字段，收集错误信息
    fields.forEach(field => {
      const value = state[field];
      const validator = validationRules[field] as ValidatorFunction;
      const error = validator(value);

      if (error) {
        setError(field, error);
        if (!hasError) {
          hasError = true;
          firstError = error;
        }
      } else {
        clearError(field);
      }
    });

    return { isValid: !hasError, firstError };
  }, [state, setError, clearError]);

  /** 获取表单数据 */
  const getFormData = useCallback(
    () => ({
      phone: state.phone,
      smsCode: state.smsCode,
      isAgreed: state.isAgreed,
    }),
    [state.phone, state.smsCode, state.isAgreed]
  );

  return {
    // 状态
    ...state,
    // 方法
    setField,
    setError,
    clearError,
    setSubmitting,
    reset,
    validateField,
    validateAll,
    getFormData,
  };
}
