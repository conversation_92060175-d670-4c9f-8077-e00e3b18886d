import { request } from '@/utils/request';

export const getOrderDetailApi = (
  params: API.HotelOrderDetails.OrderDetailProps
) =>
  request<API.HotelOrderDetailsApi.OrderDetail>(`/hotel/order/getOrderDetail`, {
    params,
  });

export const cancelOrderApi = (
  params: API.HotelOrderDetails.OrderDetailProps
) =>
  request<any>(`/hotel/order/cancelOrder`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
  });

export const getInvoiceInfo = (
  params: API.HotelOrderDetails.OrderDetailProps
) =>
  request<any>(`/hotel/order/getInvoiceInfo`, {
    params,
  });
