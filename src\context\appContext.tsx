'use client';
import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';

import { DEFAULT_CITY_INFO } from '@/config';

import { StorageKeyEnum } from '../enum';

interface AppContextType {
  geoInfo: {
    lat: string | number;
    lng: string | number;
    /** 定位更新时间戳ms */
    lastUpdatedTime?: number;
  } & Pick<
    IGeoInfo,
    | 'address'
    | 'districtName'
    | 'countryName'
    | 'provinceName'
    | 'coordinateType'
    | 'township'
    | 'cityId'
    | 'cityName'
  >;
  /** 更新定位信息 */
  updateGeoInfo: (_data: AppContextType['geoInfo']) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider = ({ children }: { children: ReactNode }) => {
  // 初始化时使用默认值，避免SSR水合不匹配
  const [geoInfo, setGeoInfo] = useState<AppContextType['geoInfo']>({
    lat: '39.909187',
    lng: '116.397455',
    lastUpdatedTime: 0,
    address: '',
    districtName: '',
    countryName: '',
    provinceName: '',
    coordinateType: 'GCJ-02',
    township: '',
    ...DEFAULT_CITY_INFO,
  });

  // 在客户端水合完成后从localStorage读取数据
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedGeoInfo = localStorage.getItem(StorageKeyEnum.Location);
      if (storedGeoInfo) {
        try {
          setGeoInfo(JSON.parse(storedGeoInfo));
        } catch (_e) {
          console.log(_e);
        }
      }
    }
  }, []);

  const updateGeoInfo = (data: AppContextType['geoInfo']) => {
    setGeoInfo({
      ...data,
      lastUpdatedTime: data.lastUpdatedTime || Date.now(),
    });
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(StorageKeyEnum.Location, JSON.stringify(geoInfo));
    }
  }, [geoInfo]);

  return (
    <AppContext.Provider value={{ geoInfo, updateGeoInfo }}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within a AppProvider');
  }
  return context;
};
