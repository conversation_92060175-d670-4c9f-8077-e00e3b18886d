import Cookies from 'js-cookie';

import { CookiesKeyEnum } from '../enum';

const opts = {
  host: 'cn-hangzhou.log.aliyuncs.com', // 所在地域的服务入口。例如cn-hangzhou.log.aliyuncs.com
  project: 'ota-app-web', // Project名称。
  logstore: 'ota-app-web', // Logstore名称。
  time: 10, // 发送日志的时间间隔，默认是10秒。
  count: 10, // 发送日志的数量大小，默认是10条。
  topic: 'topic', // 自定义日志主题。
  source: 'source',
  tags: {
    tags: 'tags',
  },
};

export interface TrackParams {
  eventType:
    | 'pv'
    | 'uv'
    | 'route'
    | 'click'
    | 'js_error'
    | 'react_error'
    | 'try_catch_error'
    | 'info';
  path?: string;
  error?: any;
  message?: string;
  stack?: string;
  extra?: Record<string, any>;
  [key: string]: any;
}

export const tracker = (params: TrackParams) => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    // 兼容处理：优先读取HT-Token，如果没有值再读取token
    const htToken = Cookies.get(CookiesKeyEnum.AUTH_TOKEN);
    const fallbackToken = Cookies.get('token');
    const token = htToken || fallbackToken || '';
    void import('@aliyun-sls/web-track-browser').then(
      ({ default: SlsTracker }) => {
        new SlsTracker(opts).send({
          ...params,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          env: process.env.NODE_ENV,
          token,
        });
      }
    );
  }
  return null;
};

/**
 * 用于try/catch块的错误埋点辅助函数
 *
 * @param error 捕获到的错误对象
 * @param extra 额外信息（可选）
 */
export function tryCatchTracker(error: any, extra?: Record<string, any>) {
  tracker({
    eventType: 'try_catch_error',
    message: error?.message || String(error),
    stack: error?.stack,
    error,
    extra,
    path: typeof window !== 'undefined' ? window.location.pathname : '',
  });
}
