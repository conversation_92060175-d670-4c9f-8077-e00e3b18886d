{"name": "ota-app-web", "version": "0.0.1", "type": "module", "scripts": {"dev": "dotenv -e .env.dev next dev", "dev:server": "dotenv -e .env.dev tsx server.ts", "dev:mock": "concurrently -k \"nodemon mock/server.js\" \"dotenv -e .env.mock next dev\"", "dev:debug": "dotenv -e .env.dev cross-env NEXT_PUBLIC_DEBUG_ENABLED=true next dev", "dev:fast": "dotenv -e .env.dev cross-env ESLINT_NO_DEV_ERRORS=true DISABLE_ESLINT_PLUGIN=true next dev", "dev:turbo": "dotenv -e .env.dev cross-env ESLINT_NO_DEV_ERRORS=true DISABLE_ESLINT_PLUGIN=true NEXT_TELEMETRY_DISABLED=1 next dev --turbo", "dev:test": "dotenv -e .env.test next dev", "build": "next build", "build:test": "dotenv -e .env.test next build", "build:prod": "dotenv -e .env.prod next build", "start": "cross-env NODE_ENV=production tsx server.ts", "lint": "next lint --max-warnings=99999", "fix": "next lint --fix --max-warnings=99999", "cleanup": "tsx ./scripts/bin.ts cleanup", "commit": "tsx ./scripts/bin.ts git-commit", "commit:zh": "tsx ./scripts/bin.ts git-commit -l=zh-cn", "gen-route": "tsx ./scripts/bin.ts gen-route", "release": "tsx ./scripts/bin.ts release", "git-commit-verify": "tsx ./scripts/bin.ts git-commit-verify", "prepare": "simple-git-hooks", "typecheck": "tsc --project tsconfig.json --noEmit --skipLib<PERSON><PERSON>ck", "update-pkg": "tsx ./scripts/bin.ts update-pkg", "pre-commit": "pnpm typecheck && pnpm lint-staged", "commit-msg": "pnpm commit-msg"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "dependencies": {"@aliyun-sls/web-track-browser": "^0.3.9", "@amap/amap-jsapi-loader": "^1.0.1", "@ota/utils": "0.0.4", "@reduxjs/toolkit": "^2.8.2", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "js-cookie": "^3.0.5", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-masonry-css": "^1.0.16", "react-redux": "^9.2.0", "react-scroll": "^1.9.3", "react-vant": "^3.3.5", "skywalking-backend-js": "^0.8.0", "swiper": "^11.2.10"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.15", "@eslint/eslintrc": "^3", "@eslint/js": "^9.27.0", "@soybeanjs/changelog": "0.3.24", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.19", "@types/node": "^20", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/react-scroll": "^1.8.10", "@types/yargs": "17.0.33", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "10.4.14", "bumpp": "10.0.3", "c12": "2.0.4", "cac": "6.7.14", "concurrently": "^9.1.2", "consola": "3.4.0", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "enquirer": "2.4.1", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-sort": "4.0.0", "eslint-plugin-unused-imports": "^4.1.4", "execa": "9.5.2", "express": "^4.18.2", "globals": "^16.1.0", "husky": "^9.1.7", "kolorist": "1.8.0", "lint-staged": "^16.0.0", "nodemon": "^3.0.2", "npm-check-updates": "17.1.14", "postcss": "8.4.24", "postcss-pxtorem": "^6.0.0", "prettier": "^3.5.3", "prettier-plugin-jsdoc": "^1.3.2", "rimraf": "6.0.1", "simple-git-hooks": "^2.13.0", "tailwindcss": "3.3.5", "tsx": "^4.19.4", "typescript": "^5", "yargs": "17.7.2"}, "simple-git-hooks": {"commit-msg": "npx tsx ./scripts/bin.ts git-commit-verify", "pre-commit": "npx prettier --write . && npx tsc --project tsconfig.json --noEmit --skipLibCheck && npx lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix --max-warnings=99999"]}, "browserslist": [">0.2%", "not dead", "not op_mini all", "ios >= 9", "last 2 versions"]}