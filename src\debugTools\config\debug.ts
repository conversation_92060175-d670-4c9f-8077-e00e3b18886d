/** 调试配置文件 支持通过环境变量控制调试功能 */

export interface DebugConfig {
  /** 是否启用调试模式 */
  enabled: boolean;
  /** 是否显示日志面板 */
  showLogPanel: boolean;
  /** 日志级别 */
  logLevel: 'all' | 'log' | 'warn' | 'error' | 'info';
  /** 最大日志条数 */
  maxLogs: number;
  /** 日志面板位置 */
  logPanelPosition: 'top-right' | 'bottom-right' | 'bottom-left' | 'top-left';
  /** 日志面板宽度 */
  logPanelWidth: number;
  /** 日志面板高度 */
  logPanelHeight: number;
}

// 环境变量配置
const isDevelopment = process.env.NODE_ENV === 'development';

// 默认配置 - 只有明确启用调试模式时才显示
const defaultConfig: DebugConfig = {
  enabled: false, // 默认关闭，只有通过环境变量明确启用才开启
  showLogPanel: isDevelopment,
  logLevel: 'all',
  maxLogs: 1000,
  logPanelPosition: 'bottom-right',
  logPanelWidth: 400,
  logPanelHeight: 300,
};

// 从环境变量读取配置
const getConfigFromEnv = (): Partial<DebugConfig> => {
  const config: Partial<DebugConfig> = {};

  // 调试模式开关
  if (process.env.NEXT_PUBLIC_DEBUG_ENABLED !== undefined) {
    config.enabled = process.env.NEXT_PUBLIC_DEBUG_ENABLED === 'true';
  }

  // 日志面板显示
  if (process.env.NEXT_PUBLIC_DEBUG_SHOW_LOG_PANEL !== undefined) {
    config.showLogPanel =
      process.env.NEXT_PUBLIC_DEBUG_SHOW_LOG_PANEL === 'true';
  }

  // 日志级别
  if (process.env.NEXT_PUBLIC_DEBUG_LOG_LEVEL) {
    const level = process.env
      .NEXT_PUBLIC_DEBUG_LOG_LEVEL as DebugConfig['logLevel'];
    if (['all', 'log', 'warn', 'error', 'info'].includes(level)) {
      config.logLevel = level;
    }
  }

  // 最大日志条数
  if (process.env.NEXT_PUBLIC_DEBUG_MAX_LOGS) {
    const maxLogs = parseInt(process.env.NEXT_PUBLIC_DEBUG_MAX_LOGS, 10);
    if (!isNaN(maxLogs) && maxLogs > 0) {
      config.maxLogs = maxLogs;
    }
  }

  // 日志面板位置
  if (process.env.NEXT_PUBLIC_DEBUG_LOG_PANEL_POSITION) {
    const position = process.env
      .NEXT_PUBLIC_DEBUG_LOG_PANEL_POSITION as DebugConfig['logPanelPosition'];
    if (
      ['top-right', 'bottom-right', 'bottom-left', 'top-left'].includes(
        position
      )
    ) {
      config.logPanelPosition = position;
    }
  }

  // 日志面板宽度
  if (process.env.NEXT_PUBLIC_DEBUG_LOG_PANEL_WIDTH) {
    const width = parseInt(process.env.NEXT_PUBLIC_DEBUG_LOG_PANEL_WIDTH, 10);
    if (!isNaN(width) && width > 0) {
      config.logPanelWidth = width;
    }
  }

  // 日志面板高度
  if (process.env.NEXT_PUBLIC_DEBUG_LOG_PANEL_HEIGHT) {
    const height = parseInt(process.env.NEXT_PUBLIC_DEBUG_LOG_PANEL_HEIGHT, 10);
    if (!isNaN(height) && height > 0) {
      config.logPanelHeight = height;
    }
  }

  return config;
};

// 合并配置
export const debugConfig: DebugConfig = {
  ...defaultConfig,
  ...getConfigFromEnv(),
};

export default debugConfig;
