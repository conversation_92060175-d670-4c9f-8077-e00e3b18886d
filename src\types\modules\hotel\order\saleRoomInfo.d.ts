declare namespace API.HotelSaleRoomInfo {
  interface RoomInfoParams {
    roomKey: string;
    /** 扩展参数 */
    [key: string]: string | string[] | undefined;
  }

  interface AdditionInfo {
    content: string;
    desp: string;
    key: string;
  }

  interface Room {
    additionInfoList: AdditionInfo[];
    description: string;
    filterTags: string[];
    roomId: string;
    roomImageUrl: string;
    tags: string[];
  }

  interface Meal {
    date: string;
    dayMealCopyWriting: string;
  }

  interface RatePlan {
    averageRate: number;
    breakDesc: string;
    currencyCode: string;
    currencySymbol: string;
    currentAlloment: number;
    freeCancelRuleShowDesc: string;
    goodsUniqId: string;
    hasBreakfast: boolean;
    hotelCode: string;
    identification: number;
    instantConfirmation: boolean;
    invoiceMode: string;
    isPriceLimittedProduct: boolean;
    littleMajiaId: string;
    maxCheckinRooms: string;
    meals: Meal[];
    payType: string;
    priceLimitedType: number;
    priceTip: string;
    ratePlanId: string;
    roomTypeId: string;
    saleRoomDescription: string;
    saleRoomTags: string[];
    tags: string[];
    totalRate: number;
  }

  interface RoomInfo {
    roomId: string;
    roomInfoName: string;
    rpList: RatePlan[];
  }

  interface SubFilter {
    filterId: number;
    name: string;
    selected: boolean;
    typeId: number;
  }

  interface Filter {
    filterId: number;
    name: string;
    selected: boolean;
    subFilters?: SubFilter[];
    typeId: number;
  }

  interface FacilityItem {
    iconUrl: string;
    title: string;
  }

  interface Facilities {
    jumpUrl: string;
    list: FacilityItem[];
    title: string;
  }

  interface PolicyItem {
    description: string;
    policyTitle: string;
  }

  interface Policy {
    jumpUrl: string;
    policys: PolicyItem[];
    title: string;
  }

  interface PriceDescription {
    description: string;
    title: string;
  }

  interface HotelImageCategory {
    category: string;
    imageList: string[];
  }

  interface HotelStaticInfo {
    businessZone: string;
    businessZoneName: string;
    cityId: string;
    cityName: string;
    decorateDate: string;
    decorateDateDesc: string;
    departureTime: string;
    distance: string;
    earliestArrivalTime: string;
    facilities: Facilities;
    hotFacilityUrl: string;
    hotelAddress: string;
    hotelId: string;
    hotelImages: HotelImageCategory[];
    hotelName: string;
    hotelTag: string[];
    inDate: string;
    lat: number;
    lon: number;
    outDate: string;
    policy: Policy;
    priceDescription: PriceDescription;
    readBeforeBookingUrl: string;
    telNo: string;
  }

  interface HotelData {
    baseMRoomList: Room[];
    filterList: Filter[];
    hotelStaticInfo: HotelStaticInfo;
    roomInfoList: RoomInfo[];
    roomKey: string;
    unMatchRoomInfoList: any[]; // Can be more specific if needed
  }

  interface RoomInfoDetail extends RoomInfo, RatePlan, HotelStaticInfo {
    [key: string]: string | string[] | undefined;
  }
}
