'use client';

import React from 'react';

import { Toast } from './index';

// 基础使用示例
export const BasicExample = () => {
  const handleClick = () => {
    Toast('这是一个基础提示');
  };

  return (
    <button
      onClick={handleClick}
      className="px-4 py-2 bg-blue-500 text-white rounded"
    >
      显示基础提示
    </button>
  );
};

// 不同类型的提示
export const TypeExample = () => (
  <div className="space-x-2">
    <button
      onClick={() => Toast.info('信息提示')}
      className="px-4 py-2 bg-gray-500 text-white rounded"
    >
      信息
    </button>
    <button
      onClick={() => Toast.success('操作成功')}
      className="px-4 py-2 bg-green-500 text-white rounded"
    >
      成功
    </button>
    <button
      onClick={() => Toast.fail('操作失败')}
      className="px-4 py-2 bg-red-500 text-white rounded"
    >
      失败
    </button>
    <button
      onClick={() => Toast.loading('加载中...')}
      className="px-4 py-2 bg-yellow-500 text-white rounded"
    >
      加载
    </button>
  </div>
);

// 异步操作示例
export const AsyncExample = () => {
  const handleAsyncOperation = async () => {
    const loadingToast = Toast.loading('处理中...');

    try {
      // 模拟异步操作
      await new Promise(resolve => setTimeout(resolve, 2000));

      loadingToast.close();
      Toast.success('操作完成！');
    } catch (_error) {
      loadingToast.close();
      Toast.fail('操作失败！');
    }
  };

  return (
    <button
      onClick={handleAsyncOperation}
      className="px-4 py-2 bg-purple-500 text-white rounded"
    >
      异步操作
    </button>
  );
};

// 表单提交示例
export const FormExample = () => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData(e.target as HTMLFormElement);
    const name = formData.get('name') as string;

    if (!name.trim()) {
      Toast.fail('请输入姓名');
      return;
    }

    Toast.success(`欢迎，${name}！`);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input
        name="name"
        type="text"
        placeholder="请输入姓名"
        className="px-3 py-2 border border-gray-300 rounded"
      />
      <button
        type="submit"
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        提交
      </button>
    </form>
  );
};

// 网络请求示例
export const NetworkExample = () => {
  const fetchData = async () => {
    const loadingToast = Toast.loading('获取数据中...');

    try {
      const response = await fetch('/api/data');

      if (!response.ok) {
        throw new Error('网络请求失败');
      }

      const data = await response.json();
      loadingToast.close();
      Toast.success('数据获取成功');

      console.warn('获取的数据:', data);
    } catch (_error) {
      loadingToast.close();
      Toast.fail('网络请求失败，请重试');
    }
  };

  return (
    <button
      onClick={fetchData}
      className="px-4 py-2 bg-indigo-500 text-white rounded"
    >
      获取数据
    </button>
  );
};

// 自定义配置示例
export const CustomExample = () => {
  const showCustomToast = () => {
    Toast({
      message: '这是一个自定义配置的提示',
      position: 'top',
      duration: 5000,
      overlay: true,
      forbidClick: true,
      onOpened: () => console.log('Toast 已显示'),
      onClose: () => console.log('Toast 已关闭'),
    });
  };

  return (
    <button
      onClick={showCustomToast}
      className="px-4 py-2 bg-teal-500 text-white rounded"
    >
      自定义配置
    </button>
  );
};

// 只显示图标示例
export const IconOnlyExample = () => {
  const showIconOnlyToast = () => {
    const loadingToast = Toast.loading({
      icon: (
        <img
          src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/loading.gif"
          alt="loading"
          className="w-[120px] h-[120px]"
        />
      ),
      duration: 3000,
      overlay: true,
      forbidClick: true,
      iconOnly: true, // 只显示图标，不显示外框
    });

    // 3秒后自动关闭
    setTimeout(() => {
      loadingToast.close();
    }, 3000);
  };

  return (
    <button
      onClick={showIconOnlyToast}
      className="px-4 py-2 bg-purple-500 text-white rounded"
    >
      只显示图标
    </button>
  );
};

// 全局配置示例
export const GlobalConfigExample = () => {
  const setGlobalConfig = () => {
    Toast.setDefaultOptions({ duration: 4000 });
    Toast('现在默认显示4秒');
  };

  const resetGlobalConfig = () => {
    Toast.resetDefaultOptions();
    Toast('已重置为默认配置');
  };

  return (
    <div className="space-x-2">
      <button
        onClick={setGlobalConfig}
        className="px-4 py-2 bg-orange-500 text-white rounded"
      >
        设置全局配置
      </button>
      <button
        onClick={resetGlobalConfig}
        className="px-4 py-2 bg-gray-500 text-white rounded"
      >
        重置配置
      </button>
    </div>
  );
};
