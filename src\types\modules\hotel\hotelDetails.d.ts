// 酒店详情模块类型声明
// 定义酒店详情页面相关的接口和数据结构

declare namespace API.HotelDetails {
  /** 酒店详情获取响应 */
  interface IHotelDetailsFetchResponse {
    code: number;
    data: IHotelDetailsFetchResponseData;
    message: string;
  }

  /** 酒店详情获取响应数据 */
  interface IHotelDetailsFetchResponseData {
    baseMRoomList: IBaseMRoom[];
    favorite: boolean;
    filterList: IFilter[];
    hotelStaticInfo: IHotelStaticInfo;
    roomInfoList: IRoomInfo[];
    roomKey: string;
    unMatchRoomInfoList: any[];
  }

  // ============= 房型相关接口 =============

  /** 基础房型信息 */
  interface IBaseMRoom {
    additionInfoList: IAdditionInfo[];
    description: string;
    filterTags: string[];
    roomId: string;
    roomImageUrl: string;
    tags: string[];
  }

  /** 附加信息项 */
  interface IAdditionInfo {
    content: string;
    desp: string;
    key: string;
  }

  /** 房型信息 */
  interface IRoomInfo {
    roomId: string;
    roomInfoName: string;
    rpList: IRatePlan[];
  }

  /** 价格计划信息 */
  interface IRatePlan {
    averageRate: number;
    breakDesc: string;
    currencyCode: string;
    currencySymbol: string;
    currentAlloment: number;
    freeCancelRuleShowDesc: string;
    goodsUniqId: string;
    hasBreakfast: boolean;
    hotelCode: string;
    identification: number;
    instantConfirmation: boolean;
    invoiceMode: string;
    isPriceLimittedProduct: boolean;
    littleMajiaId: string;
    maxCheckinRooms: string;
    meals: IMeal[];
    payType: string;
    priceLimitedType: number;
    priceTip: string;
    ratePlanId: string;
    roomTypeId: string;
    saleRoomDescription: string;
    saleRoomTags: string[];
    tags: string[];
    totalRate: number;
  }

  /** 餐食信息 */
  interface IMeal {
    date: string;
    dayMealCopyWriting: string;
  }

  // ============= 过滤器相关接口 =============

  /** 过滤器 */
  interface IFilter {
    filterId: number;
    name: string;
    selected: boolean;
    subFilters: ISubFilter[];
    typeId: number;
  }

  /** 子过滤器 */
  interface ISubFilter {
    filterId: number;
    name: string;
    selected: boolean;
    typeId: number;
  }

  // ============= 酒店静态信息相关接口 =============

  /** 酒店静态信息 */
  interface IHotelStaticInfo {
    businessZone: string;
    businessZoneName: string;
    cityId: string;
    cityName: string;
    decorateDate: string;
    decorateDateDesc: string;
    departureTime: string;
    distance: string;
    earliestArrivalTime: string;
    facilities: IFacilities;
    hotFacilityUrl: string;
    hotelAddress: string;
    hotelId: string;
    hotelImages: IHotelImageCategory[];
    hotelName: string;
    hotelTag: string[];
    inDate: string;
    lat: number;
    lon: number;
    outDate: string;
    policy: IPolicy;
    priceDescription: IPriceDescription;
    readBeforeBookingUrl: string;
    telNo: string;
  }

  /** 设施信息 */
  interface IFacilities {
    jumpUrl: string;
    list: IFacilityItem[];
    title: string;
  }

  /** 设施项 */
  interface IFacilityItem {
    iconUrl: string;
    title: string;
  }

  /** 政策信息 */
  interface IPolicy {
    jumpUrl: string;
    policys: IPolicyItem[];
    title: string;
  }

  /** 政策项 */
  interface IPolicyItem {
    description: string;
    policyTitle: string;
  }

  /** 价格说明 */
  interface IPriceDescription {
    jumpUrl: string;
    description: string;
    title: string;
  }

  /** 酒店图片类别 */
  interface IHotelImageCategory {
    category: string;
    imageList: string[];
  }

  // ============= 页面参数接口 =============
  /** 坐标信息 */
  interface ICoordinateInfo {
    coordinateType: string;
    latitude: number;
    longitude: number;
  }

  /** 酒店基础信息获取请求参数 */
  interface IHotelBaseInfoFetchRequest {
    arrivalDate: string;
    coordinateInfo: ICoordinateInfo;
    departureDate: string;
    hotelId: string;
    filter: string[];
  }
}
