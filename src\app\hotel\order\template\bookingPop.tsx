'use client';
import Dialog from '@/components/dialog';
import type { AppDispatch, RootState } from '@/store';
import { setGetPriceInfoFail, setGetPriceInfotext } from '@/store/hotel/order';
import { useDispatch, useSelector } from 'react-redux';
const BookingPop = () => {
  const getPriceInfoFail = useSelector(
    (state: RootState) => state.hotelOrder.getPriceInfoFail
  );
  const getPriceInfotext = useSelector(
    (state: RootState) => state.hotelOrder.getPriceInfotext
  );
  const dispatch = useDispatch<AppDispatch>();
  const handleOnCancel = () => {
    window.history.back();
    dispatch(setGetPriceInfoFail(false));
    dispatch(setGetPriceInfotext(''));
  };

  return (
    <Dialog
      visible={getPriceInfoFail}
      title={getPriceInfotext}
      cancelText="确认"
      hiddenConfirmBtm
      onCancel={handleOnCancel}
    />
  );
};

export default BookingPop;
