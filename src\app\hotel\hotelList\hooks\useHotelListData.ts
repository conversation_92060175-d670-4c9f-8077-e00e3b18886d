import { useCallback, useEffect, useState } from 'react';

import { getHotelList } from '@/api/hotel/hotelList';

/** 酒店选择的日期范围类型 [开始日期, 结束日期] */
export type HotelDateType = [string, string];

// Hook的输入参数类型
export interface UseHotelListDataParams {
  filters: API.HotelList.IFilter[];
  queryText: string;
  dates: HotelDateType;
  cityInfo: {
    cityId: string;
    cityName: string;
  };
  geoInfo: {
    lat: string | number;
    lng: string | number;
    cityId: string;
    cityName: string;
  };
}

// Hook的返回结果类型
export interface UseHotelListDataReturn {
  hotelList: API.HotelList.IHotelListItem[];
  isLoading: boolean;
  isLoadingMore: boolean;
  isRefreshing: boolean;
  hasMore: boolean;
  error: string | null;
  onRefresh: () => Promise<void>;
  loadMore: () => Promise<void>;
}

/** 酒店列表数据管理Hook 负责处理酒店数据的获取、API调用、状态管理和分页逻辑 */
export function useHotelListData({
  filters,
  queryText,
  dates,
  cityInfo,
  geoInfo,
}: UseHotelListDataParams): UseHotelListDataReturn {
  // 数据状态
  const [hotelList, setHotelList] = useState<API.HotelList.IHotelListItem[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageIndex, setPageIndex] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // 构建API参数的工具函数
  const buildApiParams = useCallback(
    (page: number) => ({
      queryText,
      cityId: cityInfo.cityId,
      cityName: cityInfo.cityName,
      arrivalDate: dates[0],
      departureDate: dates[1],
      sort: 'IntelligentSort' as const,
      currentPosition: {
        longitude: Number(geoInfo.lng),
        latitude: Number(geoInfo.lat),
        coordinateType: 'GCJ-02',
        cityId: geoInfo.cityId,
        cityName: geoInfo.cityName,
      },
      filters,
      pageSize: 10,
      pageIndex: page,
    }),
    [dates, cityInfo, geoInfo, queryText, filters]
  );

  // 通用的数据获取函数
  const fetchHotelData = useCallback(
    async (page: number, resetList: boolean = false) => {
      try {
        const params = buildApiParams(page);
        const { code, data, message } = await getHotelList(params);

        if (code === 200 && data?.hotels) {
          if (resetList) {
            setHotelList(data.hotels);
          } else {
            setHotelList(prev => [...prev, ...data.hotels]);
          }
          setPageIndex(page);
          setHasMore(data.hasMore ?? false);
          return true;
        } else {
          setError(`获取酒店列表失败: ${message}`);
          return false;
        }
      } catch (err) {
        console.error('获取酒店数据失败:', err);
        setError('数据获取失败，请稍后重试');
        return false;
      }
    },
    [buildApiParams]
  );

  // 监听所有依赖项的变化，自动重新获取数据
  useEffect(() => {
    setIsLoading(true);
    setError(null);
    void fetchHotelData(1, true).finally(() => {
      setIsLoading(false);
    });
  }, [fetchHotelData]); // 依赖于稳定的fetchHotelData函数

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);
    await fetchHotelData(1, true);
    setIsRefreshing(false);
  }, [fetchHotelData]);

  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore || isRefreshing || isLoading) return;

    setIsLoadingMore(true);
    const nextPage = pageIndex + 1;
    await fetchHotelData(nextPage, false);
    setIsLoadingMore(false);
  }, [
    hasMore,
    isLoadingMore,
    isRefreshing,
    isLoading,
    pageIndex,
    fetchHotelData,
  ]);

  return {
    hotelList,
    isLoading,
    isLoadingMore,
    isRefreshing,
    hasMore,
    error,
    onRefresh,
    loadMore,
  };
}
