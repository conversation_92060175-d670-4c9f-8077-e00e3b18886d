import { headers } from 'next/headers';

import { generateThemeVariables, getThemeUAType } from '@/utils/theme';

export async function ServerThemeProvider() {
  // 获取请求头中的User-Agent
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') ?? '';

  // 根据UA确定主题类型
  const themeUA = getThemeUAType(userAgent);
  // 生成主题变量
  const variables = generateThemeVariables(themeUA);

  // 构建内联样式
  const themeStyles = Object.entries(variables)
    .map(([key, value]) => `${key}: ${value};`)
    .join(' ');

  return (
    <style
      id="server-theme-styles"
      dangerouslySetInnerHTML={{
        __html: `:root { ${themeStyles} }`,
      }}
    />
  );
}
