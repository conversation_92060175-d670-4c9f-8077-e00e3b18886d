// eslint-disable-next-line camelcase
import { Geist, Geist_Mono } from 'next/font/google';

import { ToastContainer } from '@/components';
import ReduxProvider from '@/components/ReduxProvider';
import { ServerThemeProvider } from '@/components/ServerThemeProvider';
import { AppProvider, AuthProvider, HotelProvider } from '@/context';
import { DebugWrapper } from '@/debugTools/components/DebugWrapper';

import { getImageUrl } from '../utils';

import ClientTrackers from './ClientTrackers';

// 导入调试包装组件

// 导入 UserProvider

import '@/styles/globals.css';
import '@/styles/iconfont.css';

import type { Metadata } from 'next';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: '翎游 - 旅游 出行 旅行 自由行 行程定制',
  description:
    '翎游是AI+旅游领域的创新科技平台，通过自研垂类大模型与多模态交互系统，打造AI行程管家，提供行前决策、行中优化、行后延伸的全周期智慧旅游服务。面向景区及目的地，推出数字文旅大脑驱动行业效能升级。现已布局一二线高净值市场与跨境网络，获头部资本战略投资，致力于以AI重构人文旅行体验。',
  icons: {
    icon: getImageUrl('favicon.ico'),
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: 'no',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ClientTrackers />
        <ServerThemeProvider />
        <AuthProvider>
          <ReduxProvider>
            <AppProvider>
              <HotelProvider>{children}</HotelProvider>
            </AppProvider>
          </ReduxProvider>
        </AuthProvider>
        <DebugWrapper />
        <ToastContainer />
        <img
          src="http://ota-app-web.cn-hangzhou.log.aliyuncs.com/logstores/ota-app-web/track.gif?APIVersion=0.6.0&key1=val1&key2=val2"
          alt=""
          style={{ display: 'none' }}
        />
        <img
          src="http://ota-app-web.cn-hangzhou.log.aliyuncs.com/logstores/ota-app-web/track_ua.gif?APIVersion=0.6.0&key1=val1&key2=val2"
          alt=""
          style={{ display: 'none' }}
        />
      </body>
    </html>
  );
}
