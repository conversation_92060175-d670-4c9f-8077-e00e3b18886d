import { useCallback, useMemo } from 'react';

import { useHotel } from '@/context';
import { deserializeFilters, serializeFilters } from '@/utils';

/** URL参数管理hook的类型定义 */

export interface UseHotelListUrlParamsParams {
  /** Next.js搜索参数 */
  searchParams: URLSearchParams;
  /** Next.js路由器 */
  router: { replace: (_url: string) => void };
  /** 当前路径名 */
  pathname: string;
}

export interface UseHotelListUrlParamsReturn {
  /** 当前快筛条件 */
  filters: API.HotelList.IFilter[];
  /** 当前搜索关键词 */
  queryText: string;
  /** 更新URL参数 */
  updateUrl: (
    _newFilters: API.HotelList.IFilter[],
    _newQueryText: string
  ) => void;
}

/** 酒店列表URL参数管理Hook 负责处理URL参数的读取、序列化、同步和状态管理 */
export function useHotelListUrlParams({
  searchParams,
  router,
  pathname,
}: UseHotelListUrlParamsParams): UseHotelListUrlParamsReturn {
  // 从Context获取keyword
  const { keyword: keywordFromContext } = useHotel();

  // 从URL读取筛选条件
  const filters = useMemo(() => {
    const filtersParam = searchParams.get('filters');
    return deserializeFilters(filtersParam);
  }, [searchParams]);

  // 从URL读取搜索关键词，如果不存在则使用Context中的值
  const queryText = useMemo(
    () => searchParams.get('q') ?? keywordFromContext,
    [searchParams, keywordFromContext]
  );

  // URL参数更新函数
  const updateUrl = useCallback(
    (newFilters: API.HotelList.IFilter[], newQueryText: string) => {
      const filtersString = serializeFilters(newFilters);
      const newSearchParams = new URLSearchParams(searchParams);

      // 更新或删除参数
      if (filtersString) {
        newSearchParams.set('filters', filtersString);
      } else {
        newSearchParams.delete('filters');
      }

      if (newQueryText) {
        newSearchParams.set('q', newQueryText);
      } else {
        newSearchParams.delete('q');
      }

      // 构建新URL
      const queryString = newSearchParams.toString();
      const newUrl = queryString ? `${pathname}?${queryString}` : pathname;

      // 更新URL（不刷新页面）
      router.replace(newUrl);
    },
    [router, pathname, searchParams]
  );

  return {
    filters,
    queryText,
    updateUrl,
  };
}
