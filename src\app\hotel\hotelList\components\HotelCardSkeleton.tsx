'use client';

const HotelCardSkeleton = () => (
  <div className="flex bg-white rounded-[20px] overflow-hidden my-2 p-1">
    {/* 左侧图片占位 */}
    <div className="w-[116px] h-[100px] flex-shrink-0 rounded-[16px] bg-gray-200 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
    </div>

    {/* 右侧信息占位 */}
    <div className="flex-1 px-3 py-1 flex flex-col justify-between min-h-[100px]">
      {/* 酒店名称占位 - 2行 */}
      <div className="mb-2">
        <div className="h-4 bg-gray-200 rounded mb-2 w-full relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="h-4 bg-gray-200 rounded w-3/4 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>

      {/* 评分和星级占位 */}
      <div className="flex items-center mb-1.5">
        <div className="w-8 h-5 bg-gray-200 rounded mr-1.5 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="w-16 h-5 bg-gray-200 rounded relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>

      {/* 地区信息占位 */}
      <div className="flex items-center mb-2">
        <div className="h-3 bg-gray-200 rounded w-16 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="h-3 bg-gray-200 rounded ml-4 w-20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>

      {/* 酒店标签占位 */}
      <div className="flex flex-wrap gap-1 h-[22px] overflow-hidden mb-2.5">
        <div className="w-12 h-[22px] bg-gray-200 rounded-lg relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="w-16 h-[22px] bg-gray-200 rounded-lg relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
        <div className="w-14 h-[22px] bg-gray-200 rounded-lg relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>

      {/* 价格信息占位 */}
      <div className="flex items-baseline justify-end mt-auto">
        <div className="h-6 bg-gray-200 rounded w-16 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-shimmer" />
        </div>
      </div>
    </div>
  </div>
);

// 骨架屏列表组件
export const HotelCardSkeletonList = ({ count = 5 }: { count?: number }) => (
  <div className="px-2">
    {Array.from({ length: count }, (_, index) => (
      <HotelCardSkeleton key={index} />
    ))}
  </div>
);

export default HotelCardSkeleton;
