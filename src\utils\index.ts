import { CDN_BASE_URL } from '../const/cdn';

export function buildQueryString(params: Record<string, unknown>): string {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
}

export const getImageUrl = (filename: string) => `${CDN_BASE_URL}/${filename}`;

// 导出native工具集合
export {
  Native as default,
  getSafeAreaHeight,
  Native,
  setNativeLogFunction,
} from './native';

/**
 * 序列化快筛条件为URL参数字符串
 *
 * @param filters 快筛条件数组
 * @returns 序列化后的字符串，格式：typeId1-filterId1,typeId2-filterId2
 */
export function serializeFilters(filters: API.HotelList.IFilter[]): string {
  if (!filters || filters.length === 0) return '';
  return filters.map(filter => `${filter.typeId}-${filter.filterId}`).join(',');
}

/**
 * 反序列化URL参数字符串为快筛条件数组
 *
 * @param filtersString 序列化的快筛条件字符串
 * @returns 快筛条件数组
 */
export function deserializeFilters(
  filtersString: string | null
): API.HotelList.IFilter[] {
  if (!filtersString || filtersString.trim() === '') return [];

  try {
    return filtersString.split(',').map(filterStr => {
      const [typeIdStr, filterId] = filterStr.split('-');
      const typeId = parseInt(typeIdStr, 10);

      if (isNaN(typeId) || !filterId) {
        throw new Error(`Invalid filter format: ${filterStr}`);
      }

      return {
        typeId,
        filterId: isNaN(Number(filterId)) ? filterId : Number(filterId),
      };
    });
  } catch (error) {
    console.warn('Failed to deserialize filters:', error);
    return [];
  }
}

/**
 * 更新URL参数而不刷新页面
 *
 * @param params 要更新的参数对象
 * @param router Next.js router实例
 * @param pathname 当前路径
 * @param currentSearchParams 当前URL参数
 */
export function updateUrlParams(
  params: Record<string, string | null>,
  router: { replace: (_url: string) => void },
  pathname: string,
  currentSearchParams: URLSearchParams
) {
  const newSearchParams = new URLSearchParams(currentSearchParams);

  // 更新或删除参数
  Object.entries(params).forEach(([key, value]) => {
    if (value === null || value === '' || value === undefined) {
      newSearchParams.delete(key);
    } else {
      newSearchParams.set(key, value);
    }
  });

  // 构建新URL
  const queryString = newSearchParams.toString();
  const newUrl = queryString ? `${pathname}?${queryString}` : pathname;

  // 更新URL（不刷新页面）
  router.replace(newUrl);
}

/**
 * 防抖函数
 *
 * @param fn 要执行的函数
 * @param delay 延迟时间（毫秒）
 * @returns 返回防抖处理后的函数
 */
export function debounce<T extends (..._args: any[]) => void>(
  fn: T,
  delay: number
): (..._args: Parameters<T>) => void {
  let timer: Nullable<number> = null;

  return (...args: Parameters<T>) => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay) as unknown as number;
  };
}

/**
 * 节流函数
 *
 * @param fn 要执行的函数
 * @param delay 间隔时间（毫秒）
 * @param options 配置项：{ leading: 是否立即执行, trailing: 是否在间隔结束后执行 }
 * @returns 返回节流处理后的函数
 */
export function throttle<T extends (..._args: any[]) => void>(
  fn: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = {
    leading: true,
    trailing: true,
  }
): (..._args: Parameters<T>) => void {
  let lastExecTime = 0;
  let timer: Nullable<number> = null;
  const { leading = true, trailing = true } = options;

  return (...args: Parameters<T>) => {
    const now = Date.now();

    // Leading 执行（立即执行）
    if (leading && (now - lastExecTime >= delay || lastExecTime === 0)) {
      fn(...args);
      lastExecTime = now;
    } else if (trailing) {
      // Trailing 执行（延迟执行）
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(
        () => {
          fn(...args);
          lastExecTime = now;
          timer = null;
        },
        delay - (now - lastExecTime)
      ) as unknown as number;
    }
  };
}
