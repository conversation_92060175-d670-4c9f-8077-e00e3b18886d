namespace App {
  namespace Service {
    interface IResponse<T> {
      code: number;
      message: string;
      isSuccess?: boolean;
      data: T;
    }
  }
}

/** BD-09 百度 GCJ-02 高德 WGS-84 谷歌 */
type CoordinateType = 'BD-09' | 'GCJ-02' | 'WGS-84';

interface ILocation {
  /** 经度 */
  longitude: number | string;
  /** 纬度 */
  latitude: number | string;
}

interface ICity {
  /** 城市id */
  cityId: string;
  /** 城市名称 */
  cityName: string;
}

type Nullable<T> = T | null;
