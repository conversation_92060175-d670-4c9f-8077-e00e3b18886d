# OTA Debug Tools

这是一个独立的调试工具包，与业务代码完全分离。

## 功能特性

- 🐛 **日志面板** - 实时显示控制台日志
- 📱 **响应式设计** - 支持移动端和桌面端

## 文件结构

```
src/debugTools/
├── components/          # 调试组件
│   ├── DebugWrapper.tsx     # 调试包装器
│   └── LogPanel.tsx         # 日志面板
├── config/             # 调试配置
│   └── debug.ts            # 调试配置
├── index.ts            # 统一入口
└── README.md           # 说明文档
```

## 使用方法

### 1. 安装依赖（首次使用）

```bash
# 安装 cross-env 以支持跨平台环境变量设置
npm install
```

### 2. 通过 npm script 启用调试模式

```bash
# 普通开发模式（不显示调试面板）
npm run dev

# 调试模式（显示调试面板）
npm run dev:debug
```

**重要说明**：

- 默认情况下，即使在开发环境，调试面板也不会显示
- 只有通过 `npm run dev:debug` 命令明确启用 `NEXT_PUBLIC_DEBUG_ENABLED=true` 时才会显示
- 这确保了调试面板不会意外出现在普通开发模式中

### 3. 在应用根组件中使用

```tsx
import { DebugWrapper } from '@/debugTools';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <DebugWrapper />
      </body>
    </html>
  );
}
```

### 4. 配置调试选项

```tsx
import { debugConfig } from '@/debugTools/config/debug';

// 启用调试模式
debugConfig.enabled = true;

// 配置日志面板
debugConfig.showLogPanel = true;
debugConfig.logPanelPosition = 'bottom-right';
debugConfig.maxLogs = 100;
```

## 环境变量配置

可以通过环境变量控制调试功能：

```bash
# 启用调试模式
NEXT_PUBLIC_DEBUG_ENABLED=true

# 显示日志面板
NEXT_PUBLIC_DEBUG_SHOW_LOG_PANEL=true

# 日志面板位置
NEXT_PUBLIC_DEBUG_LOG_PANEL_POSITION=bottom-right

# 日志面板尺寸
NEXT_PUBLIC_DEBUG_LOG_PANEL_WIDTH=400
NEXT_PUBLIC_DEBUG_LOG_PANEL_HEIGHT=300

# 最大日志条数
NEXT_PUBLIC_DEBUG_MAX_LOGS=1000
```

## 移除调试工具

如果需要移除调试工具，只需要：

1. 删除 `src/debugTools/` 文件夹
2. 从 `src/app/layout.tsx` 中移除 `DebugWrapper`

## 注意事项

- 调试工具仅在开发模式下启用
- 生产环境会自动禁用调试功能
- 所有调试代码与业务代码完全分离
- 可以随时替换为其他调试工具

## 版本信息

- 版本：1.0.0
- 名称：OTA Debug Tools
- 兼容性：React 18+, Next.js 15+
