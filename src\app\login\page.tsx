import ServerNavbar from '../../components/navbar/ServerNavbar';
import { getImageUrl } from '../../utils';

import LoginForm from './form';

const LoginPage = () => (
  <div
    className="h-[100vh] w-[100vw] bg-text-tertiary bg-opacity-10 bg-[length:100%_100%] bg-no-repeat bg-center"
    style={{
      backgroundImage: `url(${getImageUrl('login/bg.png')})`,
    }}
  >
    <ServerNavbar className="!bg-transparent" />
    <div className="mt-[30px] px-[24px]">
      <div className="mb-[52px]">
        <div className="text-center text-[32px] font-bold text-bg-card">
          登录/注册
        </div>
        <div className="text-center text-[12px] text-bg-card">登录后更精彩</div>
      </div>

      {/* 表单区域 - 客户端组件 */}
      <LoginForm />
    </div>
  </div>
);

export default LoginPage;
