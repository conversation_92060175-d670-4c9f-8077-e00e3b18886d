import ServerNavbar from '../../../components/navbar/ServerNavbar';

interface AgreementNavbarWrapperProps {
  /** 页面标题 */
  title: string;
  /** 子组件 */
  children: React.ReactNode;
}

/** 协议页面 Navbar 包装器 - 根据是否是自己的壳来控制显示 */
export const AgreementNavbarWrapper: React.FC<AgreementNavbarWrapperProps> = ({
  title,
  children,
}) => (
  // 检查是否是自己的壳
  <>
    {/* 只有在非自己的壳时才显示 Navbar */}
    <ServerNavbar fixed title={title} />

    {/* 页面内容 */}
    <div>{children}</div>
  </>
);
