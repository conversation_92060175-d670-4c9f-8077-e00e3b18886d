'use client';

import { useCallback } from 'react';

import { useSearchParams } from 'next/navigation';

import Native from '@/utils/native';

const HomeGoBack = () => {
  const searchParams = useSearchParams();
  const from = searchParams.get('from');

  const height = Native.getSafeAreaHeight();

  const handleGoBack = useCallback(() => {
    if (from === 'dreame') {
      window.location.href = 'https://app.dreame.tech/';
      return;
    }
    if (Native.isInNativeApp()) {
      Native.closePage();
      return;
    }
    window.history.back();
  }, []);
  if (!Native.isInNativeApp() && from !== 'dreame') {
    return null;
  }
  return (
    <i
      className="iconfont icon-a-Property1fanhui text-white font-bold !text-[24px]"
      style={{ top: `${height + 5}px` }}
      onClick={() => handleGoBack()}
    />
  );
};
export default HomeGoBack;
