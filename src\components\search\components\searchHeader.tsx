'use client';

import { useState } from 'react';

import { getImageUrl } from '@/utils';

import type { SearchHeaderProps } from '../types';

export const SearchHeader: React.FC<SearchHeaderProps> = ({
  keyword,
  onKeywordChange,
  onSearch,
  placeholder = '位置/品牌/酒店名',
  disabled = false,
  onClose,
}) => {
  const [inputValue, setInputValue] = useState(keyword);
  const [isComposing, setIsComposing] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;
    const { value } = e.target;
    setInputValue(value);

    console.warn('SearchHeader: Input change', { value, isComposing });
    // 只有在非组合输入状态下才触发关键词变化，避免中文输入过程中的多次调用
    if (!isComposing) {
      console.warn('SearchHeader: Triggering keyword change', value);
      onKeywordChange(value);
    } else {
      console.warn('SearchHeader: Skipping keyword change (composing)', value);
    }
  };

  // 处理中文输入法开始组合输入
  const handleCompositionStart = () => {
    console.warn('SearchHeader: Composition start');
    setIsComposing(true);
  };

  // 处理中文输入法结束组合输入
  const handleCompositionEnd = (
    e: React.CompositionEvent<HTMLInputElement>
  ) => {
    console.warn('SearchHeader: Composition end');
    setIsComposing(false);
    const { value } = e.currentTarget;
    setInputValue(value);
    console.warn(
      'SearchHeader: Triggering keyword change after composition',
      value
    );
    onKeywordChange(value);
  };

  const handleSearch = () => {
    if (disabled) return;
    onSearch(inputValue);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleCancel = () => {
    onClose?.();
  };

  const handleClear = () => {
    if (disabled) return;
    setInputValue('');
    onKeywordChange('');
  };

  return (
    <div className="flex items-center bg-bg-card px-4 py-3 border-b border-bg-light">
      {/* 搜索框容器 */}
      <div className="flex-1 flex items-center bg-bg-light rounded-lg px-3 py-2 mr-3">
        {/* 搜索图标 */}
        <img
          src={getImageUrl('search/search.png')}
          alt="酒店"
          className="w-[18px] h-[18px] mx-[8px]"
        />

        {/* 搜索输入框 */}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          onCompositionStart={handleCompositionStart}
          onCompositionEnd={handleCompositionEnd}
          placeholder={placeholder}
          disabled={disabled}
          className={`flex-1 bg-transparent text-sm outline-none ${
            disabled
              ? 'text-text-muted placeholder-text-muted cursor-not-allowed'
              : 'text-text-primary placeholder-text-tertiary'
          }`}
        />

        {/* 清除按钮 */}
        {inputValue && !disabled && (
          <button
            onClick={handleClear}
            className="bg-[#F0F0F0] rounded-[20px] h-[24px] w-[24px] ml-[8px]"
          >
            <img
              src={getImageUrl('search/close.png')}
              alt="酒店"
              className="w-[12px] h-[12px] mx-[8px]"
            />
          </button>
        )}
      </div>

      {/* 取消按钮 */}
      <button
        onClick={handleCancel}
        className="text-sm text-gray-600 hover:text-gray-800 px-2"
      >
        取消
      </button>
    </div>
  );
};
