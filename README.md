# Dreame WebApp

基于 Next.js 15 + React 19 + TypeScript 5 的现代化 Web 应用。

## 技术栈

- **框架**: Next.js 15.3.2
- **UI 库**: React 19
- **语言**: TypeScript 5
- **样式**: Tailwind CSS 4
- **代码规范**: ESLint 9 + Prettier
- **包管理**: pnpm

## 开发环境设置

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
# 普通开发模式
pnpm dev

# 带 Mock 数据的开发模式
pnpm dev:mock
```

### 3. 代码规范检查

```bash
# 检查代码规范
pnpm lint

# 自动修复可修复的问题
pnpm lint --fix

# 类型检查
pnpm typecheck
```

## ESLint 配置说明

本项目使用 ESLint 9 的最新 flat config 格式 (`eslint.config.ts`)，包含以下特性：

### 🔧 配置特点

- **ESLint 9**: 使用最新的 flat config 格式
- **TypeScript 支持**: 完整的 TypeScript 类型检查和规则
- **React 19 优化**: 针对 React 19 的最新特性优化
- **Next.js 兼容**: 支持 Next.js App Router 和页面组件
- **Prettier 集成**: 代码格式化与 ESLint 规则协调
- **导入排序**: 自动排序和分组导入语句
- **无障碍检查**: 包含 jsx-a11y 规则确保可访问性

### 📋 包含的规则集

1. **JavaScript 基础规则**: ESLint 推荐规则
2. **TypeScript 规则**: 类型安全、一致性检查
3. **React 规则**: 组件最佳实践、Hooks 规则
4. **导入规则**: 模块导入顺序和规范
5. **代码质量规则**: 避免常见错误和反模式
6. **代码风格规则**: 统一的代码风格

### 🎯 主要规则说明

#### TypeScript 规则

- 强制使用 `type` 导入类型
- 禁止使用 `any`（警告级别）
- 优先使用 nullish coalescing (`??`)
- 强制使用 `interface` 定义类型

#### React 规则

- 组件必须使用箭头函数定义
- 禁止在 JSX 中使用数组索引作为 key
- 自动检测 React 版本
- 支持 React 19 的新特性

#### 导入规则

- 自动排序导入语句
- 分组：内置模块 → 外部依赖 → 内部模块
- React 相关导入优先级最高

### 🔧 VSCode 集成

项目已配置 VSCode 设置 (`.vscode/settings.json`)：

- 保存时自动格式化
- 保存时自动修复 ESLint 错误
- 自动组织导入语句
- 启用 ESLint flat config 支持

### 📦 推荐的 VSCode 扩展

- ESLint
- Prettier - Code formatter
- TypeScript Importer
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Path Intellisense

## 项目结构

```
src/
├── app/                 # Next.js App Router 页面
├── components/          # 可复用组件
├── context/            # React Context
├── types/              # TypeScript 类型定义
│   ├── global.d.ts     # 全局类型声明
│   ├── api.d.ts        # API 类型
│   └── app.d.ts        # 应用类型
├── utils/              # 工具函数
├── const/              # 常量定义
└── styles/             # 样式文件
```

## 脚本命令

```bash
# 开发
pnpm dev                # 启动开发服务器
pnpm dev:mock          # 启动带 Mock 的开发服务器

# 构建
pnpm build             # 构建生产版本
pnpm start             # 启动生产服务器

# 代码质量
pnpm lint              # ESLint 检查
pnpm typecheck         # TypeScript 类型检查

# 工具
pnpm cleanup           # 清理项目
pnpm commit            # 交互式提交
pnpm release           # 发布版本
```

## Git Hooks

项目配置了 Git Hooks：

- **pre-commit**: 运行类型检查和 lint-staged
- **commit-msg**: 验证提交信息格式

## 贡献指南

1. 确保代码通过 ESLint 检查
2. 确保 TypeScript 类型检查通过
3. 遵循项目的代码风格规范
4. 提交前运行 `pnpm lint` 和 `pnpm typecheck`

## 许可证

MIT
