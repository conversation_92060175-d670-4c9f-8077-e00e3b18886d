'use client';

import { useRouter, useSearchParams } from 'next/navigation';

import { Tabs, type TabItem } from '@/components';
import { tracker } from '@/utils/track';

interface OrderTabsClientProps {
  /** Tab 项列表 */
  items: TabItem[];
  /** 当前激活的 tab key */
  activeKey?: string;
}

/** 订单页面客户端Tabs组件 */
export const OrderTabsClient: React.FC<OrderTabsClientProps> = ({
  items,
  activeKey,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleTabChange = (key: string) => {
    // Tab切换埋点
    tracker({
      eventType: 'click',
      extra: {
        action: 'order_tab_change',
        fromTab: activeKey,
        toTab: key,
        tabLabel: items.find(item => item.key === key)?.label || key,
      },
    });

    const params = new URLSearchParams(searchParams);
    params.set('tab', key);
    router.replace(`?${params.toString()}`);
  };

  return (
    <div className="bg-white px-4">
      <Tabs
        items={items}
        activeKey={activeKey}
        defaultActiveKey="all"
        onTabChange={handleTabChange}
      />
    </div>
  );
};

export default OrderTabsClient;
