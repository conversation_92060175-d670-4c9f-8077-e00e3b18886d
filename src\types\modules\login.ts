declare namespace API.Login {
  /** 发送验证码请求参数 */
  interface ISendCodeRequest {
    /** 手机号 */
    mobile: string;
    /** 短信类型 */
    smsType: string;
  }

  /** 发送验证码响应 */
  interface ISendCodeResponse {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: null;
  }

  /** 登录请求参数 */
  interface ILoginRequest {
    /** 手机号 */
    mobile: string;
    /** 验证码 */
    smsCode: string;
    /** 登录类型 */
    loginType: string;
  }

  /** 登录响应 */
  interface ILoginResponse {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 用户认证token */
    token?: string;
    /** 响应数据 */
    data: null;
  }
}
