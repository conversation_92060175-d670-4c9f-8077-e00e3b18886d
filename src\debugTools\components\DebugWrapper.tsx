'use client';

import { debugConfig } from '@/debugTools/config/debug';

import { LogPanel } from './LogPanel';

/** 客户端调试组件 显示调试日志面板 */
export const DebugWrapper: React.FC = () => (
  <>
    {/* 调试日志面板 - 仅在启用时显示 */}
    {debugConfig.enabled && debugConfig.showLogPanel && (
      <LogPanel
        visible
        position={debugConfig.logPanelPosition}
        width={debugConfig.logPanelWidth}
        height={debugConfig.logPanelHeight}
        maxLogs={debugConfig.maxLogs}
      />
    )}
  </>
);

export default DebugWrapper;
