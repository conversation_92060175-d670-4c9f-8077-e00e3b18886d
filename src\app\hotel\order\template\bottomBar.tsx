'use client';
import { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';

import { PaymentSheet, Toast } from '@/components';
import ActionSheet from '@/components/actionSheet';
import Dialog from '@/components/dialog';
import type { AppDispatch, RootState } from '@/store';
import {
  createOrderApi,
  getPriceInfo,
  setCreateOrderLoading,
} from '@/store/hotel/order';
import { calculateDaysBetweenDates } from '@/utils/tools';

interface BottomBarProps {
  [key: string]: any;
}

const BottomBar: React.FC<BottomBarProps> = ({ data }) => {
  const {
    inDate,
    outDate,
    hotelId,
    hotelCode,
    roomTypeId,
    payType,
    ratePlanId,
    priceLimitedType,
    isPriceLimittedProduct,
    littleMajiaId,
    goodsUniqId,
    roomId,
    roomKey,
    hotelName,
  } = data;
  const dispatch = useDispatch<AppDispatch>();
  // const router = useRouter();
  const tourInfo = useSelector((state: RootState) => state.hotelOrder.tourInfo);
  const priceInfo = useSelector(
    (state: RootState) => state.hotelOrder.priceInfo
  );
  const getPriceLoading = useSelector(
    (state: RootState) => state.hotelOrder.getPriceLoading
  );
  const createOrderLoading = useSelector(
    (state: RootState) => state.hotelOrder.createOrderLoading
  );

  const { totalPrice, prepayResult, bookingDetailInfos } = priceInfo;
  const { roomNum, tourList, linkPhone } = tourInfo;

  const [priceDetailvisible, setPriceDetailvisible] = useState(false);
  const handlePriceDeatilPop = () => {
    setPriceDetailvisible(!priceDetailvisible);
  };
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogMessage, setDialogMessage] = useState('');
  const handleDialogPop = (flag: boolean, text: any) => {
    setDialogVisible(flag);
    setDialogMessage(text);
  };
  const [paymentSheetvisible, setPaymentSheetvisible] = useState(false);
  const [payInfoDetail, setPayInfoDetail] = useState({
    orderNo: '',
    totalAmount: 0,
    lastPayTime: 1800000,
  });

  // 初始化价格信息
  useEffect(() => {
    dispatch(
      getPriceInfo({
        arrivalDate: inDate,
        departureDate: outDate,
        hotelId,
        hotelCode,
        roomTypeId,
        ratePlanId,
        paymentType: payType,
        numberOfRooms: 1,
        priceLimitedType,
        isPriceLimittedProduct,
        littleMajiaId,
        goodsUniqId,
      })
    ).catch(error => console.error('初始化失败:', error));
  }, []);

  // 提交订单
  const SubcreateOrder = async () => {
    if (getPriceLoading || createOrderLoading) {
      Toast('请稍后重试');
      return;
    }

    let customers: Array<{
      name: string;
      idCardNo: string;
      certificateType: string;
    }> = [];
    let contactName: string = '';

    if (tourList?.length > 0) {
      customers = tourList.map(item => ({
        name: item.name || '', // 添加默认值
        idCardNo: item.cardNo || '', // 添加默认值
        certificateType: '', // 根据业务需求可能需要设置默认值
      }));

      contactName = tourList[0]?.name || ''; // 安全访问
    }

    const createOrderParams = {
      arrivalDate: inDate,
      departureDate: outDate,
      customerIPAddress: window.origin,
      numberOfRooms: Number(roomNum),
      totalPrice,
      customers,
      contactName,
      contactPhone: linkPhone,
      hotelBaseInfo: {
        hotelId,
        cancelPolicy: prepayResult.cancelDescription,
      },
      roomBaseInfo: {
        roomId,
        roomTypeId,
        ratePlanId,
      },
      roomKey,
    };

    try {
      const action = await dispatch(createOrderApi(createOrderParams));

      if (createOrderApi.fulfilled.match(action)) {
        // 成功情况
        const { orderNo, totalAmount, lastPayTime } = action.payload; // 直接解构
        setPaymentSheetvisible(true);
        setPayInfoDetail({ orderNo, totalAmount, lastPayTime });

        // router.push(`/hotel/orderDetail?orderNo=${orderNo}`);
      } else if (createOrderApi.rejected.match(action)) {
        // 错误情况
        handleDialogPop(true, action.payload || '');
      }
    } catch (error) {
      // 处理意外错误
      handleDialogPop(true, '');
      console.error('Unexpected error:', error);
    }
  };

  const handlePaymentConfirm = async () => {
    setPaymentSheetvisible(false);
    // router.push(`/hotel/orderDetail?orderNo=${payInfoDetail.orderNo}`);
    window.location.href = `/hotel/orderDetail?orderNo=${payInfoDetail.orderNo}`;
    dispatch(setCreateOrderLoading(false));
  };

  return (
    <div className="fixed bottom-0 w-full z-[100] bg-white pb-[env(safe-area-inset-bottom)]">
      <div className="h-[64px] pt-[8px] px-[16px] flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-text-primary leading-none font-medium text-[14px]">
            在线支付
          </span>
          {getPriceLoading ? (
            <span className="text-text-primary leading-none font-bold text-[24px]">
              --
            </span>
          ) : (
            <p>
              <span className="text-text-primary ml-[10px] leading-none text-[10px]">
                ￥
              </span>
              <span className="text-text-primary font-bold leading-none text-[24px]">
                {totalPrice}
              </span>
            </p>
          )}
        </div>
        <div className="flex items-center">
          <div className="flex items-center" onClick={handlePriceDeatilPop}>
            <span className="text-[12px] text-text-tertiary">明细</span>
            <img
              className={`w-[12px] h-[12px] ml-[4px] mr-[8px] ${priceDetailvisible && 'rotate-180'}`}
              src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/wx-mini-ota/hoteList/order/up_icon.png"
              alt=""
            />
          </div>

          <div
            className={`w-[113px] h-[48px] bg-btn-primary-bg rounded-[60px] font-medium text-[16px] text-btn-primary-text text-center leading-[48px] hover:bg-btn-primary-hover active:bg-btn-primary-active transition-all duration-200 cursor-pointer ${(getPriceLoading || createOrderLoading) && 'opacity-30'}`}
            onClick={SubcreateOrder}
          >
            {createOrderLoading ? '提交中···' : '提交订单'}
          </div>
        </div>
      </div>

      {/* 明细 */}
      {bookingDetailInfos?.length ? (
        <ActionSheet
          visible={priceDetailvisible}
          title="费用明细"
          showCancel={false}
          className="bottom-[64px]"
          onClose={() => setPriceDetailvisible(false)}
        >
          <div className="px-[16px] max-h-[60vh] overflow-y-auto [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            <div className="flex justify-between">
              <p className="text-[18px] text-text-primary font-medium">
                订单金额
              </p>
              <p>
                <span className="text-[14px] text-text-secondary mr-[8px] font-medium">
                  {calculateDaysBetweenDates(inDate, outDate)}晚、{roomNum}
                  间共
                </span>
                <span className="text-[10px] text-text-primary mr-[2px]">
                  ￥
                </span>
                <span className="text-[24px] text-text-primary font-blod">
                  {totalPrice}
                </span>
              </p>
            </div>
            <div className="mt-[12px]">
              {bookingDetailInfos?.map((item: any, index: number) => (
                <div className="flex justify-between mb-[8px]" key={index}>
                  <p className="text-[12px] text-[#33333E]">
                    {item.checkInDate} {item.mealDesc}
                  </p>
                  <p className="text-[12px] text-[#000000]">
                    {item.price && <span>￥{item.price}</span>}
                    {item.numberOfRooms > 0 && (
                      <span>x{item.numberOfRooms}</span>
                    )}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </ActionSheet>
      ) : null}

      {/* 下单失败弹窗 */}
      <Dialog
        visible={dialogVisible}
        title="下单失败"
        cancelText="确认"
        hiddenConfirmBtm
        onCancel={() => handleDialogPop(false, '')}
      >
        {dialogMessage || '服务器开小差了～'}
      </Dialog>

      {/* 半屏支付 */}
      <PaymentSheet
        visible={paymentSheetvisible}
        onClose={handlePaymentConfirm}
        orderId={payInfoDetail.orderNo}
        goodsName={hotelName}
        orderAmount={String(payInfoDetail.totalAmount)}
        lastPayTime={payInfoDetail.lastPayTime}
        onPaymentConfirm={handlePaymentConfirm}
      />
    </div>
  );
};

export default BottomBar;
