import { cookies } from 'next/headers';

import { CookiesKeyEnum } from '@/enum';
import { makeStore } from '@/store';
import { getOrderDetail } from '@/store/hotel/orderDetail';

import ServerNavbar from '../../../components/navbar/ServerNavbar';

import PullToRefresh from './template/pageResh';

export default async function OrderDetailPage({
  searchParams,
}: {
  searchParams: Promise<API.HotelOrderDetails.OrderDetailProps>;
}) {
  // 获取链接参数
  const params = await searchParams;
  const { orderNo } = params;

  // 获取服务端 cookies
  const cookieStore = await cookies();
  const token = cookieStore.get(CookiesKeyEnum.AUTH_TOKEN)?.value || '';

  // store
  const store = makeStore();

  try {
    const actionResult = await store.dispatch(
      getOrderDetail({ orderNo, token })
    );

    const res = actionResult?.payload;
    if (!res) {
      throw new Error('No payload received from getOrderDetail');
    }

    return (
      <div className="w-screen h-screen bg-[#F9F9F9]">
        <ServerNavbar fixed />
        <PullToRefresh data={res} />
      </div>
    );
  } catch (error) {
    console.error('Failed to fetch order details:', error);
    throw error;
  }
}
