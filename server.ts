import { createServer } from 'http';

import dotenv from 'dotenv';
import next from 'next';

dotenv.config();

if (process.env.NODE_ENV === 'production') {
  void import('skywalking-backend-js').then(({ default: agent }) => {
    agent.start({
      serviceName: 'ota-app-web',
      collectorAddress: 'tracing-analysis-dc-hz-internal.aliyuncs.com:8000',
      authorization: 'hba4vsd3v1@bb1eba44b0ad7fb_hba4vsd3v1@53df7ad2afe8301',
    });
  });
}

const port = parseInt(process.env.PORT ?? '3000', 10);
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

void app.prepare().then(() => {
  createServer((req, res) => {
    void handle(req, res);
  }).listen(port, () => {
    console.warn(`> Ready on http://localhost:${port}`);
  });
});
