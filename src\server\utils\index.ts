/** 服务端组件工具类 */

import { cookies, headers } from 'next/headers';

import { CookiesKeyEnum } from '@/enum';
import Native from '@/utils/native';

/**
 * 服务器端获取的token
 *
 * @returns Token
 */
export const getServerToken = async () => {
  const cookieStore = await cookies();
  return cookieStore.get(CookiesKeyEnum.AUTH_TOKEN)?.value ?? '';
};

/**
 * 服务器端获取UserAgent
 *
 * @returns UserAgent字符串
 */
export const getServerUserAgent = async (): Promise<string> => {
  const headersList = await headers();
  return headersList.get('user-agent') || '';
};

/**
 * 服务器端获取安全区域高度
 *
 * @returns 安全区域高度（像素）
 */
export const getServerSafeAreaHeight = async (): Promise<number> => {
  const userAgent = await getServerUserAgent();
  return Native.getSafeAreaHeight(userAgent);
};

/**
 * 服务端组件工具类-通过UA判断是否在Dreame App内
 *
 * @returns Promise<boolean>
 */
export const isInDreameApp = async () => {
  try {
    const headersList = await headers();
    return headersList.get('user-agent')?.includes('_DreameHome_');
  } catch (_error) {
    return false;
  }
};

/**
 * 服务端设备检测工具 通过请求头中的 user-agent 判断设备类型
 *
 * @param userAgent 请求头中的 user-agent 字符串
 * @returns 'ios' | 'android' | 'other'
 */
export const detectDevice = async () => {
  const headersList = await headers();
  const userAgent = headersList.get('user-agent');

  if (!userAgent) return 'other';

  const isIOS = /iPhone|iPad|iPod/i.test(userAgent);
  const isAndroid = /Android/i.test(userAgent);

  return isIOS ? 'ios' : isAndroid ? 'android' : 'other';
};
