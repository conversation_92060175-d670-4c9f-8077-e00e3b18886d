{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "react", "incremental": true, "tsBuildInfoFile": ".next/cache/tsconfig.tsbuildinfo", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": ["node"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts"], "exclude": ["node_modules", "src/app/test"]}