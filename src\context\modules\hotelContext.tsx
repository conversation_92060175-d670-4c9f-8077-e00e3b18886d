'use client';
import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';

import dayjs from 'dayjs';

import { DEFAULT_CITY_INFO } from '@/config';

import { StorageKeyEnum } from '../../enum';

interface HotelContextType {
  cityInfo: ICity;
  /** 入住/离店日期 */
  hotelDates: [string, string];
  /** 酒店关键字 */
  keyword: string;
  /** 更新酒店入离店日期 */
  updateHotelDates: (_dates: [string, string]) => void;
  /** 更新酒店搜索关键字 */
  updateKeyword: (_keyword: string) => void;
  /** 更新酒店选择城市 */
  updateHotelCityInfo: (_cityInfo: ICity) => void;
}

const HotelContext = createContext<HotelContextType | undefined>(undefined);

export const HotelProvider = ({ children }: { children: ReactNode }) => {
  const currentDate = dayjs();

  /** 默认入离日期 */
  const [defaultStartDate, defaultEndDate] = [
    currentDate.format('YYYY-MM-DD'),
    currentDate.add(1, 'day').format('YYYY-MM-DD'),
  ];
  // 初始化时使用默认值，避免SSR水合不匹配
  const [hotelDates, setHotelDates] = useState<[string, string]>([
    currentDate.format('YYYY-MM-DD'),
    currentDate.add(1, 'day').format('YYYY-MM-DD'),
  ]);
  const [keyword, setKeyword] = useState('');
  const [cityInfo, setHotelCityInfo] = useState<ICity>({
    ...DEFAULT_CITY_INFO,
  });

  // 在客户端水合完成后从localStorage读取数据
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedDates = localStorage.getItem(StorageKeyEnum.HOTEL_DATES);
      if (storedDates) {
        try {
          /** 缓存入职日期 */
          const [storedStartDate, storedEndDate] = JSON.parse(storedDates);
          setHotelDates(
            /** 校验缓存入离日期的有效性 */
            defaultStartDate > storedStartDate
              ? [defaultStartDate, defaultEndDate]
              : [storedStartDate, storedEndDate]
          );
        } catch (_e) {
          console.log(_e);
        }
      }

      const storedKeyword = localStorage.getItem(StorageKeyEnum.HOTEL_KEYWORD);
      if (storedKeyword) {
        try {
          setKeyword(JSON.parse(storedKeyword));
        } catch (_e) {
          console.log(_e);
        }
      }

      const storedCityInfo = localStorage.getItem(
        StorageKeyEnum.HOTEL_CITY_INFO
      );
      if (storedCityInfo) {
        try {
          setHotelCityInfo(JSON.parse(storedCityInfo));
        } catch (_e) {
          console.log(_e);
        }
      }
    }
  }, []);

  const updateHotelDates = (dates: [string, string]) => {
    setHotelDates(dates);
  };

  const updateHotelCityInfo = (cityInfo: ICity) => {
    setHotelCityInfo(cityInfo);
    setKeyword('');
  };
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        StorageKeyEnum.HOTEL_DATES,
        JSON.stringify(hotelDates)
      );
    }
  }, [hotelDates]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        StorageKeyEnum.HOTEL_KEYWORD,
        JSON.stringify(keyword)
      );
    }
  }, [keyword]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(
        StorageKeyEnum.HOTEL_CITY_INFO,
        JSON.stringify(cityInfo)
      );
    }
  }, [cityInfo]);

  return (
    <HotelContext.Provider
      value={{
        hotelDates,
        keyword,
        cityInfo,
        updateHotelDates,
        updateKeyword: setKeyword,
        updateHotelCityInfo,
      }}
    >
      {children}
    </HotelContext.Provider>
  );
};

export const useHotel = () => {
  const context = useContext(HotelContext);
  if (context === undefined) {
    throw new Error('useHotel must be used within a HotelProvider');
  }
  return context;
};
