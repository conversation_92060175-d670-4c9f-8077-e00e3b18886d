import { request } from '@/utils/request';

/** 获取订单列表 */
export const getOrderList = (
  params: Order.IOrderListReq,
  token?: string
): Promise<App.Service.IResponse<Order.IOrderItem[]>> =>
  request<Order.IOrderItem[]>('/hotel/order/orderList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: params,
    params: token ? { token } : undefined,
  });

/** 取消订单 */
export const cancelOrder = (
  orderNo: string
): Promise<App.Service.IResponse<boolean>> =>
  request<boolean>('/hotel/order/cancelOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: { orderNo },
  });

/** 删除订单 */
export const deleteOrder = (params: { orderNo: string }) =>
  request<API.HotelBaseInfo.IHotelBaseInfoFetchResponse>(
    `/hotel/order/deleteOrder`,
    {
      params,
    }
  );

/** 支付订单 */
export const payOrder = (
  orderId: string
): Promise<App.Service.IResponse<{ payUrl: string }>> =>
  request<{ payUrl: string }>('/hotel/order/pay', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: { orderId },
  });

/** 获取订单详情 */
export const getOrderDetail = (
  orderId: string
): Promise<App.Service.IResponse<Order.IOrderItem>> =>
  request<Order.IOrderItem>('/hotel/order/detail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: { orderId },
  });
