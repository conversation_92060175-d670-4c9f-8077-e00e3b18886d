// 业务模块类型声明模板
// 复制此文件并重命名为具体的业务模块名称
// 例如：product.d.ts, order.d.ts, payment.d.ts 等

declare namespace API.HotelBaseInfo {
  interface IHotelBaseInfoFetchResponse {
    isSuccess: boolean;
    data: IHotelBaseInfoFetchResponseData;
  }

  /** 酒店基础信息获取响应 */
  interface IHotelBaseInfoFetchResponseData {
    description: string | null;
    hotelName: string;
    roomNum: number;
    hotelOpenDate: string;
    hotelTel: string;
    checkOutTime: string;
    checkInOutDesc: string;
    checkInPolicyShowInfo: ICheckInPolicyShowInfo[] | null;
    hotelCloseTimeShowInfo: IHotelCloseTimeShowInfo[] | null;
    facilityModel: IFacilityModel;
    detailPolicyInfo: IDetailPolicyInfo[];
  }

  /** 酒店入住政策信息结构 */
  interface ICheckInPolicyShowInfo {
    desc: string[];
  }

  /** 酒店关闭时间信息结构 */
  interface IHotelCloseTimeShowInfo {
    title: string;
    desc: string;
    showRule: number;
    childAndExtraBedOutInfo: IChildAndExtraBedOutInfo | null;
  }

  /** 酒店设施信息结构 */
  interface IFacilityModel {
    hotFacList: IFacilityType[];
    morFacList: IFacilityType[];
  }

  /** 设施类型 */
  interface IFacilityType {
    facilityTypeName: string;
    facilityTypeId: number;
    hotFacList?: IFacility[];
    facList?: IFacility[];
    moreFacList?: IFacility[];
    iconUrl: string;
    anchorId: string;
  }

  /** 设施项 */
  interface IFacility {
    tagList: ITag[] | null;
    facilityName: string;
    facilityNameCn: string;
    iconUrl: string;
    facilityId: number;
    facilityItemPicList: any[] | null;
    descList: string[] | null;
  }

  /** 设施标签 */
  interface ITag {
    desc: string;
    stress: string;
  }

  /** 政策详情信息 */
  interface IDetailPolicyInfo {
    anchorId: string;
    descList: IDetailPolicyDesc[];
    firstTitle: IFirstTitle;
    extraBed: boolean | null;
  }

  /** 政策详情描述 */
  interface IDetailPolicyDesc {
    detail: IDetailContent;
    secondTitle: ISecondTitle;
    type?: number;
  }

  /** 详情内容 */
  interface IDetailContent {
    desc?: string[];
    detailDesc: IDetailDesc[];
    showRule?: number;
    childAndExtraBedOutInfo: IChildAndExtraBedOutInfo | null;
  }

  /** 详情描述 */
  interface IDetailDesc {
    info: string;
  }

  /** 一级标题 */
  interface IFirstTitle {
    iconId: string;
    iconUrl: string | null;
    showRule?: number;
    title: string;
  }

  /** 二级标题 */
  interface ISecondTitle {
    iconId: string;
    iconUrl: string;
    showRule?: number;
    title: string;
    subTitle: string | null;
    color: string | null;
  }

  /** 儿童和加床信息 */
  interface IChildAndExtraBedOutInfo {
    bedInfo?: IBedInfo;
    breakfastInfo?: IBreakfastInfo;
  }

  /** 床位信息 */
  interface IBedInfo {
    ruleList: IBedRule[];
    title: string;
  }

  /** 床位规则 */
  interface IBedRule {
    description: string;
    details: string[];
  }

  /** 早餐信息 */
  interface IBreakfastInfo {
    ruleList: IBreakfastRule[];
    title: string;
  }

  /** 早餐规则 */
  interface IBreakfastRule {
    description: string;
    details: string[];
  }

  /** 酒店基础信息页面参数 */
  interface IHotelBaseInfoPageProps {
    /** 酒店ID */
    hotelId: string;
    /** 锚点ID */
    anchor?: string;
    /** 扩展参数 */
    [key: string]: string | string[] | undefined;
  }
}
