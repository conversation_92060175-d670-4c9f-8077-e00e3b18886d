'use client';
import { useCallback, useEffect, useRef, useState } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import dayjs from 'dayjs';
import { Loading, PullRefresh } from 'react-vant';

import { getHotelList } from '@/api/hotel/hotelList';
import { HotelDatePicker } from '@/components/datePicker';
import { SearchComponent } from '@/components/search';
import { useApp, useHotel } from '@/context';
import { useLocation } from '@/hooks/useLocation';

import {
  deserializeFilters,
  getImageUrl,
  serializeFilters,
} from '../../../utils';

import HotelCard from './components/HotelCard';
import { HotelCardSkeletonList } from './components/HotelCardSkeleton';
import NavBar from './components/NavBar';
import QuickFilter from './components/QuickFilter';

/** 空状态组件 */
const EmptyState: React.FC = () => (
  <div className="flex flex-col items-center py-16">
    <img src={getImageUrl('order/empty.png')} alt="" className="w-[196px]" />
    <h3 className="text-[#000] font-bold text-[16px] mt-[10px]">
      暂无酒店数据
    </h3>
  </div>
);

const HotelListPage = () => {
  // Next.js hooks for URL management
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [hotelList, setHotelList] = useState<API.HotelList.IHotelListItem[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageIndex, setPageIndex] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // 从URL读取初始参数
  const getInitialFiltersFromUrl = useCallback(() => {
    const filtersParam = searchParams.get('filters');
    return deserializeFilters(filtersParam);
  }, [searchParams]);

  const getInitialQueryTextFromUrl = useCallback(
    () => searchParams.get('q') || '',
    [searchParams]
  );

  const [filters, setFilters] = useState<API.HotelList.IFilter[]>(
    getInitialFiltersFromUrl
  );
  const [queryText, setQueryText] = useState<string>(
    getInitialQueryTextFromUrl
  );

  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [showDatePicker, setShowPicker] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);

  // 添加ref来存储稳定的参数，避免依赖循环
  const stableParamsRef = useRef({
    filters: [] as API.HotelList.IFilter[],
    queryText: '',
  });

  // URL参数更新的防抖ref
  const urlUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    hotelDates: dates,
    updateHotelDates,
    cityInfo: hotelCityInfo,
  } = useHotel();
  const { geoInfo } = useApp();
  const { LocationComponent } = useLocation({});

  // URL参数同步函数
  const syncUrlParams = useCallback(
    (newFilters: API.HotelList.IFilter[], newQueryText: string) => {
      // 清除之前的防抖定时器
      if (urlUpdateTimeoutRef.current) {
        clearTimeout(urlUpdateTimeoutRef.current);
      }

      // 设置较短的防抖时间，让用户能更快看到URL更新
      urlUpdateTimeoutRef.current = setTimeout(() => {
        const filtersString = serializeFilters(newFilters);

        const newSearchParams = new URLSearchParams(searchParams);

        // 更新或删除参数
        if (filtersString) {
          newSearchParams.set('filters', filtersString);
        } else {
          newSearchParams.delete('filters');
        }

        if (newQueryText) {
          newSearchParams.set('q', newQueryText);
        } else {
          newSearchParams.delete('q');
        }

        // 构建新URL
        const queryString = newSearchParams.toString();
        const newUrl = queryString ? `${pathname}?${queryString}` : pathname;

        // 更新URL（不刷新页面）
        router.replace(newUrl);
      }, 100);
    },
    [router, pathname, searchParams]
  );

  // 更新稳定参数的ref
  useEffect(() => {
    stableParamsRef.current = {
      filters,
      queryText,
    };
  }, [filters, queryText]);

  // 监听URL参数变化，同步到本地状态
  useEffect(() => {
    const urlFilters = getInitialFiltersFromUrl();
    const urlQueryText = getInitialQueryTextFromUrl();

    // 只有当URL参数与当前状态不同时才更新
    const filtersChanged =
      JSON.stringify(urlFilters) !== JSON.stringify(filters);
    const queryTextChanged = urlQueryText !== queryText;

    if (filtersChanged) {
      setFilters(urlFilters);
    }

    if (queryTextChanged) {
      setQueryText(urlQueryText);
    }
  }, [searchParams, getInitialFiltersFromUrl, getInitialQueryTextFromUrl]);

  const handleShowPicker = useCallback(() => {
    setShowPicker(true);
  }, []);

  const handleClosePicker = useCallback(() => {
    setShowPicker(false);
  }, []);

  const handleDateChange = useCallback((dateList: string[]) => {
    updateHotelDates(dateList as [string, string]);
  }, []);

  const handleShowSearchModal = useCallback(() => {
    setShowSearchModal(true);
  }, []);

  const handleCloseSearchModal = useCallback(() => {
    setShowSearchModal(false);
  }, []);

  const handleSearchResultSelect = useCallback(
    (result: Search.ISearchResultItem) => {
      // 设置搜索关键词
      const newQueryText = result.itemName;
      setQueryText(newQueryText);

      // 同步URL参数
      syncUrlParams(filters, newQueryText);

      // 根据搜索结果类型处理
      switch (result.type) {
        case 5: // Hotel - 直接跳转到酒店详情
          // TODO: 跳转到酒店详情页面
          break;
        default: // 其他类型 - 使用搜索关键词重新查询酒店列表
          // 重新查询逻辑将通过useEffect监听queryText变化来触发
          break;
      }

      // 关闭搜索弹框
      setShowSearchModal(false);
    },
    [filters, syncUrlParams]
  );

  const handleCitySelect = useCallback((_city: Search.ICityInfo) => {
    // 更新城市信息
    // TODO: 更新context中的城市信息

    // 关闭搜索弹框
    setShowSearchModal(false);
  }, []);

  // 清除搜索关键词
  const handleClearSearch = useCallback(() => {
    const newQueryText = '';
    setQueryText(newQueryText);
    // 同步URL参数
    syncUrlParams(filters, newQueryText);
  }, [filters, syncUrlParams]);

  // 构建API参数的工具函数
  const buildApiParams = useCallback(
    (
      page: number,
      customFilters?: API.HotelList.IFilter[],
      customQueryText?: string
    ) => ({
      queryText: customQueryText ?? stableParamsRef.current.queryText,
      cityId: hotelCityInfo.cityId,
      cityName: hotelCityInfo.cityName,
      arrivalDate: dates[0],
      departureDate: dates[1],
      sort: 'IntelligentSort' as const,
      currentPosition: {
        longitude: Number(geoInfo.lng),
        latitude: Number(geoInfo.lat),
        coordinateType: 'GCJ-02',
        cityId: geoInfo.cityId,
        cityName: geoInfo.cityName,
      },
      filters: customFilters ?? stableParamsRef.current.filters,
      pageSize: 10,
      pageIndex: page,
    }),
    [dates, hotelCityInfo, geoInfo]
  );

  const { cityId } = hotelCityInfo; // 从hotelContext获取

  // 通用的数据获取函数
  const fetchHotelData = useCallback(
    async (
      page: number,
      resetList: boolean = false,
      customFilters?: API.HotelList.IFilter[],
      customQueryText?: string
    ) => {
      try {
        const params = buildApiParams(page, customFilters, customQueryText);
        const { code, data, message } = await getHotelList(params);

        if (code === 200 && data?.hotels) {
          if (resetList) {
            setHotelList(data.hotels);
            setPageIndex(page);
          } else {
            setHotelList(prev => [...prev, ...data.hotels]);
            setPageIndex(page);
          }
          setHasMore(data.hasMore ?? false);
          return true;
        } else {
          setError(`获取酒店列表失败: ${message}`);
          return false;
        }
      } catch (error) {
        console.error('获取酒店数据失败:', error);
        setError('数据获取失败，请稍后重试');
        return false;
      }
    },
    [buildApiParams]
  );

  // 处理筛选条件变化
  const handleFilterChange = useCallback(
    async (newFilters: API.HotelList.IFilter[]) => {
      setFilters(newFilters);
      setIsLoading(true);
      setError(null);

      // 同步URL参数
      syncUrlParams(newFilters, queryText);

      const success = await fetchHotelData(1, true, newFilters);
      if (!success) {
        // 如果失败，恢复之前的筛选条件
        setFilters(stableParamsRef.current.filters);
      }

      setIsLoading(false);
    },
    [fetchHotelData, queryText, syncUrlParams, filters]
  );

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setIsRefreshing(true);
    setError(null);

    await fetchHotelData(1, true);
    setIsRefreshing(false);
  }, [fetchHotelData]);

  // 加载更多数据 - 移除对hotelListParams的依赖
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore || isRefreshing || isLoading) return;

    setIsLoadingMore(true);
    const nextPage = pageIndex + 1;
    await fetchHotelData(nextPage, false);
    setIsLoadingMore(false);
  }, [
    hasMore,
    isLoadingMore,
    isRefreshing,
    isLoading,
    pageIndex,
    fetchHotelData,
  ]);

  // 使用 Intersection Observer 监听底部元素
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (
          entries[0].isIntersecting &&
          hasMore &&
          !isLoading &&
          !isLoadingMore &&
          !isRefreshing
        ) {
          void loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoading, isLoadingMore, isRefreshing, loadMore]);

  // 初始数据加载 - 只在组件挂载时执行
  useEffect(() => {
    const initData = async () => {
      setIsLoading(true);
      setError(null);
      // 使用URL参数初始化数据加载
      await fetchHotelData(
        1,
        true,
        getInitialFiltersFromUrl(),
        getInitialQueryTextFromUrl()
      );
      setIsLoading(false);
    };

    void initData();
  }, []); // 移除所有依赖，只在挂载时执行

  // 监听日期变化
  useEffect(() => {
    const fetchDataOnDateChange = async () => {
      // 避免初始化时重复调用
      const initialDates = [
        dayjs().format('YYYY-MM-DD'),
        dayjs().add(1, 'day').format('YYYY-MM-DD'),
      ];

      if (dates[0] === initialDates[0] && dates[1] === initialDates[1]) {
        return; // 跳过初始日期
      }

      setIsLoading(true);
      setError(null);
      await fetchHotelData(1, true);
      setIsLoading(false);
    };

    void fetchDataOnDateChange();
  }, [dates, fetchHotelData]);

  // 监听搜索关键词变化
  useEffect(() => {
    const fetchDataOnQueryChange = async () => {
      setIsLoading(true);
      setError(null);
      await fetchHotelData(1, true, undefined, queryText);
      setIsLoading(false);
    };

    // 只有在搜索关键词真正变化时才查询（跳过空字符串的初始状态）
    const isInitialEmptyQuery = queryText === '' && hotelList.length === 0;
    if (!isInitialEmptyQuery) {
      void fetchDataOnQueryChange();
    }
  }, [queryText, fetchHotelData]); // 移除hotelList.length依赖

  // 清理防抖定时器
  useEffect(
    () => () => {
      if (urlUpdateTimeoutRef.current) {
        clearTimeout(urlUpdateTimeoutRef.current);
      }
    },
    []
  );

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar
        cityId={hotelCityInfo.cityId}
        cityName={hotelCityInfo.cityName}
        arrivalDate={dates[0]}
        departureDate={dates[1]}
        queryText={queryText} // 传递搜索关键词
        onDateSelect={handleShowPicker}
        onSearch={handleShowSearchModal}
        onClearSearch={handleClearSearch}
      />
      {/* 快筛区域 */}
      <QuickFilter
        cityId={cityId}
        selectedFilters={filters}
        onFilterChange={handleFilterChange}
      />

      {/* 酒店列表 - 添加顶部间距避免被固定组件遮挡 */}
      <div className="px-2 pt-[54px]">
        <HotelDatePicker
          visible={showDatePicker}
          value={dates}
          onSelect={handleDateChange}
          onClose={handleClosePicker}
        />
        <PullRefresh onRefresh={onRefresh}>
          {isLoading ? (
            <HotelCardSkeletonList count={8} />
          ) : isRefreshing ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : hotelList.length > 0 ? (
            <>
              {hotelList.map(hotel => (
                <HotelCard key={hotel.hotelId} hotel={hotel} />
              ))}

              {/* 底部加载状态 */}
              <div
                ref={loadMoreRef}
                className="flex items-center justify-center py-6"
              >
                {isLoadingMore ? (
                  <div className="flex items-center gap-2">
                    <Loading color="#8c6533" size="16px">
                      加载中...
                    </Loading>
                  </div>
                ) : (
                  !hasMore && (
                    <span className="text-[#66666e] text-sm">没有更多了</span>
                  )
                )}
              </div>
            </>
          ) : (
            <EmptyState />
          )}
        </PullRefresh>
      </div>

      {/* 搜索弹框 */}
      <SearchComponent
        visible={showSearchModal}
        onClose={handleCloseSearchModal}
        searchParams={{
          cityId: hotelCityInfo.cityId,
          cityName: hotelCityInfo.cityName,
          arrivalDate: dates[0],
          departureDate: dates[1],
        }}
        currentCity={{
          cityId: geoInfo.cityId,
          cityName: geoInfo.cityName,
          address: geoInfo.address,
          districtName: geoInfo.districtName,
          isUserLocation: Boolean(geoInfo.lastUpdatedTime),
        }}
        LocationComp={LocationComponent}
        onSearchResultSelect={handleSearchResultSelect}
        onCitySelect={handleCitySelect}
        showCitySection={false}
        enableSearch
        directSearch
        initialKeyword={queryText}
        placeholder="位置/品牌/酒店名"
      />
    </div>
  );
};

export default HotelListPage;
