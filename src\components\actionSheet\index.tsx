'use client';

import React, { useEffect, useState, type ReactNode } from 'react';

export interface ActionSheetAction {
  /** 选项名称 */
  name: string;
  /** 选项值 */
  value?: string | number;
  /** 选项颜色 */
  color?: string;
  /** 是否为加载状态 */
  loading?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 选项子标题 */
  subname?: string;
  /** 选项类名 */
  className?: string;
  /** 是否展示遮罩层 */
  showMask?: boolean;
}

export interface ActionSheetProps {
  /** 是否显示动作面板 */
  visible: boolean;
  /** 面板标题 */
  title?: string;
  /** 取消按钮文字 */
  cancelText?: string;
  /** 选项列表 */
  actions?: ActionSheetAction[];
  /** 面板描述信息 */
  description?: string;
  /** 是否显示取消按钮 */
  showCancel?: boolean;
  /** 是否在点击选项后关闭 */
  closeOnClickAction?: boolean;
  /** 是否在点击遮罩层后关闭 */
  closeOnClickOverlay?: boolean;
  /** 是否开启底部安全区适配 */
  safeAreaInsetBottom?: boolean;
  /** 动画时长，单位毫秒 */
  duration?: number;
  /** 自定义类名 */
  className?: string;
  /** 是否展示遮罩层 */
  showMask?: boolean;
  /** 自定义标题区域内容 */
  header?: ReactNode;
  /** 自定义内容区域 */
  children?: ReactNode;
  /** 自定义底部区域内容 */
  footer?: ReactNode;
  /** 点击选项时触发，禁用或加载状态下不会触发 */
  onSelect?: (_action: ActionSheetAction, _index: number) => void;
  /** 点击取消按钮时触发 */
  onCancel?: () => void;
  /** 关闭时触发 */
  onClose?: () => void;
}

/** ActionSheet 动作面板组件 */
export const ActionSheet: React.FC<ActionSheetProps> = ({
  visible,
  title,
  cancelText = '取消',
  actions = [],
  description,
  showCancel = true,
  closeOnClickAction = true,
  closeOnClickOverlay = true,
  safeAreaInsetBottom = true,
  duration = 300,
  className = '',
  showMask = true,
  header,
  children,
  footer,
  onSelect,
  onCancel,
  onClose,
}) => {
  // 简化的动画状态管理，避免 SSR 问题
  const [isAnimating, setIsAnimating] = useState(false);

  // 处理显示/隐藏逻辑
  useEffect(() => {
    if (visible) {
      // 使用 setTimeout 确保组件先渲染，然后触发动画
      const timer = setTimeout(() => {
        setIsAnimating(true);
      }, 10);
      return () => clearTimeout(timer);
    } else {
      setIsAnimating(false);
    }
  }, [visible]);

  // 阻止背景滚动
  useEffect(() => {
    if (visible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [visible]);

  // 处理遮罩层点击
  const handleOverlayClick = () => {
    if (closeOnClickOverlay) {
      onClose?.();
    }
  };

  // 处理选项点击
  const handleActionClick = (action: ActionSheetAction, index: number) => {
    if (action.disabled || action.loading) {
      return;
    }

    onSelect?.(action, index);

    if (closeOnClickAction) {
      onClose?.();
    }
  };

  // 处理取消按钮点击
  const handleCancelClick = () => {
    onCancel?.();
    onClose?.();
  };

  // 如果从未显示过，则不渲染
  if (!visible && !isAnimating) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-[9999] ${className} ${!visible ? 'pointer-events-none' : ''}`}
    >
      {/* 遮罩层 */}
      {showMask && (
        <div
          className={`absolute inset-0 bg-black ${safeAreaInsetBottom ? 'pb-safe' : ''}`}
          style={{
            opacity: isAnimating ? 0.4 : 0,
            transition: `opacity ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
          }}
          onClick={handleOverlayClick}
        />
      )}

      {/* 动作面板内容 */}
      <div
        className={`absolute bottom-0 left-0 right-0 bg-white ${safeAreaInsetBottom ? 'pb-safe' : ''}`}
        style={{
          transform: isAnimating ? 'translateY(0)' : 'translateY(100%)',
          transition: `transform ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
        }}
      >
        {/* 自定义标题区域或默认标题和描述 */}
        {header ??
          ((title ?? description) && (
            <div className="px-4 py-4 text-center">
              {title && (
                <div className="text-[16px] font-medium text-[#323233] mb-1">
                  {title}
                </div>
              )}
              {description && (
                <div className="text-[14px] text-[#969799] leading-[20px]">
                  {description}
                </div>
              )}
            </div>
          ))}

        {/* 自定义内容区域或默认选项列表 */}
        {children ?? (
          <div className="max-h-[60vh] overflow-y-auto">
            {actions.map((action, index) => (
              <button
                key={index}
                className={`w-full px-4 py-[14px] text-left border-b border-bg-light last:border-b-0 transition-colors ${
                  action.disabled
                    ? 'text-text-muted cursor-not-allowed'
                    : 'text-text-primary hover:bg-bg-light active:bg-bg-gray'
                } ${action.className ?? ''}`}
                style={{ color: action.color }}
                onClick={() => handleActionClick(action, index)}
                disabled={action.disabled ?? action.loading}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-[16px] leading-[22px]">
                      {action.name}
                    </div>
                    {action.subname && (
                      <div className="text-[12px] text-text-muted mt-1 leading-[16px]">
                        {action.subname}
                      </div>
                    )}
                  </div>
                  {action.loading && (
                    <div className="ml-2">
                      <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
        )}

        {/* 自定义底部区域或默认取消按钮 */}
        {footer ??
          (showCancel && (
            <div className="border-t-[8px] border-gray-100">
              <button
                className="w-full px-4 py-[14px] text-[16px] text-[#323233] hover:bg-gray-50 active:bg-gray-100 transition-colors"
                onClick={handleCancelClick}
              >
                {cancelText}
              </button>
            </div>
          ))}
      </div>
    </div>
  );
};

export default ActionSheet;
