'use client';
import React from 'react';

interface OrderInfoProps {
  [key: string]: any;
}
const OrderInfo: React.FC<OrderInfoProps> = data => {
  const { cancelPolicy } = data;
  return (
    <div className="mx-[8px] mt-[8px] rounded-[20px] bg-white p-[12px]">
      {/* 取消政策 */}
      {cancelPolicy ? (
        <div className="flex">
          <p className="text-[14px] text-text-tertiary w-[81px]">取消政策</p>
          <p className="text-[14px] text-warning flex-1">{cancelPolicy}</p>
        </div>
      ) : null}

      {/* 入住信息,本期无 */}
      {
        // <div className="flex mt-[8px]">
        //   <p className="text-[14px] text-[#66666E] w-[81px]">入住信息</p>
        //   <p className="text-[14px] text-[#000000] flex-1">请携带所有入住人的有效身份证，报入住人姓名于5月1日14:00后办理入住，5月3日12:00前退房如需提前入住或延迟退房请联系商家</p>
        // </div>
      }

      {/* 办理入住 本期无 */}
      {
        // <div className="flex mt-[8px]">
        //   <p className="text-[14px] text-[#66666E] w-[81px]">前台出示</p>
        //   <p className="text-[14px] text-[#000000] flex-1">快速办理入住</p>
        // </div>
      }
    </div>
  );
};

export default OrderInfo;
