// 酒店列表模块类型声明
// 包含酒店搜索、筛选、排序等相关接口类型定义

declare namespace API.HotelList {
  /** 位置信息接口 */
  interface IPosition {
    /** 经度 */
    longitude: number;
    /** 纬度 */
    latitude: number;
    /** 坐标系类型 */
    coordinateType: string;
    /** 城市ID */
    cityId: string;
    /** 城市名称 */
    cityName: string;
  }

  /** 筛选器接口（用于酒店列表请求） */
  interface IFilter {
    /** 类型ID */
    typeId: number;
    /** 筛选器ID（支持字符串和数字类型） */
    filterId: string | number;
  }

  /** 酒店列表请求参数 */
  interface IHotelListFetchRequest {
    /** 到达日期 */
    arrivalDate: string;
    /** 离开日期 */
    departureDate: string;
    /** 城市ID（左上角下拉搜索的） */
    cityId: string;
    /** 城市名称 */
    cityName: string;
    /** 搜索词 */
    queryText: string;
    /** 排序方式 */
    sort: string;
    /** 最低价格 */
    lowRate?: number;
    /** 最高价格 */
    highRate?: number;
    /** 星级筛选 */
    stars?: number[];
    /** 地图查询位置（搜索指定范围内的酒店信息） */
    position?: IPosition;
    /** 当前位置 */
    currentPosition?: IPosition;
    /** 筛选器列表 */
    filters: IFilter[];
    /** 房间数量 */
    roomNumber?: number;
    /** 页码索引 */
    pageIndex: number;
    /** 页面大小 */
    pageSize: number;
  }

  /** 酒店评价信息 */
  interface IHotelReview {
    /** 好评数 */
    good: number;
    /** 差评数 */
    poor: number;
    /** 评价总数 */
    count: number;
    /** 评分 */
    score: number;
  }

  /** 酒店详细信息 */
  interface IHotel {
    /** 酒店名称 */
    hotelName: string;
    /** 星级 */
    starRate: number;
    /** 纬度 */
    latitude: string;
    /** 经度 */
    longitude: string;
    /** 酒店类别 */
    category: number;
    /** 地址 */
    address: string;
    /** 电话 */
    phone: string;
    /** 缩略图URL */
    thumbNailUrl: string;
    /** 城市代码 */
    city: string;
    /** 城市名称 */
    cityName: string;
    /** 区县代码 */
    district: string;
    /** 区县名称 */
    districtName: string;
    /** 商圈代码 */
    businessZone: string;
    /** 商圈名称 */
    businessZoneName: string;
    /** 评价信息 */
    review: IHotelReview;
    /** 入住时间 */
    checkInTime: string;
    /** 退房时间 */
    checkOutTime: string;
    /** 特色 */
    features: string[];
    /** 通用设施 */
    generalAmenities: string;
    /** 交通信息 */
    traffic: string;
    /** 描述 */
    description: string;
  }

  /** 酒店列表项 */
  interface IHotelListItem {
    /** 酒店ID */
    hotelId: string;
    /** 最低价格 */
    lowRate: number;
    /** 货币代码 */
    currencyCode: string;
    /** 酒店是否失效（用于用户行为酒店列表） */
    hotelInvalid: boolean;
    /** 酒店详细信息 */
    hotel: IHotel;
  }

  /** 酒店列表响应数据 */
  interface IHotelListFetchResponseData {
    /** 总数量 */
    count: number;
    /** 是否有更多数据 */
    hasMore: boolean;
    /** 酒店列表 */
    hotels: IHotelListItem[];
  }

  /** 酒店列表响应 */
  interface IHotelListFetchResponse {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: IHotelListFetchResponseData;
  }

  /** 酒店列表页面参数 */
  interface IHotelListPageProps {
    /** 城市ID */
    cityId?: string;
    /** 城市名称 */
    cityName?: string;
    /** 搜索词 */
    queryText?: string;
    /** 到达日期 */
    arrivalDate?: string;
    /** 离开日期 */
    departureDate?: string;
    /** 当前位置 */
    currentPosition?: IPosition;
    /** 扩展参数 */
    [key: string]: string | string[] | undefined;
  }

  /** 酒店列表搜索状态 */
  interface IHotelListSearchState {
    /** 是否正在加载 */
    loading: boolean;
    /** 是否有更多数据 */
    hasMore: boolean;
    /** 当前页码 */
    currentPage: number;
    /** 总数量 */
    totalCount: number;
    /** 酒店列表 */
    hotels: IHotelListItem[];
    /** 搜索参数 */
    searchParams: Partial<IHotelListFetchRequest>;
  }

  // ==================== 快筛相关接口 ====================

  /** 快筛请求参数 */
  interface IQuickFilterFetchRequest {
    /** 城市ID */
    cityId: string;
  }

  /** 快筛项详细信息 */
  interface IQuickFilterItem {
    /** 类型ID */
    typeId: number;
    /** 筛选器ID */
    filterId: string;
    /** 名称 */
    name: string;
    /** 英文名称 */
    nameEn: string | null;
    /** 描述 */
    describe: string | null;
    /** 是否多选 */
    multi: number;
    /** 子筛选器 */
    subFilters: IQuickFilterItem[] | null;
    /** 是否置灰 */
    grey: boolean | null;
  }

  /** 快筛响应数据 */
  type IQuickFilterFetchResponseData = IQuickFilterItem[];

  /** 快筛响应 */
  interface IQuickFilterFetchResponse {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: IQuickFilterFetchResponseData;
  }

  /** 快筛状态管理 */
  interface IQuickFilterState {
    /** 是否正在加载 */
    loading: boolean;
    /** 快筛列表 */
    filters: IQuickFilterItem[];
    /** 已选择的筛选器 */
    selectedFilters: IFilter[];
    /** 错误信息 */
    error: string | null;
  }
}
