/** 支付相关类型定义 */

declare namespace Pay {
  /** 支付方式类型 */
  interface IPaymentType {
    /** 支付方式代码 */
    payType: string;
    /** 支付方式名称 */
    payName: string;
    /** 支付方式图标URL */
    payIcon: string;
  }

  /** 支付请求参数 */
  interface IPaymentRequest {
    /** 支付方式 */
    payType: string;
    /** 订单号 */
    orderNo: string;
    /** 订单支付金额 */
    orderPayMoney: string;
    /** 商品名称 */
    goodsName: string;
  }

  /** 支付响应数据 */
  interface IPaymentResponse {
    gatewayUrl: any;
    /** 订单号 */
    orderNo: string;
    /** 多笔流水 */
    recordList: IPaymentRecord[];
    /** 流水号 */
    recordNo: string;
    /** 支付金额 */
    paidMoney: string;
    /** 支付时间 */
    payTime: string;
    /** 支付参数集合 */
    payload: string;
    /** 支付状态 */
    payStatus: number;
    /** 结果 */
    recordList: any[];
  }

  /** 支付记录 */
  interface IPaymentRecord {
    /** 流水号 */
    recordNo: string;
    /** 支付金额 */
    paidMoney: string;
    /** 支付时间 */
    payTime: string;
    /** 支付状态 */
    payStatus: number;
  }

  /** 支付状态查询请求参数 */
  interface IPaymentStatusRequest {
    /** 订单号 */
    orderNo: string;
  }

  /** 支付状态查询响应数据 */
  interface IPaymentStatusResponse {
    /** 订单号 */
    orderNo: string;
    /** 多笔流水 */
    recordList: IPaymentRecord[];
  }

  /** 支付状态常量 */
  const PaymentStatus = {
    /** 等待支付 */
    WAITING_PAYMENT: 10,
    /** 支付成功 */
    PAYMENT_SUCCESS: 11,
    /** 支付失败 */
    PAYMENT_FAILED: 12,
    /** 支付已超时 */
    PAYMENT_TIMEOUT: 13,
    /** 等待退款 */
    WAITING_REFUND: 20,
    /** 退款成功 */
    REFUND_SUCCESS: 21,
    /** 退款失败 */
    REFUND_FAILED: 22,
  } as const;
}
