/**
 * Calculates the number of days between two dates
 *
 * @param startDateStr Start date in "YYYY-MM-DD" format
 * @param endDateStr End date in "YYYY-MM-DD" format
 * @returns Number of days between the two dates
 */
export function calculateDaysBetweenDates(
  startDateStr: string,
  endDateStr: string
): number {
  // Parse the date strings into Date objects
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);

  // Calculate the time difference in milliseconds
  const timeDiff = endDate.getTime() - startDate.getTime();

  // Convert milliseconds to days (1000ms * 60s * 60m * 24h)
  const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

  // Return the absolute value in case dates are reversed
  return Math.abs(Math.round(daysDiff));
}

/**
 * 将日期字符串转换为带有"今天"、"明天"或星期几的中文格式
 *
 * @param dateStr 日期字符串，格式为"YYYY-M-D"或"YYYY-MM-DD"
 * @param referenceDate 可选参数，基准日期（默认为当前日期）
 * @returns 格式化后的字符串，如"06月24日 今天"、"06月25日 明天"或"06月26日 周四"
 */
export function formatDateWithIndicator(
  dateStr: string,
  referenceDate: Date = new Date()
): string {
  // 解析输入日期（兼容"2025-6-24"和"2025-06-24"格式）
  const date = new Date(dateStr);

  // 验证日期有效性
  if (isNaN(date.getTime())) {
    throw new Error('无效的日期格式');
  }

  // 格式化月份和日期，确保两位数显示
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const datePart = `${month}月${day}日`; // 日期部分，如"06月24日"

  // 计算与基准日期的天数差
  // 先将时间都设置为午夜，避免时间差影响天数计算
  const normalizedDate = new Date(date).setHours(0, 0, 0, 0);
  const normalizedRefDate = new Date(referenceDate).setHours(0, 0, 0, 0);
  const dayDiff = Math.round(
    (normalizedDate - normalizedRefDate) / (1000 * 60 * 60 * 24)
  );

  // 中文星期数组
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[date.getDay()]; // 获取星期几

  // 根据天数差返回不同格式
  if (dayDiff === 0) {
    return `${datePart} 今天`; // 当天显示"今天"
  } else if (dayDiff === 1) {
    return `${datePart} 明天`; // 明天显示"明天"
  } else {
    return `${datePart} ${weekday}`; // 其他日期显示星期几
  }
}

/**
 * 从日期字符串中提取月份和日期
 *
 * @param dateStr 格式为 "YYYY-MM-DD" 的日期字符串
 * @returns 包含月份和日期的对象 { month: number; day: number }
 */
export function getMonthAndDay(dateStr: string): {
  month: number;
  day: number;
} {
  const date = new Date(dateStr);

  if (isNaN(date.getTime())) {
    throw new Error('Invalid date string. Expected format: YYYY-MM-DD');
  }

  return {
    month: date.getMonth() + 1, // 月份从0开始，所以要+1
    day: date.getDate(),
  };
}

/**
 * 电话号码脱敏（保留前3位和后4位）
 *
 * @param phone 电话号码字符串
 * @returns 脱敏后的字符串，如 "138****1234"
 */
export function maskPhone(phone: string): string {
  if (!phone || phone.length < 7) {
    return phone; // 返回原值或根据需求抛出错误
  }

  return phone.replace(/(\d{3})\d+(\d{4})/, '$1****$2');
}
