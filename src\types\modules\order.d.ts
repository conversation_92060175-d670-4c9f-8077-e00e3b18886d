/* eslint-disable no-unused-vars */
/** 订单相关类型定义 */

declare namespace Order {
  /** 订单状态枚举 */
  enum OrderStatus {
    PendingPayment = 0, // 待付款
    Confirmed = 1, // 已确认/已支付
    NotTraveled = 2, // 未出行
    Cancelled = 6, // 已取消
  }

  /** 订单列表查询参数接口 */
  interface IOrderListReq {
    /** 页码 */
    pageIndex: number;
    /** 每页数量 */
    pageSize: number;
    /** 订单状态 - 空字符串表示全部，0表示待付款，2表示未出行 */
    status: string;
    /** 搜索关键词 */
    keyword: string;
    /** 订单日期（年份） */
    orderDate?: string;
  }

  /** 订单列表响应接口 */
  interface IOrderListRes {
    /** 响应码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 订单列表数据 */
    data: IOrderItem[];
    /** 是否成功 */
    success: boolean;
  }

  /** 订单列表项接口（对应API返回数据） */
  interface IOrderItem {
    /** 订单ID */
    id: string;
    /** 酒店ID */
    hotelId: string;
    /** 房间类型ID */
    roomTypeId: string;
    /** 订单号 */
    orderNo: string;
    /** 订单状态显示文本 */
    showOrderStatus: string;
    /** 订单状态 */
    orderStatus: number;
    /** 订单金额 */
    orderAmount: string;
    /** 订单创建时间 */
    orderCreateTime: string;
    /** 订单支付时间 */
    orderPayTime: string | null;
    /** 酒店名称 */
    hotelName: string;
    /** 入住日期 */
    arriveDate: string;
    /** 离店日期 */
    departDate: string;
    /** 入住晚数 */
    nightCount: number;
    /** 房间类型名称 */
    roomTypeName: string;
    /** 房间数量 */
    roomCount: number;
    /** 床型描述 */
    bedDesc: string | null;
    /** 房间描述 */
    roomDesc: string;
    /** 酒店地址 */
    hotelAddress: string;
    /** 区域ID */
    areaId: string;
    /** 区域名称 */
    areaName: string;
    /** 最后支付时间 */
    lastPayTime: number;
  }

  /** Tab 项接口 */
  interface ITabItem {
    /** 标签键值 */
    key: string;
    /** 标签显示文本 */
    label: string;
    /** 徽章数量（可选） */
    badge?: number;
  }
}
