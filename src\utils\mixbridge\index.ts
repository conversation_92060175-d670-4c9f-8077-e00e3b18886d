/** 混合调用工具集 旨在磨平平台差异，使用方无需关注是在native app内/h5/小程序等 */
import Native from '@/utils/native';

export const MixBridge = {
  openMap({
    position,
    location,
    name,
    coordinate = 'GCJ-02',
    callnative = true,
  }: {
    /** Type为navigation时比传 */
    location?: ILocation;
    /** 位置信息 */
    position: ILocation;
    /** 地图上显示的名称 */
    name?: string;
    /** 坐标系 */
    coordinate?: CoordinateType;
    /** 是否尝试掉漆高德地图app */
    callnative?: Boolean;
  }) {
    const isInNativeApp = Native.isInNativeApp();
    if (isInNativeApp) {
      if (location && position) {
        // todo: 调用app native拉起第三方地图app导航
      } else {
        // 打开第三方地图点标注
        Native.openMap({
          lat: Number(position.latitude),
          lng: Number(position.longitude),
        });
      }
    } else {
      if (location && position) {
        // 导航
        window.open(
          `https://uri.amap.com/navigation?from=${location.longitude},${location.latitude},我的位置&to=${position.longitude},${position.latitude},${name}&callnative=${callnative ? 1 : 0}`,
          '_blank'
        );
      } else {
        // 点标注
        window.open(
          `https://m.amap.com/share/index/lnglat=${position.longitude},${position.latitude}&name=${name}&coordinate=${coordinate}&callnative=${callnative ? 1 : 0}`,
          '_blank'
        );
      }
    }
  },
};
