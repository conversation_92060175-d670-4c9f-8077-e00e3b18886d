'use client';

import { useEffect, useState } from 'react';

/** Tab 项接口 */
export interface TabItem {
  /** 标签键值 */
  key: string;
  /** 标签显示文本 */
  label: string;
  /** 徽章数量（可选） */
  badge?: number;
}

/** Tabs组件属性接口 */
export interface TabsProps {
  /** Tab 项列表 */
  items: TabItem[];
  /** 当前激活的 tab key */
  activeKey?: string;
  /** 点击回调函数 */
  // eslint-disable-next-line no-unused-vars
  onTabChange?: (key: string) => void;
  /** 默认激活的tab key */
  defaultActiveKey?: string;
  /** 自定义类名 */
  className?: string;
  /** 是否固定定位 */
  fixed?: boolean;
  /** 是否自动添加占位空间（仅在fixed=true时生效） */
  autoSpacer?: boolean;
}

/** 自定义Tabs组件 - 不依赖react-vant */
export const Tabs: React.FC<TabsProps> = ({
  items,
  activeKey,
  onTabChange,
  defaultActiveKey,
  className = '',
  fixed = false,
  autoSpacer = true,
}) => {
  // 使用内部state管理激活状态
  const [currentActiveKey, setCurrentActiveKey] = useState(
    activeKey ?? defaultActiveKey ?? items[0]?.key ?? ''
  );

  // 同步外部activeKey变化
  useEffect(() => {
    if (activeKey !== undefined) {
      setCurrentActiveKey(activeKey);
    }
  }, [activeKey]);

  const handleTabClick = (key: string) => {
    setCurrentActiveKey(key);
    onTabChange?.(key);
  };

  return (
    <>
      <div
        className={`bg-white ${fixed ? 'fixed top-[56px] left-0 right-0 z-40' : ''} ${className}`}
      >
        <div className="h-[56px] relative">
          <div className="flex relative h-full pb-1">
            {items.map(item => (
              <div
                key={item.key}
                className={`flex-1 flex items-center justify-center cursor-pointer relative transition-colors duration-300 ${
                  currentActiveKey === item.key ? '' : ''
                }`}
                onClick={() => handleTabClick(item.key)}
              >
                <span
                  className={`text-[14px] transition-all duration-300 ${
                    currentActiveKey === item.key
                      ? 'text-primary font-medium'
                      : 'text-text-secondary font-normal'
                  }`}
                >
                  {item.label}
                </span>
                {item.badge !== undefined && item.badge > 0 && (
                  <span className="bg-[#ff4444] text-white text-[10px] px-1 py-0.5 rounded-lg ml-1 min-w-[16px] text-center leading-[14px]">
                    {item.badge}
                  </span>
                )}
              </div>
            ))}
            {/* 下划线指示器 */}
            <div
              className="absolute bottom-[10px] w-[20px] h-[2px] bg-primary transition-transform duration-300"
              style={{
                left: `calc(${100 / items.length}% * ${items.findIndex(item => item.key === currentActiveKey)} + ${100 / items.length / 2}% - 10px)`,
              }}
            />
          </div>
        </div>
      </div>

      {/* 自动占位空间 - 仅在固定定位且启用autoSpacer时显示 */}
      {fixed && autoSpacer && <div className="h-[56px]" aria-hidden="true" />}
    </>
  );
};

export default Tabs;
