import { headers } from 'next/headers';

import { Native } from '../../utils';

import { Navbar, type NavbarProps } from './index';

/** 服务端Navbar组件属性 - 自动获取UserAgent */
export interface ServerNavbarProps extends Omit<NavbarProps, 'userAgent'> {}

/** 服务端Navbar组件 - 自动获取UserAgent并传递给Navbar */
export const ServerNavbar: React.FC<ServerNavbarProps> = async props => {
  // 直接在组件内获取UserAgent，避免导入服务端工具函数
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';
  const isOwnApp = Native.isOwnApp(userAgent);
  if (isOwnApp) return null;
  return <Navbar {...props} userAgent={userAgent} />;
};

export default ServerNavbar;
