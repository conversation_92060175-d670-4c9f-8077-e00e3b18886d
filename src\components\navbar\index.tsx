'use client';

import React from 'react';

import { useRouter } from 'next/navigation';

import { getImageUrl } from '../../utils';
import Native from '../../utils/native';

/** Navbar 左侧区域属性 */
export interface NavbarLeftProps {
  /** 是否显示返回按钮，默认 true */
  showBack?: boolean;
  /** 是否返回首页，默认 false（返回上一页） */
  backToHome?: boolean;
  /** 自定义返回处理函数，如果不提供则使用 router.back() 或 router.push('/') */
  onBack?: () => void;
  /** 返回按钮的目标URL，优先级高于 backToHome 和默认返回行为 */
  backUrl?: string;
  /** 自定义左侧内容 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

/** Navbar 中间区域属性 */
export interface NavbarCenterProps {
  /** 标题文本 */
  title?: string;
  /** 自定义中间内容 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

/** Navbar 右侧区域属性 */
export interface NavbarRightProps {
  /** 自定义右侧内容 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

/** Navbar 组件属性 */
export interface NavbarProps {
  /** 标题文本 */
  title?: string;
  /** 左侧区域配置 */
  left?: NavbarLeftProps;
  /** 中间区域配置 */
  center?: NavbarCenterProps;
  /** 右侧区域配置 */
  right?: NavbarRightProps;
  /** 整个组件的自定义类名 */
  className?: string;
  /** 整个组件的自定义样式 */
  style?: React.CSSProperties;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 高度 */
  height?: string | number;
  /** 左右内边距 */
  paddingX?: string | number;
  /** 是否固定定位 */
  fixed?: boolean;
  /** 固定定位时的z-index */
  zIndex?: number;
  /** 是否自动添加占位空间（仅在fixed=true时生效） */
  autoSpacer?: boolean;
  /** UserAgent字符串（SSR场景下传入） */
  userAgent?: string;
}

/** Navbar 左侧区域组件 */
const NavbarLeft: React.FC<NavbarLeftProps> = ({
  showBack = true,
  backToHome = false,
  onBack,
  backUrl,
  children,
  className = '',
}) => {
  const router = useRouter();

  // 渲染自定义内容
  if (children) {
    return (
      <div className={`flex items-center justify-start ${className}`}>
        {children}
      </div>
    );
  }

  // 渲染返回按钮
  if (showBack) {
    const handleBack = () => {
      if (onBack) {
        // 优先级1：使用自定义返回处理函数
        onBack();
      } else if (backUrl) {
        // 优先级2：跳转到指定的 backUrl
        router.replace(backUrl);
      } else if (backToHome) {
        // 优先级3：返回首页
        router.push('/');
      } else {
        // 优先级4：默认返回上一页
        // router.back();
        window.history.back();
      }
    };

    return (
      <div className={`flex items-center justify-start ${className}`}>
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-[18px] h-[18px] text-text-tertiary hover:text-text-primary transition-colors duration-200"
          aria-label="返回"
          type="button"
        >
          <img src={getImageUrl('arrow.png')} alt="" />
        </button>
      </div>
    );
  }

  // 不显示任何内容
  return <div className={`flex items-center justify-start ${className}`} />;
};

/** Navbar 中间区域组件 */
const NavbarCenter: React.FC<NavbarCenterProps> = ({
  title,
  children,
  className = '',
}) => (
  <div
    className={`flex-1 flex justify-center items-center overflow-hidden ${className}`}
  >
    {children ??
      (title ? (
        <h1 className="max-w-full text-center text-base font-bold text-[#11111E] overflow-hidden text-ellipsis whitespace-nowrap">
          {title}
        </h1>
      ) : null)}
  </div>
);

/** Navbar 右侧区域组件 */
const NavbarRight: React.FC<NavbarRightProps> = ({
  children,
  className = '',
}) => (
  <div className={`flex items-center justify-end ${className}`}>{children}</div>
);

/** Navbar 主组件 - 支持SSR的导航栏组件 */
export const Navbar: React.FC<NavbarProps> = ({
  title,
  left,
  center,
  right,
  className = '',
  style,
  backgroundColor = 'white',
  height = 56,
  paddingX = '24px',
  fixed = false,
  zIndex = 50,
  autoSpacer = true,
  userAgent,
}) => {
  // 获取安全区域高度（支持SSR传入userAgent）
  const safeAreaHeightPx = Native.getSafeAreaHeight(userAgent);
  // 构建内联样式
  const inlineStyle: React.CSSProperties = {
    backgroundColor,
    height:
      typeof height === 'number' ? `${height + safeAreaHeightPx}px` : height,
    paddingLeft: typeof paddingX === 'number' ? `${paddingX}px` : paddingX,
    paddingRight: typeof paddingX === 'number' ? `${paddingX}px` : paddingX,
    paddingTop: safeAreaHeightPx > 0 ? `${safeAreaHeightPx}px` : undefined,
    zIndex: fixed ? zIndex : undefined,
    ...style,
  };

  // 构建基础类名
  const baseClasses = [
    'flex items-center',
    'border-bg-light',
    fixed ? 'fixed top-0 left-0 right-0' : 'relative',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <>
      <nav className={baseClasses} style={inlineStyle}>
        {/* 左侧区域 */}
        <div className="flex-shrink-0 flex items-center min-w-0">
          <NavbarLeft {...left} />
        </div>

        {/* 中间区域 */}
        <NavbarCenter {...center} title={title ?? center?.title} />

        {/* 右侧区域 */}
        <div className="flex-shrink-0 flex items-center min-w-0 ml-auto">
          <NavbarRight {...right} />
        </div>
      </nav>

      {/* 自动占位空间 - 仅在固定定位且启用autoSpacer时显示 */}
      {fixed && autoSpacer && (
        <div
          style={{
            height:
              typeof height === 'number'
                ? `${height + safeAreaHeightPx}px`
                : `calc(${height} + ${safeAreaHeightPx}px)`,
          }}
          aria-hidden="true"
        />
      )}
    </>
  );
};

// 导出子组件，方便单独使用
export { NavbarCenter, NavbarLeft, NavbarRight };

export default Navbar;
