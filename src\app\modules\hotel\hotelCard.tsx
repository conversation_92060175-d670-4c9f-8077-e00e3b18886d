import React from 'react';

import { useRouter } from 'next/navigation';

import { tracker } from '../../../utils/track';

const RecommendHotelCard = React.memo((data: Home.IRecommendHotel) => {
  const router = useRouter();
  const handleJumpDetail = () => {
    router.push(`/hotel/hotelDetails?hotelId=${data.outId}`);
    tracker({
      eventType: 'click',
      extra: {
        action: 'home_hotel_user_to_hotel_detail',
        ...data,
      },
    });
  };
  return (
    <div
      key={data.outId}
      className="mb-[12px] overflow-hidden"
      onClick={handleJumpDetail}
    >
      <div className="relative aspect-square">
        <div
          className="w-full h-full bg-cover bg-center rounded-[20px]"
          style={{
            backgroundImage: `url(${data.mainImage})`,
          }}
        />
        {/* <span className="absolute left-[10px] bottom-[10px] bg-[rgba(0,0,0,0.4)] rounded-[6px] px-[4px] py-[2px] text-white text-[10px] leading-[14px]">
          {data.distance}
        </span> */}
        <span className="absolute right-[10px] bottom-[10px] bg-[rgba(0,0,0,0.4)] rounded-[6px] px-[4px] py-[2px] text-white text-[10px] leading-[14px]">
          {data.categoryDesc}
        </span>
      </div>
      <div className="mt-[8px] leading-[20px]">
        <b className="font-bold text-[14px]">{data.hotelName}</b>
      </div>
      <div className="flex leading-[17px] text-[12px]">
        <span className="text-primary font-bold whitespace-nowrap">
          {data.serviceScore}
        </span>
        <span className="text-primary font-bold mx-[4px] whitespace-nowrap">
          {data.serviceScoreDesc}
        </span>
        <span className="text-text-muted flex-1 min-w-0 overflow-hidden whitespace-nowrap text-ellipsis">
          {data.address}
        </span>
      </div>
      <div className="flex items-baseline mt-[4px] text-[10px]">
        <span>{data.currency}</span>
        <span className="leading-[18px] text-[16px] font-bold">
          {data.minPrice}
        </span>
        <span className="ml-[2px]">起</span>
      </div>
    </div>
  );
});

export default RecommendHotelCard;
