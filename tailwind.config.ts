import { generateThemeConfig } from './src/utils/theme';

import type { Config } from 'tailwindcss';
import type { PluginAPI } from 'tailwindcss/types/config';

const themeConfig = generateThemeConfig();

// 预定义颜色，使用 rgba 格式
const colors = {
  // 保持向后兼容的颜色
  background: 'rgba(255, 255, 255, 1)',
  'background-dark': 'rgba(10, 10, 10, 1)',
  foreground: 'rgba(23, 23, 23, 1)',
  'foreground-dark': 'rgba(237, 237, 237, 1)',

  // 新的主题颜色系统
  ...themeConfig,

  // 保持现有的颜色定义
  gray: {
    100: 'rgba(243, 244, 246, 1)',
    300: 'rgba(209, 213, 219, 1)',
    500: 'rgba(107, 114, 128, 1)',
  },
  red: {
    500: 'rgba(239, 68, 68, 1)',
  },
  white: 'rgba(255, 255, 255, 1)',
  'white-90': 'rgba(255, 255, 255, 0.9)',
  'white-80': 'rgba(255, 255, 255, 0.8)',
  'white-70': 'rgba(255, 255, 255, 0.7)',
  'white-60': 'rgba(255, 255, 255, 0.6)',
  'white-50': 'rgba(255, 255, 255, 0.5)',
  'white-40': 'rgba(255, 255, 255, 0.4)',
  'white-30': 'rgba(255, 255, 255, 0.3)',
  'white-20': 'rgba(255, 255, 255, 0.2)',
  'white-10': 'rgba(255, 255, 255, 0.1)',
};

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],

  theme: {
    extend: {
      colors,
      fontFamily: {
        sans: ['Geist', 'Geist Fallback', 'system-ui', 'sans-serif'],
        mono: ['Geist Mono', 'Geist Mono Fallback', 'monospace'],
      },
    },
  },
  plugins: [
    // 添加自定义插件来处理 transform
    function ({ addUtilities }: PluginAPI) {
      const newUtilities = {
        '.transform-center': {
          '-webkit-transform': 'translate(-50%, -50%)',
          transform: 'translate(-50%, -50%)',
        },
        '.transform-center-x': {
          '-webkit-transform': 'translateX(-50%)',
          transform: 'translateX(-50%)',
        },
        '.transform-center-y': {
          '-webkit-transform': 'translateY(-50%)',
          transform: 'translateY(-50%)',
        },
      };
      addUtilities(newUtilities);
    },
    function ({ addVariant }: PluginAPI) {
      addVariant('active', '&.active');
    },
  ],
  // 禁用现代特性
  future: {
    hoverOnlyWhenSupported: false, // 禁用现代 hover 特性
  },
  // 只禁用真正不兼容的特性
  corePlugins: {
    // 禁用 backdrop 相关特性
    backdropFilter: false,
    backdropBlur: false,
    backdropBrightness: false,
    backdropContrast: false,
    backdropGrayscale: false,
    backdropHueRotate: false,
    backdropInvert: false,
    backdropOpacity: false,
    backdropSaturate: false,
    backdropSepia: false,
  },
} satisfies Config;

export default config;
