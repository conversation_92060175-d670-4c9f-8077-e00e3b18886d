import type { CSSProperties, ReactNode } from 'react';

/** Toast 提示类型 */
export type ToastType = 'info' | 'loading' | 'success' | 'fail';

/** Toast 位置 */
export type ToastPosition = 'top' | 'middle' | 'bottom';

/** 加载图标类型 */
export type LoadingType = 'circular' | 'spinner';

/** Toast 配置选项 */
export interface ToastOptions {
  /** 提示类型，可选值为 info、loading、success、fail */
  type?: ToastType;
  /** 位置，可选值为 top、middle、bottom */
  position?: ToastPosition;
  /** 文本内容，支持通过 \n 换行 */
  message?: string;
  /** 自定义图标 */
  icon?: ReactNode;
  /** 图标大小，如 20px、2em，默认单位为 px */
  iconSize?: number | string;
  /** 是否禁止背景点击 */
  forbidClick?: boolean;
  /** 是否在点击后关闭 */
  closeOnClick?: boolean;
  /** 是否在点击遮罩层后关闭 */
  closeOnClickOverlay?: boolean;
  /** 加载图标类型，可选值为 circular、spinner */
  loadingType?: LoadingType;
  /** 展示时长(ms)，值为 0 时，toast 不会消失 */
  duration?: number;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  customStyle?: CSSProperties;
  /** 是否显示背景遮罩层 */
  overlay?: boolean;
  /** 自定义遮罩层类名 */
  overlayClass?: string;
  /** 自定义遮罩层样式 */
  overlayStyle?: CSSProperties;
  /** 是否只显示图标，不显示外框背景 */
  iconOnly?: boolean;
  /** 完全展示后的回调函数 */
  onOpened?: () => void;
  /** 关闭时的回调函数 */
  onClose?: () => void;
  /** 动画类名 */
  transition?: string;
  /** 指定挂载的节点 */
  teleport?: HTMLElement | (() => HTMLElement);
}

/** Toast 实例 */
export interface ToastInstance {
  /** 更新配置 */
  config: (_options: Partial<ToastOptions>) => void;
  /** 关闭 */
  close: () => void;
}

/** Toast 静态方法 */
export interface ToastStatic {
  /** 展示提示 */
  (_options: ToastOptions | string): ToastInstance;
  /** 展示文字提示 */
  info: (_options: ToastOptions | string) => ToastInstance;
  /** 展示加载提示 */
  loading: (_options: ToastOptions | string) => ToastInstance;
  /** 展示成功提示 */
  success: (_options: ToastOptions | string) => ToastInstance;
  /** 展示失败提示 */
  fail: (_options: ToastOptions | string) => ToastInstance;
  /** 关闭提示 */
  clear: (_clearAll?: boolean) => void;
  /** 允许同时存在多个 Toast */
  allowMultiple: () => void;
  /** 修改默认配置，对所有 Toast 生效。传入 type 可以修改指定类型的默认配置 */
  setDefaultOptions: (
    _typeOrOptions: string | Partial<ToastOptions>,
    _options?: Partial<ToastOptions>
  ) => void;
  /** 重置默认配置，对所有 Toast 生效。传入 type 可以重置指定类型的默认配置 */
  resetDefaultOptions: (_type?: string) => void;
}

/** ToastContainer 组件属性 */
export interface ToastContainerProps {
  /** 指定挂载的节点 */
  teleport?: HTMLElement | (() => HTMLElement);
}

/** UseToast Hook 返回值 */
export interface UseToastReturn extends ToastStatic {
  /** 渲染 Toast 容器 */
  render: (_teleport?: HTMLElement | (() => HTMLElement)) => ReactNode;
}
