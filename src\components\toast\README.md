# Toast 轻提示

在页面中间弹出黑色半透明提示，用于消息通知、加载提示、操作结果反馈等场景。

## 特性

- 🎯 **完整的API对标** - 完全对标 vant-react 的 Toast API 设计
- 🎨 **多种提示类型** - 支持 info、success、fail、loading 四种类型
- 📍 **灵活的位置** - 支持 top、middle、bottom 三种位置
- ⚡ **高性能** - 基于 React Portal 实现，不影响页面布局
- 🎭 **丰富的动画** - 内置淡入淡出和缩放动画效果
- 🔧 **高度可定制** - 支持自定义图标、样式、持续时间等
- 📱 **移动端优化** - 专为移动端设计，支持触摸交互
- 🌐 **SSR 友好** - 支持 Next.js 服务端渲染

## 引入

```js
import { Toast, ToastContainer } from '@/components';
```

## 代码演示

### 基础用法

```js
// 文字提示
Toast('提示内容');

// 成功提示
Toast.success('成功文案');

// 失败提示
Toast.fail('失败文案');

// 加载提示
Toast.loading('加载中...');
```

### 自定义位置

```js
Toast({
  message: '顶部展示',
  position: 'top',
});

Toast({
  message: '底部展示',
  position: 'bottom',
});
```

### 只显示图标（无外框）

```js
Toast.loading({
  icon: <img src="loading.gif" alt="loading" className="w-[120px] h-[120px]" />,
  duration: 0,
  overlay: true,
  forbidClick: true,
  iconOnly: true, // 只显示图标，不显示外框背景
});
```

### 动态更新内容

```js
let remain = 4;
let timer;
const toast = Toast.info({
  message: `还剩 ${remain + 1} 秒`,
  duration: 5000,
  onClose: () => clearInterval(timer),
});

timer = setInterval(() => {
  toast.config({ message: `还剩 ${remain--} 秒` });
}, 1000);
```

### 全局方法

```js
// 修改默认配置
Toast.setDefaultOptions({ duration: 2000 });

// 修改指定类型的默认配置
Toast.setDefaultOptions('loading', { forbidClick: true });

// 重置默认配置
Toast.resetDefaultOptions();

// 允许同时存在多个 Toast
Toast.allowMultiple();

// 关闭提示
Toast.clear();
```

## API

### 方法

| 方法名                    | 说明                   | 参数                 | 返回值     |
| ------------------------- | ---------------------- | -------------------- | ---------- |
| Toast                     | 展示提示               | `options \| message` | toast 实例 |
| Toast.info                | 展示文字提示           | `options \| message` | toast 实例 |
| Toast.loading             | 展示加载提示           | `options \| message` | toast 实例 |
| Toast.success             | 展示成功提示           | `options \| message` | toast 实例 |
| Toast.fail                | 展示失败提示           | `options \| message` | toast 实例 |
| Toast.clear               | 关闭提示               | `clearAll`           | `void`     |
| Toast.allowMultiple       | 允许同时存在多个 Toast | -                    | `void`     |
| Toast.setDefaultOptions   | 修改默认配置           | `type \| options`    | `void`     |
| Toast.resetDefaultOptions | 重置默认配置           | `type`               | `void`     |

### Options

| 参数                | 说明                                    | 类型                                         | 默认值          |
| ------------------- | --------------------------------------- | -------------------------------------------- | --------------- |
| type                | 提示类型                                | `'info' \| 'loading' \| 'success' \| 'fail'` | `'info'`        |
| position            | 位置                                    | `'top' \| 'middle' \| 'bottom'`              | `'middle'`      |
| message             | 文本内容，支持通过 \\n 换行             | `string`                                     | `''`            |
| icon                | 自定义图标                              | `ReactNode`                                  | -               |
| iconSize            | 图标大小                                | `number \| string`                           | `'36px'`        |
| forbidClick         | 是否禁止背景点击                        | `boolean`                                    | `false`         |
| closeOnClick        | 是否在点击后关闭                        | `boolean`                                    | `false`         |
| closeOnClickOverlay | 是否在点击遮罩层后关闭                  | `boolean`                                    | `false`         |
| loadingType         | 加载图标类型                            | `'circular' \| 'spinner'`                    | `'circular'`    |
| duration            | 展示时长(ms)，值为 0 时，toast 不会消失 | `number`                                     | `2000`          |
| className           | 自定义类名                              | `string`                                     | -               |
| overlay             | 是否显示背景遮罩层                      | `boolean`                                    | `false`         |
| overlayClass        | 自定义遮罩层类名                        | `string`                                     | -               |
| overlayStyle        | 自定义遮罩层样式                        | `object`                                     | -               |
| iconOnly            | 是否只显示图标，不显示外框背景          | `boolean`                                    | `false`         |
| onOpened            | 完全展示后的回调函数                    | `Function`                                   | -               |
| onClose             | 关闭时的回调函数                        | `Function`                                   | -               |
| transition          | 动画类名                                | `string`                                     | `'rv-fade'`     |
| teleport            | 指定挂载的节点                          | `HTMLElement \| (() => HTMLElement)`         | `document.body` |

## 在 Next.js 中使用

由于 Toast 组件需要在客户端渲染，建议在应用根部添加 ToastContainer：

```jsx
// app/layout.tsx
import { ToastContainer } from '@/components';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <ToastContainer />
      </body>
    </html>
  );
}
```

或者使用 useToast Hook：

```jsx
'use client';
import { useToast } from '@/components';

export default function MyComponent() {
  const toast = useToast();

  const handleClick = () => {
    toast.success('操作成功');
  };

  return (
    <div>
      <button onClick={handleClick}>显示提示</button>
      {toast.render()}
    </div>
  );
}
```
