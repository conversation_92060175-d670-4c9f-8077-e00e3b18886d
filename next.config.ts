import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // 图片配置
  images: {
    domains: [
      'ota-front-public.oss-cn-hangzhou.aliyuncs.com',
      'pavo.elongstatic.com',
    ],
  },

  // 开发环境性能优化
  ...(process.env.NODE_ENV === 'development' && {
    // 禁用 x-powered-by 头
    poweredByHeader: false,

    // 优化开发构建
    swcMinify: true,

    // 实验性功能 - 提升开发性能
    experimental: {
      // 启用 SWC 插件缓存
      swcPlugins: [],
      // 优化包导入
      optimizePackageImports: ['react-vant', 'dayjs'],
    },

    // Webpack 配置优化
    webpack: (config, { dev, isServer }) => {
      if (dev && !isServer) {
        // 开发环境优化
        config.cache = {
          type: 'filesystem',
          buildDependencies: {
            config: [__filename],
          },
        };

        // 减少文件监听
        config.watchOptions = {
          ignored: [
            '**/node_modules/**',
            '**/.git/**',
            '**/.next/**',
            '**/coverage/**',
            '**/dist/**',
          ],
        };
      }

      return config;
    },
  }),
};

export default nextConfig;
