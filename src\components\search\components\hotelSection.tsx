'use client';

import { List } from 'react-vant';

import { getImageUrl } from '@/utils';

import type { HotelSectionProps } from '../types';

// Search 类型已经在全局声明中定义，无需导入
// SearchItemType 枚举值: 1=City, 2=Brand, 3=District, 4=BusinessZone, 5=Hotel

// 历史记录相关工具函数（支持城市和酒店）
const HISTORY_KEY = 'search_history';
const MAX_HISTORY_COUNT = 6;

function getHistory(): Search.ISearchResultItem[] {
  if (typeof window === 'undefined') return [];
  try {
    const history = localStorage.getItem(HISTORY_KEY);
    return history ? JSON.parse(history) : [];
  } catch {
    return [];
  }
}

function addToHistory(item: Search.ISearchResultItem) {
  if (typeof window === 'undefined') return;
  try {
    const history = getHistory();
    // 以type+itemName+cityId为唯一
    const filtered = history.filter(
      h =>
        !(
          h.type === item.type &&
          h.itemName === item.itemName &&
          h.cityId === item.cityId
        )
    );
    const newHistory = [item, ...filtered].slice(0, MAX_HISTORY_COUNT);
    localStorage.setItem(HISTORY_KEY, JSON.stringify(newHistory));
  } catch (error) {
    console.warn('Failed to save search history:', error);
  }
}

function clearHistory() {
  if (typeof window === 'undefined') return;
  try {
    localStorage.removeItem(HISTORY_KEY);
  } catch (error) {
    console.warn('Failed to clear search history:', error);
  }
}

export const HotelSection: React.FC<HotelSectionProps> = ({
  searchResults,
  keyword = '',
  loading = false,
  loadingMore = false,
  hasMore = false,
  onLoadMore,
  onResultSelect,
}) => {
  // 高亮显示匹配的关键词
  const highlightKeyword = (text: string, keyword: string) => {
    if (!keyword.trim()) {
      return <span className="text-text-secondary">{text}</span>;
    }

    // 转义特殊字符
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedKeyword})`, 'gi');
    const parts = text.split(regex);

    return (
      <>
        {parts.map((part, index) => {
          const isMatch = part.toLowerCase() === keyword.toLowerCase();
          return (
            <span
              key={index}
              className={isMatch ? 'text-primary' : 'text-text-secondary'}
            >
              {part}
            </span>
          );
        })}
      </>
    );
  };

  // 调试日志
  console.warn('🏨 HotelSection render:', {
    searchResultsLength: searchResults.length,
    keyword,
    searchResults: searchResults.map(item => ({
      type: item.type,
      itemName: item.itemName,
    })),
    loading,
    loadingMore,
    hasMore,
    timestamp: new Date().toISOString(),
  });

  // 处理搜索结果项点击
  const handleResultClick = (item: Search.ISearchResultItem) => {
    console.warn('🖱️ Search result selected:', item);
    if ((item.type === 5 || item.type === 1) && item.itemName) {
      addToHistory(item);
    }
    onResultSelect?.(item);
  };

  // 获取类型图标
  const getTypeIcon = (type: number) => {
    switch (type) {
      case 1: // City
        return '🏙️';
      case 3: // District
        return '🏘️';
      case 4: // BusinessZone
        return '🏢';
      case 5: // Hotel
        return getImageUrl('order/hotel.png');
      case 2: // Brand
        return '🏷️';
      default:
        return '📍';
    }
  };

  if (loading && searchResults.length === 0) {
    return (
      <div className="h-full bg-white flex items-center justify-center">
        <div className="text-sm text-gray-500">搜索中...</div>
      </div>
    );
  }

  if (searchResults.length === 0) {
    return (
      <div className="h-full bg-white flex items-center justify-center">
        <div className="text-sm text-gray-500">暂无搜索结果</div>
      </div>
    );
  }

  // 处理加载更多
  const handleLoadMore = async () => {
    console.warn('🔄 HotelSection handleLoadMore triggered:', {
      searchResultsLength: searchResults.length,
      hasMore,
      loading,
      loadingMore,
      hasOnLoadMore: !!onLoadMore,
      timestamp: new Date().toISOString(),
    });

    // 添加保护机制：只有在有搜索结果且确实有更多数据时才允许加载
    if (
      onLoadMore &&
      searchResults.length > 0 &&
      hasMore &&
      !loading &&
      !loadingMore
    ) {
      console.warn('🚀 HotelSection: Executing load more');
      await onLoadMore();
    } else {
      console.warn('⏸️ HotelSection: Load more blocked', {
        hasOnLoadMore: !!onLoadMore,
        resultsLength: searchResults.length,
        hasMore,
        loading,
        loadingMore,
      });
    }
  };

  return (
    <div className="h-full bg-white flex flex-col">
      {/* 搜索结果提示 */}
      <div className="my-[32px] mx-[16px]">
        <div
          className="text-text-secondary bg-bg-light rounded-[12px] text-[12px] text-center h-[41px] leading-[41px]"
          style={{ fontSize: '12px' }}
        >
          当前目的地及周边查询结果（酒店起价为参考价）
        </div>
      </div>

      {/* 搜索结果列表 - 使用 List 组件实现上拉加载 */}
      <div className="flex-1 overflow-auto">
        <List
          onLoad={handleLoadMore}
          finished={!hasMore}
          loadingText="加载中..."
          finishedText={searchResults.length > 0 ? '没有更多了' : undefined}
          offset={0} // 设置为0，禁用自动触发，只有用户真正滚动到底部时才触发
        >
          {searchResults.map((item, _index) => (
            <button
              key={`${item.type}-${item.itemName}-${item.cityId || ''}-${item.district || ''}-${item.businessZone || ''}`}
              onClick={() => handleResultClick(item)}
              className="w-full px-4 py-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
              style={{ minHeight: '72px' }}
            >
              <div className="flex items-start justify-between">
                {/* 左侧内容 */}
                <div className="flex items-start flex-1 min-w-0">
                  {/* 类型图标 */}
                  <div className="w-8 h-8 flex items-center justify-center rounded-lg mr-3 flex-shrink-0 mt-1">
                    <img
                      src={getTypeIcon(item.type)}
                      alt="酒店"
                      className="w-[18px] h-[18px]"
                    />
                  </div>

                  {/* 文本信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center mb-1">
                      <span
                        className="truncate"
                        style={{ fontSize: '14px', fontWeight: '500' }}
                      >
                        {highlightKeyword(item.itemName, keyword)}
                      </span>
                    </div>

                    {/* 地址信息 */}
                    <div
                      className="text-[#66666E] mt-1 truncate"
                      style={{ fontSize: '12px' }}
                    >
                      {item.brandName ? `${item.brandName} / ` : ''}
                      {item.type === 5 // Hotel
                        ? `${item.reviewScore ?? 0}分`
                        : `${item.cityName}${item.districtName ? ` / ${item.districtName}` : ''}`}
                    </div>
                  </div>
                </div>

                {/* 右侧价格（仅酒店显示） */}
                {item.type === 5 && ( // Hotel
                  <div className="text-right ml-3 flex-shrink-0">
                    <div
                      className="text-[#33333E] relative top-[15px]"
                      style={{ fontSize: '12px', fontWeight: '500' }}
                    >
                      {item.currencySymbol}
                      {Math.floor(item.itemPrice)}起
                    </div>
                  </div>
                )}
              </div>
            </button>
          ))}
        </List>
      </div>
    </div>
  );
};

export { addToHistory, clearHistory, getHistory };
