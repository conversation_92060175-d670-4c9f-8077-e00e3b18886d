import Cookies from 'js-cookie';

import { Toast } from '../../components';
import { CookiesKeyEnum } from '../../enum';
import { buildQueryString } from '../index';
import { tracker, tryCatchTracker } from '../track';

interface RequestConfig {
  baseURL?: string;
  defaultHeaders?: Record<string, any>;
  timeout?: number;
}

interface IRequestOptions extends Omit<RequestInit, 'body'> {
  /** 忽略错误 */
  ignoreError?: boolean;
  /** 超时时间 */
  timeout?: number;
  /** 重试次数 */
  retryCount?: number;
  /** Get请求参数 */
  params?: Record<string, any>;
  /** 请求体，支持对象类型 */
  body?: any;
  headers?: Record<string, string>;
}

export function createRequest(config: RequestConfig = {}) {
  const { baseURL = '', defaultHeaders = {}, timeout = 10000 } = config;

  async function request<T>(
    url: string,
    options: IRequestOptions = {}
  ): Promise<App.Service.IResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body = null,
      retryCount = 0,
      signal,
      ignoreError,
      params,
      timeout: requestTimeout = timeout,
    } = options;
    const { token } = params ?? {};
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), requestTimeout);

    try {
      if (token) {
        headers['HT-Token'] = token;
      } else {
        // 兼容处理：优先读取HT-Token，如果没有值再读取token
        const htToken = Cookies.get(CookiesKeyEnum.AUTH_TOKEN);
        const fallbackToken = Cookies.get('token');
        headers['HT-Token'] = htToken || fallbackToken || '';
      }
      if (method.toUpperCase() === 'GET' && options.params) {
        url += buildQueryString(options.params);
      }

      // 为 POST/PUT 请求自动设置 Content-Type 和处理 body
      if (
        ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase()) &&
        body !== null &&
        body !== undefined
      ) {
        // 如果没有设置 Content-Type，默认为 application/json
        if (!headers['Content-Type']) {
          headers['Content-Type'] = 'application/json';
        }

        // 根据 Content-Type 处理请求体
        const contentType = headers['Content-Type'].toLowerCase();
        if (contentType.includes('application/json')) {
          options.body = JSON.stringify(body);
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          // 处理表单数据
          options.body = new URLSearchParams(body).toString();
        } else {
          // 其他类型直接使用原始数据（如 FormData、Blob 等）
          options.body = body;
        }
      }
      tracker({
        eventType: 'pv',
        url,
        options,
      });
      const response = await fetch(`${baseURL}${url}`, {
        signal: signal ?? controller.signal,
        credentials: 'include', // 允许跨域请求携带和接收 Cookie
        ...options,
        headers: { ...defaultHeaders, ...headers },
        body: options.body || null,
      });

      clearTimeout(timeoutId);
      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `Request to ${baseURL}${url} failed with status ${response.status}: ${response.statusText} - ${errorText}`
        );
        const message = JSON.parse(errorText)?.message;
        if (message) {
          Toast({
            message,
          });
        }
        throw new Error(
          `Error ${response.status}: ${response.statusText} - ${errorText}`
        );
      }

      const res = (await response.json()) as App.Service.IResponse<T>;
      tracker({
        eventType: 'pv',
        url,
        response: res,
      });
      if (
        (res.code === 1008 || res.code === 401) &&
        typeof window !== 'undefined'
      ) {
        // 兼容处理：同时移除HT-Token和token
        Cookies.remove(CookiesKeyEnum.AUTH_TOKEN);
        Cookies.remove('token');
        window.location.href = '/login';
        return { ...res, isSuccess: false };
      }
      if (res.code !== 200 && !ignoreError && typeof window !== 'undefined') {
        console.error(`Error: ${res.message}`);
      }
      return { ...res, isSuccess: res.code === 200 };
    } catch (error) {
      tryCatchTracker(error, { url, options });
      clearTimeout(timeoutId);

      if (retryCount > 0) {
        return request<T>(url, {
          ...options,
          retryCount: retryCount - 1,
        });
      }

      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
        const apiErrorMatch = errorMessage.match(/^API Error (\d+): (.*)$/);
        if (apiErrorMatch) {
          const code = parseInt(apiErrorMatch[1], 10);
          const message = apiErrorMatch[2];
          errorMessage = `API Error ${code}: ${message}`;
        }
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      console.error(`Request to ${baseURL}${url} failed:`, errorMessage);

      return {
        data: null,
        message: errorMessage,
        code: 500,
        isSuccess: false,
      } as App.Service.IResponse<T>;
    }
  }

  return request;
}
