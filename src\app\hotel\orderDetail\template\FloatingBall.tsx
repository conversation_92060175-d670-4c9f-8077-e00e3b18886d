'use client';
import ActionSheet from '@/components/actionSheet';
import React, { useEffect, useRef, useState } from 'react';

interface Position {
  x: number;
  y: number;
}

interface FloatingBallProps {
  // initialPosition?: Position; // 初始位置
  size?: number;
}

const FloatingBall: React.FC<FloatingBallProps> = ({ size = 66 }) => {
  const [position, setPosition] = useState<Position>({ x: 20, y: 20 }); // 初始值不重要

  useEffect(() => {
    // 仅在客户端执行
    setPosition({
      x: window.innerWidth - size - 20,
      y: window.innerHeight - size - 100,
    });
  }, [size]); // size变化时重新计算

  const [isDragging, setIsDragging] = useState(false);
  const [offset, setOffset] = useState<Position>({ x: 0, y: 0 });
  const [popVisible, setPopVisible] = useState(false);
  const ballRef = useRef<HTMLDivElement>(null);

  // 边界检查，确保悬浮球不会移出视口
  const checkBoundary = (pos: Position): Position => {
    if (!ballRef.current) return pos;

    const ballWidth = ballRef.current.offsetWidth;
    const ballHeight = ballRef.current.offsetHeight;

    return {
      x: Math.max(0, Math.min(pos.x, window.innerWidth - ballWidth)),
      y: Math.max(0, Math.min(pos.y, window.innerHeight - ballHeight)),
    };
  };

  // 处理鼠标/触摸按下事件
  const handleStart = (clientX: number, clientY: number) => {
    if (ballRef.current) {
      const rect = ballRef.current.getBoundingClientRect();
      setOffset({
        x: clientX - rect.left,
        y: clientY - rect.top,
      });
      setIsDragging(true);
    }
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const newPos = checkBoundary({
      x: e.clientX - offset.x,
      y: e.clientY - offset.y,
    });
    setPosition(newPos);
  };

  // 处理触摸移动事件
  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging || e.touches.length === 0) return;
    e.preventDefault();

    const newPos = checkBoundary({
      x: e.touches[0].clientX - offset.x,
      y: e.touches[0].clientY - offset.y,
    });
    setPosition(newPos);
  };

  // 处理结束拖拽
  const handleEnd = () => {
    setIsDragging(false);
  };

  // 添加事件监听器
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleEnd);
      window.addEventListener('touchmove', handleTouchMove, { passive: false });
      window.addEventListener('touchend', handleEnd);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleEnd);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleEnd);
    };
  }, [isDragging, offset]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setPosition(prev => checkBoundary(prev));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div>
      <div
        ref={ballRef}
        className={`
        fixed cursor-move
        flex items-center flex-col
        select-none transition-transform duration-100
        ${isDragging ? 'scale-110 active:scale-105' : 'scale-100'}
        hover:shadow-xl
      `}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          zIndex: 99,
        }}
        onMouseDown={e => {
          if (e.button !== 0) return; // 只响应左键
          handleStart(e.clientX, e.clientY);
        }}
        onTouchStart={e => {
          handleStart(e.touches[0].clientX, e.touches[0].clientY);
        }}
        onClick={() => {
          // 如果拖拽距离很小则视为点击
          if (!isDragging) {
            setPopVisible(true);
          }
        }}
        onDragStart={e => e.preventDefault()} // 禁止默认拖拽行为
      >
        <div
          className="w-[66px] h-[66px] rounded-full border border-[#E8DEC1] bg-white shadow-lg flex items-center justify-center"
          style={{
            width: `${size}px`,
            height: `${size}px`,
          }}
        >
          <img
            className="w-[36px] h-[36px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotel/orderDetail/call_icon.png"
            alt=""
          />
        </div>
        <p className="mt-[4px] text-[12px] text-[#66666E]">联系客服</p>
      </div>
      <ActionSheet
        visible={popVisible}
        title="联系客服"
        showCancel={false}
        onClose={() => setPopVisible(false)}
      >
        <div className="px-[16px] pb-[30px] flex flex-col items-center justify-center">
          <p className="text-[18px] text-[#000000] font-medium">021 62839771</p>
          <p className="text-[10px] text-[#99999E] mt-[4px]">客服电话</p>
          <div className="w-[343px] h-[1px] bg-[#F0F0F0] mt-[24px]" />
          <img
            className="w-[118px] h-[119px] mt-[24px]"
            src="https://ota-front-public.oss-cn-hangzhou.aliyuncs.com/web-app/hotel/orderDetail/ewmnew.jpeg"
            alt=""
          />
          <p className="text-[10px] text-[#99999E] mt-[8px]">
            识别二维码，添加翎游官方客服
          </p>
        </div>
      </ActionSheet>
    </div>
  );
};

export default FloatingBall;
